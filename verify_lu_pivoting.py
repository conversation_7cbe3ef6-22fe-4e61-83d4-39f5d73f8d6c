import numpy as np

def verify_lu_pivoting():
    # Original matrix A and vector b
    A = np.array([
        [2, -6, -1],
        [-3, -1, 7],
        [-8, 1, -2]
    ])
    
    b = np.array([-38, -34, -20])
    
    # Permutation matrices
    P1 = np.array([
        [0, 0, 1],
        [0, 1, 0],
        [1, 0, 0]
    ])
    
    P = np.array([
        [0, 0, 1],
        [1, 0, 0],
        [0, 1, 0]
    ])
    
    # L and U matrices from our calculation
    L = np.array([
        [1, 0, 0],
        [0.375, 1, 0],
        [-0.25, 0.239130, 1]
    ])
    
    U = np.array([
        [-8, 1, -2],
        [0, -5.75, -1.5],
        [0, 0, 8.108696]
    ])
    
    # Our solution
    x = np.array([4.0, 8.0, -2.0])
    
    print("LU Factorization with Partial Pivoting Verification")
    print("-" * 50)
    
    # Verify PA = LU
    PA = np.dot(P, A)
    LU = np.dot(L, U)
    
    print("\n1. Verifying PA = LU:")
    print("Maximum difference between PA and LU:", np.max(np.abs(PA - LU)))
    
    # Verify solution using numpy's solver
    x_numpy = np.linalg.solve(A, b)
    
    print("\n2. Solution Verification:")
    print("Our solution:     ", x)
    print("Numpy's solution: ", x_numpy)
    print("Maximum difference:", np.max(np.abs(x - x_numpy)))
    
    # Verify original equations
    print("\n3. Verifying Original Equations:")
    
    residuals = np.dot(A, x) - b
    print("Equation residuals (should be close to zero):")
    for i, res in enumerate(residuals, 1):
        print(f"Equation {i}: {res:e}")
    
    # Detailed verification of each equation
    print("\n4. Detailed Equation Check:")
    
    # Equation 1: 2x₁ - 6x₂ - x₃ = -38
    eq1 = 2*x[0] - 6*x[1] - x[2]
    print(f"Equation 1: 2({x[0]}) - 6({x[1]}) - ({x[2]}) = {eq1} (should be -38)")
    
    # Equation 2: -3x₁ - x₂ + 7x₃ = -34
    eq2 = -3*x[0] - x[1] + 7*x[2]
    print(f"Equation 2: -3({x[0]}) - ({x[1]}) + 7({x[2]}) = {eq2} (should be -34)")
    
    # Equation 3: -8x₁ + x₂ - 2x₃ = -20
    eq3 = -8*x[0] + x[1] - 2*x[2]
    print(f"Equation 3: -8({x[0]}) + ({x[1]}) - 2({x[2]}) = {eq3} (should be -20)")
    
    # Verify forward substitution
    print("\n5. Verifying Forward Substitution (Ly = Pb):")
    Pb = np.dot(P, b)
    y = np.dot(np.linalg.inv(L), Pb)
    print("y =", y)
    
    # Verify backward substitution
    print("\n6. Verifying Backward Substitution (Ux = y):")
    x_back = np.dot(np.linalg.inv(U), y)
    print("x =", x_back)
    print("Maximum difference from our solution:", np.max(np.abs(x - x_back)))

if __name__ == "__main__":
    verify_lu_pivoting()
