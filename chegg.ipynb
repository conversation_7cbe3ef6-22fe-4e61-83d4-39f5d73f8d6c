{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle 0.726184377413891$"], "text/plain": ["0.726184377413891"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import sympy as sp\n", "mu,L=sp.symbols('mu L')\n", "t,m1,m2,g=sp.symbols('t m1 m2 g')\n", "\n", "T=g*(m1+m2)/(sp.sin(t)+mu*sp.cos(t))\n", "T\n", "x=L/(2*m1*g)*(2*T*sp.sin(t)-m2*g)\n", "x.simplify()\n", "sp.sqrt(135/256)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{0.0018 y}{x} + \\frac{0.00036 \\sqrt{5} z}{x}$"], "text/plain": ["0.0018*y/x + 0.00036*sqrt(5)*z/x"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["import sympy as sp\n", "from math import *\n", "x,y,z=sp.symbols('x y z')\n", "def efield(q1,q2,x1,x2):\n", "    epsi=9*10**9\n", "    efield=2*epsi*(q1/x1+q2/x2)*10**-15/10**-2\n", "    return efield\n", "field="]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["124.65539999999997"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["3*1.38*10**-23*6.022*10**23*5"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0025000000000000005\n"]}, {"data": {"text/plain": ["-15.616"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["print(0.05**2)\n", "-21.491******"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5.427966101694915\n", "1.516119e-03\n", "1.869880e-03\n", "432.0\n"]}], "source": ["import sympy as sp\n", "x,y,z=sp.symbols('x y z')\n", "U=4*x**2*y**-3+6*x**3*y+4*x*y**2\n", "text=(str(sp.diff(U,z)))\n", "new=\"\"\n", "for x in range(0,len(text)):\n", "    if text[x]==\"*\":\n", "        if text[x+1]==\"*\":\n", "            new+=\"^\"\n", "            continue\n", "        else:\n", "            continue\n", "    new+=text[x]\n", "print((1.22*525*10**-9)/2.95*25*10**6)\n", "print(f'{505.373*(3*10**-6):e}')\n", "print(f'{505.373*(3.7*10**-6):e}')\n", "print(135/1.25**2*5)\n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.000000e+00\n", "-3.400000e+05\n", "0.000000e+00\n", "\n", "\n", "-0.000000e+00\n", "6.623200e-07\n", "-0.000000e+00\n", "0.25\n"]}], "source": ["import numpy as np\n", "velocity=10**5*np.array([6.8,0,0])\n", "r1=[0.5,0,0]\n", "r2=[0,0,0.5]\n", "def mag_field(x):\n", "    global velocity\n", "    mag=10**-7*(-4.87*10**-6*np.cross(velocity,x))/np.linalg.norm(x)**2\n", "    return mag\n", "value=mag_field(r2)\n", "for x in np.cross(velocity,r2):\n", "    print(f'{x:e}')\n", "print('\\n')\n", "for x in value:\n", "    print(f'{x:e}')\n", "from math import *\n", "print(np.linalg.norm(r1)**2)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.046687898089171974\n", "0.11147225435741147\n"]}, {"data": {"text/plain": ["<matplotlib.lines.Line2D at 0x19a2ad2ca70>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from math import *\n", "#print(degrees(asin((620*10**9)/((1/2.22*10**6)))))\n", "print(0.733*1/15.7)\n", "print(0.15*cos(15.7*0.04669))\n", "from matplotlib.pyplot import *\n", "import numpy as np\n", "def funn(x):\n", "    return(0.15*np.cos(15.7*x))\n", "xx=np.arange(0,2,0.001)\n", "plot(xx,funn(xx))\n", "axvline(x=0.04668,color='black')\n", "axhline(y=00.1114,color='red')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:25: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Total surfaces']=(2*x[\"Electrode count\"]+x['Neutral count'])*2\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:26: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Resistance']=x['Voltage']/x['Current']\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:25: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Total surfaces']=(2*x[\"Electrode count\"]+x['Neutral count'])*2\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:26: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Resistance']=x['Voltage']/x['Current']\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:25: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Total surfaces']=(2*x[\"Electrode count\"]+x['Neutral count'])*2\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:26: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Resistance']=x['Voltage']/x['Current']\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:28: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data22['Total surface area']=data22['Total surfaces']*new_area\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:29: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data11['Total surface area']=data11['Total surfaces']*old_area\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:30: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  data33['Total surface area']=data33['Total surfaces']*old_area\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:32: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Flow rate per unit area']=x['Flow rate']/x['Total surface area']\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:33: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x[\"Flow rate per power\"]=x['Flow rate']/x['Power']\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:32: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Flow rate per unit area']=x['Flow rate']/x['Total surface area']\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:33: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x[\"Flow rate per power\"]=x['Flow rate']/x['Power']\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:32: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x['Flow rate per unit area']=x['Flow rate']/x['Total surface area']\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_6360\\2341529375.py:33: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  x[\"Flow rate per power\"]=x['Flow rate']/x['Power']\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "data=pd.read_excel('hho data.xlsx')\n", "new_area=82.5\n", "old_area=(4*1800)/(m.pi*11**2)*7 \n", "\n", "def splitter(data,e_count):\n", "    j=0\n", "    init=0\n", "    for x in range(len(data)):\n", "        if data.iloc[x]['Electrode count']==e_count:\n", "            j+=1\n", "            if j==1:\n", "                init=x\n", "                j=x   \n", "    return init,j+1\n", "p,q=splitter(data,1)\n", "a,b=splitter(data,2)\n", "c,d=splitter(data,3)\n", "\n", "data11=data.iloc[0:q]\n", "data22=data.iloc[a:b]\n", "data33=data.iloc[c:d]\n", "datas=[data11,data22,data33]\n", "for x in datas:\n", "    x['Total surfaces']=(2*x[\"Electrode count\"]+x['Neutral count'])*2\n", "    x['Resistance']=x['Voltage']/x['Current']\n", "\n", "data22['Total surface area']=data22['Total surfaces']*new_area\n", "data11['Total surface area']=data11['Total surfaces']*old_area\n", "data33['Total surface area']=data33['Total surfaces']*old_area\n", "for x in datas:\n", "    x['Flow rate per unit area']=x['Flow rate']/x['Total surface area']\n", "    x[\"Flow rate per power\"]=x['Flow rate']/x['Power']\n", "from matplotlib.pyplot import *\n", "xxx=0\n", "colors=['red','blue','green']\n", "for x in datas:\n", "    #bar(x['Neutral count'],x['Flow rate per unit area'],width=1,label=x.iloc[1]['Electrode count'],alpha=0.6,color=colors[xxx])\n", "    \n", "    bar(x['Neutral count'],x['Flow rate per power'],width=1,label=x.iloc[1]['Electrode count'],alpha=0.6)\n", "    xxx+=1\n", "legend()\n", "finalhhodata=pd.concat([data11,data22,data33])\n", "finalhhodata.to_excel('Final hho data with area.xlsx')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["28.116702580494746\n", "84.96017585755774\n"]}], "source": ["from math import *\n", "print(sqrt(2.47**2+28.008**2))\n", "print(degrees(atan(28.008/2.47)))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.03661669690713075\n", "16.448027274433638\n", "0.4642854768132289\n", "2560.521119844522\n"]}], "source": ["from math import acos,degrees,radians,cos,sqrt,sin,atan,asin,tan\n", "a=1200\n", "b=1450\n", "theta=radians(30)\n", "def sinn(x):\n", "    return()\n", "print(degrees(atan(b*sin(theta))/(a+(b*cos(theta)))))\n", "print(degrees(atan((b*sin(theta))/(a+b*cos(theta)))))\n", "print(sqrt(1/2)/1.523)\n", "print(sqrt(1200**2+1450**2+(2*1200*1450*cos(radians(30)))))"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0718514914742627e-05"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["30+(30-23.855)\n", "from math import *\n", "def fun (x):\n", "    return 10*sin((2*pi*x)/60+pi/2)+8\n", "fun(23.855)\n", "#fun(36.144)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Max: kinetic energy (eV)')"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def freq(l):\n", "    h=6.63*10**-34*3*10**8\n", "    return (h/(l*10**-9))    \n", "freq(600)\n", "from matplotlib.pyplot import *\n", "from matplotlib import rcParams\n", "rcParams.update({\"font.family\":\"times new roman\"})\n", "plot([freq(430),freq(530),freq(600)],[876,569,356],marker='o')\n", "title('Curve between Max: kinetic energy and incoming photon frequency',weight='bold')\n", "xlabel(\"Frequency (Hz)\",weight='bold')\n", "ylabel(\"Max: kinetic energy (eV)\",weight='bold')"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6.003332407921454\n", "1.0309966518171998\n", "0.4587019511215249\n", "0.23947220877486014\n", "7.768317207102372\n", "-2.3470808790090567\n", "10.875847397990784\n"]}], "source": ["from math import *\n", "\n", "print(sqrt(3**2******2))\n", "\n", "print((12*sin(radians(22)))**2/(2*9.8))\n", "theta=radians(22)\n", "print((12*sin(theta))/9.8)\n", "print(sqrt((2*0.281)/9.8))\n", "print(12*cos(theta)*0.6982)\n", "print(12*sin(theta)-(9.8*0.6982))\n", "print(sqrt(-2.3470**2+(12*cos(theta))**2))"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["G*M*y/(x**2 + y**2 + z**2)**(3/2)\n", "4.725666e+08\n", "4.695092e+08\n"]}], "source": ["import sympy as sp\n", "\n", "x,y,z=sp.symbols('x y z')\n", "G,M=sp.symbols('G M',constant=True)\n", "r=sp.sqrt(x**2+y**2+z**2)\n", "Phi=-(G*M)/r\n", "print(sp.diff(Phi,y))\n", "from math import sqrt\n", "print(f'{(6.67*10**-11*6*10**24)/(4000**2+4000**2+3000**2)**(3/2)*(4000*20+4000*50+3000*10):e}')\n", "def gravity(x,y,z):\n", "    g=-(6.67*10**-11*6*10**24)/sqrt(x**2+y**2+z**2)\n", "    return g\n", "print(f\"{gravity(4020,4050,3010)-gravity(4000,4000,3000):e}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-979.651063829787*i - 1.48390070921986\n", "-1.51472372562817e-3\n", "3.334420e-03\n"]}], "source": ["import sympy as sp\n", "i=sp.symbols('i')\n", "eqn=1.55-879*1/423*(1.46+32.7*i)-911.7*i\n", "sp.solve(eqn)\n", "print(eqn)\n", "print(f'{sp.solve(eqn)[0]:e}')\n", "print(f'{1/423*(1.46+32.7*(-1.515*10**-3)):e}')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8.790224659524841e-05\n", "101594.90355680179\n"]}], "source": ["vf=1400\n", "def acc(x):\n", "    g=9.8\n", "    R=6.37*10**6\n", "    acc=-g*R**2/(R+x)**2\n", "    return acc\n", "xi=0\n", "dt=10**-5\n", "while vf>0:\n", "    xf=xi+vf*dt\n", "    vi=vf\n", "    vf=vi+acc(xf)*dt\n", "    xi=xf\n", "print(vi)\n", "print(xi)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.8660254037844387\n", "0.8660254037844386\n", "53.13010235415598\n", "45.98079720050543\n", "-49.99999999999998\n", "-86.60254037844388\n", "0.49516421899999996\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["\n", "from math import sqrt,pi,atan,degrees,sin, cos,radians,degrees\n", "print(cos(pi/6))\n", "print(sqrt(3)/2)\n", "print(degrees(atan(40/30)))\n", "sqrt(30**2)\n", "print(50*cos(radians(23.130)))\n", "print(100*cos(radians(-120)))\n", "print(100*sin(radians(-120)))\n", "print(0.192*(1.53)**2+0.299*(0.391)**2)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[4 5 0]\n", "[  0.   0. -35.]\n", "[7 0 0]\n", "[   0.   -0. -140.]\n", "[-4  6  0]\n", "[  0.           0.         100.94811367]\n", "[[  0.           0.         -74.05188633]]\n", "\n", "\n", "[-4  1  0]\n", "[ 0.  0. -7.]\n", "[-1 -4  0]\n", "[ 0.  0. 20.]\n", "[-12   2   0]\n", "[  0.           0.         114.98213378]\n", "[[  0.           0.         127.98213378]]\n"]}], "source": ["from math import *\n", "import numpy as np\n", "def fvec(r,theta):\n", "    fx=r*cos(radians(theta))\n", "    fy=r*sin(radians(theta))\n", "    fz=0\n", "    array=np.array([fx,fy,fz])\n", "    return array\n", "fvec1=fvec(7,0)\n", "fvec2=fvec(20,270)\n", "fvec3=fvec(14,213)\n", "pos1=np.array([2,1,0])\n", "pos2=np.array([5,-4,0])\n", "pos3=np.array([-6,2,0])\n", "forces=[fvec1,fvec2,fvec3]\n", "poses=[pos1,pos2,pos3]\n", "a=np.array([-2,-4,0])\n", "b=np.array([6,0,0])\n", "\n", "def torque_calc(pos):\n", "    tau=np.zeros([1,3])\n", "    for x in range(len(forces)):\n", "        f=np.cross(-(pos-poses[x]),forces[x])\n", "        print(-(pos-poses[x]))\n", "        tau=tau+f\n", "        print(f)\n", "    return tau\n", "print(torque_calc(a))\n", "print('\\n')\n", "print(torque_calc(b))\n", "#print(fvec(14,213))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-1.0*k/d**2 -1.73205080756888*k/d**2]\n", "\t\n", "[1.5*k/d**2 -2.59807621135332*k/d**2]\n", "\t\n", "[-1.0*k/d**2 1.22464679914735e-16*k/d**2]\n", "\t\n", "[0.5*k/d**2 0.866025403784438*k/d**2]\n", "\t\n", "[2.5*k/d**2 -4.3301270189222*k/d**2]\n", "\t\n", "[2.0*k/d**2 -4.89858719658941e-16*k/d**2]\n", "\t\n", "1.7320508075688772\n", "[[4.5*k/d**2 -7.79422863405995*k/d**2]]\n"]}, {"data": {"text/plain": ["7.500000000000001e-09"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "import sympy as sp\n", "from math import radians\n", "d,k=sp.symbols('d k')\n", "charges=np.array([-2,-3,1,-1,5,2])\n", "positions=np.array([1,2,3,4,5,6])\n", "t=pi/3\n", "force=np.zeros((1,2))\n", "def eforce(q1,q2,r,theta):\n", "    fx=(k*q1*q2)/r**2*np.cos(theta)\n", "    fy=(k*q1*q2)/r**2*np.sin(theta)\n", "    return np.array([fx,fy])\n", "for x in range(len(charges)):\n", "    force=force+eforce(1,charges[x],d,theta=t*positions[x])\n", "    print(eforce(1,charges[x],d,theta=t*positions[x]))\n", "    print('\\t')\n", "print(sqrt(3))\n", "print(force)\n", "np.sum(charges)\n", "6/8*10**-8"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.4285714285714286e-06\n", "5.0004161999999994e-05\n", "1.6197749e-20\n", "0.48444444444444446\n"]}], "source": ["from math import *\n", "def dely_calc(m,wl):\n", "    L=2.9\n", "    d=1/700*10**-3\n", "    print(d)\n", "    y=L*tan(asin((m*wl*10**-9)/d))\n", "    return y\n", "dely_calc(1,700)\n", "print(69*10**-6*309.7*2.34*10**-3)\n", "print(1172.9*1.381*10**-23)\n", "print(9.81/4.5**2)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.93480220054468\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import sympy as sp\n", "\n", "x=sp.symbols('x')\n", "a,b=sp.symbols('a b',constant=True)\n", "def fun(k):\n", "    dv=np.pi*sp.sin(k*x)**2\n", "    value=sp.integrate(dv,(x,0,np.pi))\n", "    return value\n", "k=np.arange(0,10,0.1)\n", "kk=np.arange(0,10,0.5)\n", "xx=[]\n", "yy=[]\n", "for j in k:\n", "    xx.append(j)\n", "    yy.append(fun(j))\n", "plt.plot(xx,yy)\n", "for m in kk:\n", "    plt.scatter(m,fun(m),c='red')\n", "\n", "plt.xlabel('K')\n", "plt.ylabel('V')\n", "plt.savefig('chegg.png')\n", "print(fun(0.5))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.772458e-11\n", "7.105877e+11\n", "3.901729e-23\n", "3.552950e+05\n", "8.485281e+00\n"]}], "source": ["\n", "def printer(x):\n", " \n", "    print(f\"{x:e}\")\n", "from math import *\n", "printer(173.2786*1.6*10**-19*10**6)\n", "printer(1/(3.9017*10**-23)*2.7725*10**-11)\n", "printer(235.04392*1.66*10**-25)\n", "printer(7.1059*10**13/(2*10**8))\n", "printer(12*sin((5*pi*0.35)+pi/2))"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.730474e+05\n", "3.460948e+05\n", "1.730474e+05\n"]}], "source": ["printer((799*9.8*44.2)/2)\n", "printer((799*9.8*44.2))\n", "printer(799*9.8*44.2/2)"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[25. 12. 38.]\n", "90.0\n", "49.751\n"]}], "source": ["import numpy as np\n", "a=np.array([25,-30,15])*10**-2\n", "b=np.array([-10,8,12])*10**-2\n", "o=np.array([0,0,0])\n", "p=np.array([15,20,50])*10**-2\n", "qa=-0.3*10**-6\n", "qb=0.5*10**-6\n", "ao=o-a\n", "bo=o-b\n", "ap=p-a\n", "bp=p-b\n", "def efield(q,a):\n", "    r=np.linalg.norm(a)\n", "    k=9*10**9\n", "    e=k*q/r**3*a\n", "    print('eda distance value ithaane, ',r*100)\n", "    return e\n", "print(bp*100)\n", "\n", "\n", "\n", "\n", "print(9*10**9*2*10**-9/(20*10**-2))\n", "print((90-40.249))"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eda distance value ithaane,  20.0\n", "449.99999999999994\n", "1,1,1,1,1,1,1,1,1,1,1,1,1,\n", "0,1,2,3,4,5,6,7,8,9,10,11,12,\n", "0,1,4,9,16,25,36,49,64,81,100,121,144,\n", "0,1,8,27,64,125,216,343,512,729,1000,1331,1728,\n"]}, {"ename": "NameError", "evalue": "name 'beta_0' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[59], line 11\u001b[0m\n\u001b[0;32m      9\u001b[0m         text\u001b[38;5;241m=\u001b[39mtext\u001b[38;5;241m+\u001b[39m\u001b[38;5;28mstr\u001b[39m(x\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39my)\u001b[38;5;241m+\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m,\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     10\u001b[0m     \u001b[38;5;28mprint\u001b[39m(text)\n\u001b[1;32m---> 11\u001b[0m ((\u001b[38;5;241m13\u001b[39m,\u001b[38;5;241m78\u001b[39m,\u001b[38;5;241m650\u001b[39m,\u001b[38;5;241m5832\u001b[39m),(\u001b[38;5;241m28\u001b[39m,\u001b[38;5;241m650\u001b[39m,\u001b[38;5;241m5832\u001b[39m,\u001b[38;5;241m58254\u001b[39m),(\u001b[38;5;241m650\u001b[39m,\u001b[38;5;241m5832\u001b[39m,\u001b[38;5;241m38254\u001b[39m,\u001b[38;5;241m606528\u001b[39m),(\u001b[38;5;241m5832\u001b[39m,\u001b[38;5;241m58254\u001b[39m,\u001b[38;5;241m606528\u001b[39m,\u001b[38;5;241m6589252\u001b[39m))\u001b[38;5;241m*\u001b[39m((\u001b[43mbeta_0\u001b[49m),(beta_1),(beta_2),(beta_3))\n", "\u001b[1;31mNameError\u001b[0m: name 'beta_0' is not defined"]}], "source": ["print(efield(2*10**-9,20*10**-2))\n", "sin(atan(2))\n", "sqrt(281.75**2+80.549**2)\n", "degrees(atan(80.549/281.75))\n", "dd=[]\n", "for y in range(0,4):\n", "    text=''\n", "    for x in range (0,13):\n", "        text=text+str(x**y)+','\n", "    print(text)\n", "((13,78,650,5832),(28,650,5832,58254),(650,5832,38254,606528),(5832,58254,606528,6589252))*((beta_0),(beta_1),(beta_2),(beta_3))"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 8.8 29.9 62.0 104.7 159.1 222.0 294.5 380.4 471.1 571.7 686.8 809.2\n", "38.0556\n", "53.03655\n"]}, {"data": {"text/plain": ["0.0822"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["x=\"((0),(8.8),(29.9),(62.0),(104.7),(159.1),(222.0),(294.5),(380.4),(471.1),(571.7),(686.8),(809.2))\"\n", "text=\"\"\n", "for y in x:\n", "    if y=='(' or y==')':\n", "        continue\n", "    elif y==',':\n", "        text=text+' '\n", "    else:\n", "        text=text+y\n", "print(text)\n", "print(4.7025+11.1108*4.5-0.822*4.5**2)\n", "def value(x):\n", "    value=(4.7025)+(11.1108*4.5)-(3*0.0274*4.5**2)\n", "    return value\n", "print(value(4.5))\n", "3*0.0274\n"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5.1296+10.315+21.071+-14.352+\n", "27.752<PERSON><PERSON>+15.445hatj\n", "sqrt(27.752^2+15.445^2)\n", "tan^-1(15.445/27.752)\n", "31.760509560287016\n", "29.096666860187764\n"]}], "source": ["from math import *\n", "vecx=[]\n", "vecy=[]\n", "def vec(r,theta):\n", "    global vecx, vecy\n", "    theta=radians(theta)\n", "    x=r*cos(theta)\n", "    y=r*sin(theta)\n", "    vecx.append(x)\n", "    vecy.append(y)\n", "rr=[23.7,11.3,40.1,15]\n", "theeetta=[12.5,65.9,148.3,286.9]\n", "\n", "for x in range (len(rr)):\n", "    vec(rr[x],theeetta[x])\n", "text=''\n", "for x in vecy:\n", "    text+=f'{x:.5}+'\n", "print(text)\n", "def resultant(*x):\n", "    sumx=0\n", "    sumy=0\n", "    for i in range(len(x)):\n", "        sumx+=vecx[x[i]-1]\n", "        sumy+=vecy[x[i]-1]\n", "    print(f'{sumx:.5}hati+{sumy:.5}hatj')\n", "    print(f'sqrt({sumx:.5}^2+{sumy:.5}^2)')\n", "    print(f'tan^-1({sumy:.5}/{sumx:.5})')\n", "    print(sqrt(sumx**2+sumy**2))\n", "    print(degrees(atan(sumy/sumx)))\n", "    \n", "    pass\n", "resultant(1,2)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.495480e+07 \n", "\n", "-2.832153e+01\n", "-3.236746e+01\n", "-0.000000e+00\n"]}, {"data": {"text/plain": ["10.63014581273465"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from math import *\n", "import numpy as np\n", "def electric_field(q,x):\n", "    epsi=9*10**9\n", "    print(f'{epsi*q/np.linalg.norm(x)**3:e} \\n')\n", "    return epsi*q/np.linalg.norm(x)**3*x\n", "for x in electric_field(6*10**-6,np.array([7,8,0])*10**-2):\n", "    print(f'{-9*10**-6*x:e}')\n", "sqrt(7**2+8**2)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.164258e+03\n", "1.426962e+09\n", "1.997247e+09\n", "96352.0\n"]}], "source": ["from math import *\n", "print(f'{sqrt(2*(1/2*(1388.889)**2+(6.67*10**-11*1.71*10**23)*(1/(1800*10**3)-1/(2300*10**3)))):e}')\n", "print(f'{-1/2*500*(1388.889)**2+(6.67*10**-11*1.71*10**23*500)*(1/(2300*10**3)-1/(2*5000*10**3)):e}')\n", "print(f'{-1/2*500*(1388.889)**2+(6.67*10**-11*1.71*10**23*500)/(2300*10**3):e}')\n", "print(f'{6.022*10**23*1.6*10**-19}')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error fetching URL https://www.sciencedirect.com/science/article/pii/S092534671830301X: 400 Client Error: Bad Request for url: https://www.sciencedirect.com/unsupported_browser\n", "Title: None\n", "DOI: None\n", "\n"]}], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "\n", "def get_paper_info(url):\n", "    headers = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36\",\n", "        \"Accept-Language\": \"en-US,en;q=0.9\",\n", "        \"Accept-Encoding\": \"gzip, deflate, br\",\n", "        \"Accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\",\n", "        \"Connection\": \"keep-alive\",\n", "    }\n", "\n", "    try:\n", "        response = requests.get(url, headers=headers)\n", "        response.raise_for_status()  # Check for HTTP request errors\n", "\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "\n", "        # Find the paper title and DOI using the appropriate HTML tags or attributes\n", "        title_tag = soup.find('span', class_='title-text')\n", "        title = title_tag.get_text(strip=True) if title_tag else 'Title not found'\n", "\n", "        doi_tag = soup.find('a', {'class': 'doi'})\n", "        doi = doi_tag['href'] if doi_tag else 'DOI not found'\n", "\n", "        return title, doi\n", "\n", "    except requests.RequestException as e:\n", "        print(f\"Error fetching URL {url}: {e}\")\n", "        return None, None\n", "\n", "# List of URLs to scrape\n", "urls = [\n", "    \"https://www.sciencedirect.com/science/article/pii/S092534671830301X\",\n", "    # Add more URLs as needed\n", "]\n", "\n", "# Scrape each URL and print the results\n", "for url in urls:\n", "    title, doi = get_paper_info(url)\n", "    print(f\"Title: {title}\\nDOI: {doi}\\n\")\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.pyplot import rcParams\n", "import numpy as np\n", "rcParams[\"font.family\"]='Times new roman'\n", "rcParams['xtick.major.size']=0\n", "rcParams['ytick.major.size']=0"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['0$\\\\tau$', '1$\\\\tau$', '2$\\\\tau$', '3$\\\\tau$', '4$\\\\tau$', '5$\\\\tau$']\n"]}], "source": ["x=np.arange(0,5*200,0.1)\n", "xpos=np.linspace(0,1000,6)\n", "voltage=1\n", "cap=20\n", "resist=10\n", "def funn(xx):\n", "    charge=voltage*cap*(1-np.exp(-xx/(resist*cap)))\n", "    return charge\n", "def curr(xx):\n", "    curr=voltage/resist*(1-np.exp(-xx/(resist*cap)))\n", "    return curr\n", "tau=r'$\\tau$'\n", "xlabel=[str(i)+tau for i in range (0,6)]\n", "print(xlabel)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.038812499999999986\n", "11594.202898550728\n"]}], "source": ["from math import *\n", "print(3/2*1.38*10**-23*300/(1.6*10**-19))\n", "print(1.6*10**-19/(1.38*10**-23))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-1.8888888888888888\n"]}], "source": ["import sympy as sp\n", "t=sp.symbols('t')\n", "barnu=200*100\n", "h=6.63*10**-34\n", "kb=1.38*10**-23\n", "c=3*10**8\n", "ut=3/2*kb*t\n", "ew=h*c*barnu\n", "sp.solve(ut-ew)\n", "def energy(x,y):\n", "    print(-13.6*(1/x**2-1/y**2))\n", "energy(2,3)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-8\n", "70.00000000000001\n", "-4.25\n", "-10.0\n"]}], "source": ["from math import *\n", "import sympy as sp\n", "t=sp.symbols('t')\n", "x=2*t**3-8*t**2+11.75\n", "\n", "vel=sp.lambdify(t, sp.diff(x,t))\n", "print(vel(2))\n", "acc=sp.lambdify(t,sp.diff(sp.diff(x,t)))\n", "print(12.5*acc(1.8))\n", "pos=sp.lambdify(t,x)\n", "print(pos(2))\n", "print(pos(2)-pos(1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["v=sp.diff(x,t)\n", "print(sp.diff(v,t))\n", "def text(x):\n", "    txt=''\n", "    for i in range(len(x)):\n", "        if x[i]=='*':\n", "            if x[i+1]=='*':\n", "                txt+='^'\n", "                continue\n", "            else: \n", "                continue\n", "        txt+=x[i]\n", "    print(txt)\n", "print()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["11.530464400000001\n"]}], "source": ["print(1/2*9.8*1.534**2)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}