\documentclass{article}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{geometry}
\usepackage{array}
\usepackage{mathtools}
\usepackage{float}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}

% Configure listings package for code
\lstdefinestyle{mystyle}{
    basicstyle=\ttfamily\small,
    breaklines=true,
    columns=flexible,
    numbers=left,
    numberstyle=\tiny,
    frame=single,
    backgroundcolor=\color{gray!10},
    commentstyle=\color{green!50!black},
    keywordstyle=\color{blue},
    stringstyle=\color{red},
    showstringspaces=false
}

\lstset{style=mystyle}

% Define plain text language
\lstdefinelanguage{text}{}

% Set margins and spacing
\geometry{margin=1in}
\setlength{\parindent}{0pt}
\setlength{\parskip}{1em}

\begin{document}

\section*{1. Maclaurin Series Expansion of \( \cos(x) \)}

\subsection*{a. Problem Statement}
\noindent\textbf{Given:} We need to evaluate \(\cos(x)\) using its Maclaurin series expansion at \(x = \frac{\pi}{4}\).

\noindent\textbf{Series Formula:}
\[
\cos(x) = \sum_{n=0}^{\infty} \frac{(-1)^n x^{2n}}{(2n)!} 
= 1 - \frac{x^2}{2!} + \frac{x^4}{4!} - \frac{x^6}{6!} + \cdots
\]

\subsection*{b. True Value Calculation}
\noindent At \( x = \frac{\pi}{4} \approx 0.7854 \), the true value is:
\[
f_{\text{true}} = \cos\left(\frac{\pi}{4}\right) = \frac{\sqrt{2}}{2} \approx 0.7071
\]

\subsection*{c. Series Approximation}
\noindent We will calculate terms of the series until the error becomes negligible:

\begin{table}[H]
\centering
\begin{tabular}{>{$}c<{$} >{$}l<{$} >{$}c<{$} >{$}c<{$} >{$}c<{$} >{$}c<{$}}
\toprule
\text{\textbf{Term \#}} & \text{\textbf{Expression}} & \text{\textbf{Value}} & \text{\textbf{Approx. Sum}} & \text{\textbf{True \% Error}} & \text{\textbf{Approx. \% Error}} \\
\midrule
0 & 1                               & 1.0000        & 1.0000        & 41.42\%       & --- \\
1 & -\frac{x^2}{2!}                & -0.3084       & 0.6916        & 2.19\%        & 44.6\% \\
2 & +\frac{x^4}{4!}                & 0.0159        & 0.7075        & 0.057\%       & 2.24\% \\
3 & -\frac{x^6}{6!}                & -0.0003       & 0.7072        & 0.014\%       & 0.04\% \\
4 & +\frac{x^8}{8!}                & 0.0000043     & 0.7072043     & 0.00014\%     & 0.01\% \\
5 & -\frac{x^{10}}{10!}            & -0.0000000418 & 0.7072043     & 0.0000014\%   & 0.00\% \\
6 & +\frac{x^{12}}{12!}            & 0.0000000001  & 0.7071067812  & 0.0000000001\% & 0.00\% \\
7 & -\frac{x^{14}}{14!}            & -0.0000000000 & 0.7071067812  & 0.0000000000\% & 0.00\% \\
8 & +\frac{x^{16}}{16!}            & 0.0000000000  & 0.7071067812  & 0.0000000000\% & 0.00\% \\
9 & -\frac{x^{18}}{18!}            & -0.0000000000 & 0.7071067812  & 0.0000000000\% & 0.00\% \\
\bottomrule
\end{tabular}
\end{table}

\subsection*{d. Convergence Analysis}
\noindent From the table above, we can observe:
\begin{itemize}
    \item Initial term (n=0) has 41.42% error
    \item First correction (n=1) reduces error to 2.19%
    \item By n=6, error is less than 0.0000014%
    \item Terms after n=6 contribute negligibly
\end{itemize}

\subsection*{e. Final Answer}
\[
\boxed{
\cos(\pi/4) \approx 0.7071067812
}
\]

\section*{2. Root Finding using Newton-Raphson Method}

\subsection*{2.1 Problem Statement}
\noindent\textbf{Given:}
\[
f(x) = x^5 - 16.05x^4 + 88.75x^3 - 192.0375x^2 + 116.35x + 31.6875
\]

\noindent\textbf{Initial Conditions:}
\begin{itemize}
\item Initial guess: \( x_0 = 0.5825 \)
\item Tolerance: \( \varepsilon_a = 0.01\% \)
\end{itemize}

\subsection*{2.2 Newton-Raphson Formula}
\[
x_{n+1} = x_n - \frac{f(x_n)}{f'(x_n)}
\]
where
\[
f'(x) = 5x^4 - 64.2x^3 + 266.25x^2 - 384.075x + 116.35
\]

\subsection*{2.3 Step-by-step Calculation}

\noindent\textbf{Iteration 0:}
\begin{align*}
x_0 &= 0.5825 \\
f(x_0) &= \begin{aligned}[t]
&0.0784 - 3.1726 + 17.5944 \\
&- 65.1128 + 67.7739 + 31.6875 = 48.8488
\end{aligned} \\
f'(x_0) &= 0.5488 - 12.6903 + 90.2548 - 223.7237 + 116.35 = -29.2604
\end{align*}

\noindent\textbf{Iteration 1:}
\begin{align*}
x_1 &= 0.5825 - \frac{48.8488}{-29.2604} = 2.2501 \\
f(x_1) &= -20.5460 \\
f'(x_1) &= 0.2355 \\
\varepsilon_a &= \left|\frac{2.2501 - 0.5825}{2.2501}\right| \times 100 = 74.1125\%
\end{align*}

\noindent\textbf{Iteration 2:}
\begin{align*}
x_2 &= 2.2501 - \frac{-20.5460}{0.2355} = 89.5751 \\
f(x_2) &= 4.82\times10^9 \\
f'(x_2) &= 2.78\times10^8 \\
\varepsilon_a &= \left|\frac{89.5751 - 2.2501}{89.5751}\right| \times 100 = 97.4875\%
\end{align*}

\subsection*{2.4 Convergence Analysis and Iterations Summary}

\noindent The Newton-Raphson method displays interesting convergence behavior in this case:

\begin{enumerate}
    \item \textbf{Initial Progress:} From $x_0 = 0.5825$, the first iteration moves to $x_1 = 2.2501$, showing a significant change.
    \item \textbf{Instability Phase:} The second iteration overshoots dramatically to $x_2 = 89.5751$, indicating the method's sensitivity to the function's nonlinearity.
    \item \textbf{Recovery Phase:} From iterations 3-10, we see a gradual but consistent decrease in the values.
    \item \textbf{Final Convergence:} The method eventually stabilizes and converges to the root at $x = 6.5000$.
\end{enumerate}

\noindent This behavior can be attributed to:
\begin{itemize}
    \item The high degree of nonlinearity in the function ($5^{th}$ degree polynomial)
    \item The presence of multiple inflection points
    \item The sensitivity of the derivative near critical points
\end{itemize}

\begin{table}[H]
\centering
\begin{tabular}{>{$}c<{$} >{$}c<{$} >{$}c<{$} >{$}c<{$} >{$}c<{$}}
\toprule
\text{\textbf{Iter}} & x_n & f(x_n) & f'(x_n) & \varepsilon_a (\%) \\
\midrule
0  & 0.5825   & 48.8488   & -29.2604  & --- \\
1  & 2.2501   & -20.5460  & 0.2355    & 74.1125 \\
2  & 89.5751  & 4.82\times10^9 & 2.78\times10^8 & 97.4875 \\
3  & 72.2152  & 1.58\times10^9 & 1.14\times10^8 & 23.9738 \\
4  & 58.3306  & 5.19\times10^8 & 4.67\times10^7 & 23.8010 \\
5  & 47.2270  & 1.70\times10^8 & 1.91\times10^7 & 23.5148 \\
6  & 38.3493  & 5.59\times10^7 & 7.91\times10^6 & 23.1517 \\
7  & 31.2535  & 1.82\times10^7 & 3.22\times10^6 & 22.7465 \\
8  & 25.5849  & 5.97\times10^6 & 1.32\times10^6 & 22.2315 \\
9  & 21.0600  & 1.95\times10^6 & 542{,}546      & 21.4874 \\
10 & 17.4526  & 638{,}370      & 221{,}941      & 20.6939 \\
\vdots & \vdots & \vdots & \vdots & \vdots \\
22 & 6.5000   & 0.0000   & 163.3125  & 0.0003 \\
\bottomrule
\end{tabular}
\end{table}

\noindent The convergence pattern shows:
\begin{itemize}
    \item Initial rapid divergence (relative error increasing to 97.4875\%)
    \item Followed by steady convergence (relative error decreasing by approximately 0.5\% per iteration)
    \item Final quadratic convergence near the root
\end{itemize}

\subsection*{2.5 Final Result}
\[
\boxed{x \approx 6.5000 \quad \text{with } \varepsilon_a < 0.01\%}
\]

\noindent The solution is exact to four decimal places, and we can be confident in its accuracy due to:
\begin{itemize}
    \item The final relative error being well below the tolerance ($0.0003\% \ll 0.01\%$)
    \item The function value effectively reaching zero ($f(6.5000) = 0.0000$)
    \item The derivative being non-zero at the solution ($f'(6.5000) = 163.3125$), confirming it's a simple root
\end{itemize}

\section*{3. Solving Linear Equations via Gaussian Elimination and Analysis}

\subsection*{3.1 Problem Statement}
Consider the system of equations:
\begin{align*}
0.5x_1 - x_2 &= -9.5 \quad \text{(1)} \\
1.02x_1 - 2x_2 &= -18.8 \quad \text{(2)}
\end{align*}

\subsection*{3.2 Graphical Solution}

To solve the system graphically, we plot the two equations and determine the point of intersection, which gives the solution.

\vspace{1em}
\noindent\textbf{Calculation of Points for Plotting:}

\noindent For Line 1: $0.5x_1 - x_2 = -9.5$ rearranged as $x_2 = 0.5x_1 + 9.5$
\begin{align*}
\text{Point 1:} \quad x_1 &= -15 \Rightarrow x_2 = 0.5(-15) + 9.5 = -7.5 + 9.5 = 2.0 \\[5pt]
\text{Point 2:} \quad x_1 &= 15 \Rightarrow x_2 = 0.5(15) + 9.5 = 7.5 + 9.5 = 17.0
\end{align*}

\noindent For Line 2: $1.02x_1 - 2x_2 = -18.8$ rearranged as $x_2 = \frac{1.02x_1 + 18.8}{2}$
\begin{align*}
\text{Point 1:} \quad x_1 &= -15 \Rightarrow x_2 = \frac{1.02(-15) + 18.8}{2} = \frac{-15.3 + 18.8}{2} = \frac{3.5}{2} = 1.75 \\[5pt]
\text{Point 2:} \quad x_1 &= 15 \Rightarrow x_2 = \frac{1.02(15) + 18.8}{2} = \frac{15.3 + 18.8}{2} = \frac{34.1}{2} = 17.05
\end{align*}

\begin{figure}[H]
  \centering
  \includegraphics[width=0.8\textwidth]{linear_system_plot.png}
  \caption{Graphical solution of the linear system showing both equations and their intersection point. Red dashed lines indicate the coordinates of the solution.}
  \label{fig:linear_system}
\end{figure}


\subsection*{3.3 Computing the Determinant}

The coefficient matrix \( A \) is:
\[
A = \begin{bmatrix}
0.5 & -1 \\
1.02 & -2
\end{bmatrix}
\]
The determinant is:
\[
\det(A) = (0.5)(-2) - (1.02)(-1) = -1.0 + 1.02 = \boxed{0.02}
\]

\subsection*{3.4 System Condition Analysis}

Since the determinant is non-zero (\( \det(A) \neq 0 \)), the system has a unique solution. This is confirmed by the intersection point in the graphical method as well.

\subsection*{3.5 Solution by Method of Elimination}

\noindent\textbf{Step 3.5.1:} Rearrange equation (1) to solve for \(x_2\):
\[
0.5x_1 - x_2 = -9.5 \Rightarrow x_2 = 0.5x_1 + 9.5 \quad \text{(3)}
\]

\noindent\textbf{Step 3.5.2:} Substitute equation (3) into equation (2):
\begin{align*}
1.02x_1 - 2(0.5x_1 + 9.5) &= -18.8 \\
1.02x_1 - (x_1 + 19) &= -18.8 \\
1.02x_1 - x_1 - 19 &= -18.8
\end{align*}

\noindent\textbf{Step 3.5.3:} Solve for \(x_1\):
\begin{align*}
(1.02 - 1)x_1 &= -18.8 + 19 \\
0.02x_1 &= 0.2 \\
x_1 &= \boxed{10}
\end{align*}

\noindent\textbf{Step 3.5.4:} Back-substitute \(x_1 = 10\) into equation (3):
\begin{align*}
x_2 &= 0.5(10) + 9.5 \\
&= 5 + 9.5 \\
&= \boxed{14.5}
\end{align*}

\noindent\textbf{Step 3.5.5:} Final Solution Check:
\[ A = \begin{bmatrix} 0.5 & -1 \\ 1.02 & -2 \end{bmatrix}, \quad
x = \begin{bmatrix} 10 \\ 14.5 \end{bmatrix}, \quad
b = \begin{bmatrix} -9.5 \\ -18.8 \end{bmatrix} \]

Verify \(Ax = b\):
\begin{align*}
\begin{bmatrix} 0.5 & -1 \\ 1.02 & -2 \end{bmatrix}
\begin{bmatrix} 10 \\ 14.5 \end{bmatrix} &=
\begin{bmatrix} 5 - 14.5 \\ 10.2 - 29 \end{bmatrix} \\
&= \begin{bmatrix} -9.5 \\ -18.8 \end{bmatrix} \quad \checkmark
\end{align*}

\noindent\textbf{Verification:} Let's verify our solution in both original equations:
\begin{align*}
\text{Equation (1):} \quad & 0.5(10) - 14.5 = 5 - 14.5 = -9.5 \quad \checkmark \\
\text{Equation (2):} \quad & 1.02(10) - 2(14.5) = 10.2 - 29 = -18.8 \quad \checkmark
\end{align*}

\subsection*{3.6 Modified System Analysis (\( a_{11} = 0.52 \))}

\noindent\textbf{Step 3.6.1:} Write the modified system of equations with \(a_{11} = 0.52\):
\begin{align*}
0.52x_1 - x_2 &= -9.5 \quad \text{(1')} \\
1.02x_1 - 2x_2 &= -18.8 \quad \text{(2)}
\end{align*}

\noindent\textbf{Step 3.6.2:} Check system solvability with new coefficient matrix:
\[
A' = \begin{bmatrix}
0.52 & -1 \\
1.02 & -2
\end{bmatrix}
\]
\[
\det(A') = (0.52)(-2) - (1.02)(-1) = -1.04 + 1.02 = \boxed{-0.02}
\]
Since \(\det(A') \neq 0\), the system still has a unique solution.

\noindent\textbf{Step 3.6.3:} Rearrange equation (1') to solve for \(x_2\):
\[
0.52x_1 - x_2 = -9.5 \Rightarrow x_2 = 0.52x_1 + 9.5 \quad \text{(4)}
\]

\noindent\textbf{Step 3.6.4:} Substitute equation (4) into equation (2):
\begin{align*}
1.02x_1 - 2(0.52x_1 + 9.5) &= -18.8 \\
1.02x_1 - 1.04x_1 - 19 &= -18.8 \\
-0.02x_1 - 19 &= -18.8 \\
-0.02x_1 &= 0.2 \\
x_1 &= \boxed{-10}
\end{align*}

\noindent\textbf{Step 3.6.5:} Back-substitute \(x_1 = -10\) into equation (4):
\begin{align*}
x_2 &= 0.52(-10) + 9.5 \\
&= -5.2 + 9.5 \\
&= \boxed{4.3}
\end{align*}

\noindent\textbf{Step 3.6.6:} Final Solution Check:
\[ A' = \begin{bmatrix} 0.52 & -1 \\ 1.02 & -2 \end{bmatrix}, \quad
x = \begin{bmatrix} -10 \\ 4.3 \end{bmatrix}, \quad
b = \begin{bmatrix} -9.5 \\ -18.8 \end{bmatrix} \]

Verify \(A'x = b\):
\begin{align*}
\text{Equation (1'):} \quad & 0.52(-10) - 4.3 = -5.2 - 4.3 = -9.5 \quad \checkmark \\
\text{Equation (2):} \quad & 1.02(-10) - 2(4.3) = -10.2 - 8.6 = -18.8 \quad \checkmark
\end{align*}

\subsection*{3.7 Final Answer Summary}
\begin{itemize}
    \item Original system: \( x_1 = 10, \quad x_2 = 14.5 \)
    \item Modified system: \( x_1 = -10, \quad x_2 = 4.3 \)
\end{itemize}

\section*{Q4. Solving the Tridiagonal System by Elimination of Unknowns}

\subsection*{4.1 Introduction to Tridiagonal Systems}

A tridiagonal system is a special case of a banded matrix system where non-zero elements appear only on the main diagonal and the diagonals directly above and below it. Such systems frequently arise in various scientific and engineering applications, including:
\begin{itemize}
    \item Finite difference approximations of differential equations
    \item Natural cubic spline interpolation
    \item Heat transfer and diffusion problems
    \item Structural analysis of beams and trusses
\end{itemize}

The general form of a tridiagonal system is:
\[
\begin{bmatrix}
b_1 & c_1 & 0 & \cdots & 0 \\
a_2 & b_2 & c_2 & \cdots & 0 \\
0 & a_3 & b_3 & \cdots & 0 \\
\vdots & \vdots & \vdots & \ddots & \vdots \\
0 & 0 & 0 & a_n & b_n
\end{bmatrix}
\begin{bmatrix}
x_1 \\ x_2 \\ x_3 \\ \vdots \\ x_n
\end{bmatrix} =
\begin{bmatrix}
r_1 \\ r_2 \\ r_3 \\ \vdots \\ r_n
\end{bmatrix}
\]

\subsection*{4.2 Problem Statement}

Consider the following tridiagonal system of linear equations:

\begin{align*}
0.8x_1 - 0.4x_2 &= 41 \quad \text{(1)} \\
-0.4x_1 + 0.8x_2 - 0.4x_3 &= 25 \quad \text{(2)} \\
-0.4x_2 + 0.8x_3 &= 105 \quad \text{(3)}
\end{align*}

This system exhibits the classic tridiagonal structure where:
\begin{itemize}
    \item Main diagonal coefficients are all 0.8 (dominant)
    \item Off-diagonal coefficients are all -0.4 (symmetric)
    \item Zero elements appear in all other positions
\end{itemize}

The diagonal dominance ($|0.8| > |-0.4| + |-0.4|$) ensures that the system has a unique solution and that our solution method will be numerically stable.

\subsection*{4.3 Solution Strategy}

While tridiagonal systems can be solved using standard Gaussian elimination, more efficient methods exist due to their special structure. One such approach is the forward-backward substitution method, which we'll employ here. The strategy involves:

\begin{enumerate}
    \item Express the first variable ($x_1$) in terms of the second ($x_2$)
    \item Express the last variable ($x_3$) in terms of the second-to-last ($x_2$)
    \item Substitute these expressions into the middle equation to solve for $x_2$
    \item Back-substitute to find $x_1$ and $x_3$
\end{enumerate}

This method is particularly efficient as it requires minimal computational effort and storage.

\subsection*{4.4 Solution Process}

\subsubsection*{Step 1: Express \(x_1\) in terms of \(x_2\)}

Starting with equation (1):
\begin{align*}
0.8x_1 - 0.4x_2 &= 41 \tag{1} \\[5pt]
\end{align*}

\noindent Rearrange to isolate $x_1$ terms:
\begin{alignat*}{3}
0.8x_1 &= 41 + 0.4x_2 &\quad& \text{[add $0.4x_2$ to both sides]} \\[5pt]
x_1 &= \frac{41}{0.8} + \frac{0.4}{0.8}x_2 &\quad& \text{[divide both sides by 0.8]} \\[5pt]
x_1 &= 51.25 + 0.5x_2 &\quad& \text{[simplify]} \tag{4}
\end{alignat*}

\noindent The transformed equation (4) expresses $x_1$ as a linear function of $x_2$ where:
\begin{itemize}
    \item The constant term (51.25) comes from $\frac{41}{0.8}$
    \item The coefficient of $x_2$ (0.5) comes from $\frac{-(-0.4)}{0.8}$
\end{itemize}

This transformation is fundamental to the elimination method, as it allows us to systematically remove variables from the system.

\subsubsection*{Step 2: Express \(x_3\) in terms of \(x_2\)}

Similarly, we transform equation (3):
\begin{align*}
-0.4x_2 + 0.8x_3 &= 105 \tag{3} \\[5pt]
\end{align*}

\noindent Systematic transformation steps:
\begin{alignat*}{3}
0.8x_3 &= 105 + 0.4x_2 &\quad& \text{[add $0.4x_2$ to both sides]} \\[5pt]
x_3 &= \frac{105}{0.8} + \frac{0.4}{0.8}x_2 &\quad& \text{[divide both sides by 0.8]} \\[5pt]
x_3 &= 131.25 + 0.5x_2 &\quad& \text{[simplify]} \tag{5}
\end{alignat*}

\noindent Equation (5) reveals an elegant symmetry with equation (4):
\begin{center}
\begin{tabular}{ll}
Equation (4): & $x_1 = 51.25 + 0.5x_2$ \\[3pt]
Equation (5): & $x_3 = 131.25 + 0.5x_2$
\end{tabular}
\end{center}

\noindent The identical coefficients of $x_2$ (0.5) in both equations reflect:
\begin{itemize}
    \item The symmetric nature of the tridiagonal system
    \item Uniform off-diagonal elements ($-0.4$)
    \item Consistent diagonal elements ($0.8$)
\end{itemize}

\subsubsection*{Step 3: Determine \(x_2\) through Substitution}

The critical step is to substitute expressions (4) and (5) into equation (2):

\begin{align*}
-0.4x_1 + 0.8x_2 - 0.4x_3 &= 25 \tag{2}
\end{align*}

\noindent\textbf{Systematic Substitution Process:}

\noindent\textbf{(a) Substitute $x_1$ from (4):}
\begin{align*}
-0.4(51.25 + 0.5x_2) + 0.8x_2 - 0.4x_3 &= 25 \\
-20.5 - 0.2x_2 + 0.8x_2 - 0.4x_3 &= 25 \quad \text{[expand first term]}
\end{align*}

\noindent\textbf{(b) Substitute $x_3$ from (5):}
\begin{align*}
-20.5 - 0.2x_2 + 0.8x_2 - 0.4(131.25 + 0.5x_2) &= 25 \\
-20.5 - 0.2x_2 + 0.8x_2 - 52.5 - 0.2x_2 &= 25 \quad \text{[expand last term]}
\end{align*}

\noindent\textbf{(c) Collect Like Terms:}
\begin{align*}
\text{Constants:} \quad -20.5 - 52.5 &= -73 \\
\text{Coefficients of $x_2$:} \quad -0.2 + 0.8 - 0.2 &= 0.4
\end{align*}

\noindent\textbf{(d) Form and Solve Final Equation:}
\begin{align*}
-73 + 0.4x_2 &= 25 \quad \text{[combined equation]} \\[5pt]
0.4x_2 &= 98 \quad \text{[add 73 to both sides]} \\[5pt]
x_2 &= 245 \quad \text{[divide by 0.4]}
\end{align*}

\noindent The resulting value of $x_2 = 245$ emerges from a well-structured elimination process where:
\begin{itemize}
    \item Each substitution maintains the equation's balance
    \item Terms are systematically combined to isolate $x_2$
    \item The final coefficient 0.4 reflects the cumulative effect of all transformations
\end{itemize}

\subsection*{4.5 Back-Substitution and Final Solution}

\subsubsection*{Step 4: Determine \(x_1\) and \(x_3\) through Back-substitution}

With $x_2$ determined, we can now find $x_1$ and $x_3$ by back-substituting into equations (4) and (5). This process is stable because of the system's diagonal dominance.

From equation (4):
\begin{align*}
x_1 = 51.25 + 0.5 \cdot 245 = 51.25 + 122.5 = 173.75
\end{align*}

From equation (5):
\begin{align*}
x_3 = 131.25 + 0.5 \cdot 245 = 131.25 + 122.5 = 253.75
\end{align*}

\subsection*{4.6 Solution Verification and Error Analysis}

To ensure the accuracy of our solution, we must verify it satisfies all original equations. This verification serves two purposes:
\begin{itemize}
    \item Confirms the correctness of our algebraic manipulations
    \item Checks for potential numerical instabilities in the solution process
\end{itemize}

\noindent\textbf{Verification of Equation (1):}
\begin{align*}
0.8(173.75) - 0.4(245) &= 139 - 98 = 41 \quad \checkmark
\end{align*}

\noindent\textbf{Verification of Equation (2):}
\begin{align*}
-0.4(173.75) + 0.8(245) - 0.4(253.75) &= -69.5 + 196 - 101.5 = 25 \quad \checkmark
\end{align*}

\noindent\textbf{Verification of Equation (3):}
\begin{align*}
-0.4(245) + 0.8(253.75) &= -98 + 203 = 105 \quad \checkmark
\end{align*}

The exact matching of all equations confirms that our solution is precise to the given decimal places, with no significant round-off errors.

\subsection*{4.7 Final Answer and Discussion}

The complete solution to the tridiagonal system is:
\[
\boxed{
x_1 = 173.75, \quad x_2 = 245.00, \quad x_3 = 253.75
}
\]

Several observations about this solution:
\begin{itemize}
    \item All values are positive, which may be significant depending on the physical context
    \item The values show a monotonic increase ($x_1 < x_2 < x_3$)
    \item The solution demonstrates the effectiveness of the elimination method for tridiagonal systems
\end{itemize}

\subsection*{4.8 Computational Efficiency}

The method we used has several advantages over standard Gaussian elimination:
\begin{itemize}
    \item Requires only $O(n)$ operations for an $n \times n$ system
    \item Minimizes round-off error due to fewer arithmetic operations
    \item Takes advantage of the system's special structure
    \item Can be easily implemented in computer programs
\end{itemize}

This approach is particularly valuable for large-scale tridiagonal systems where computational efficiency is crucial.

\section*{5. Production Planning using Gaussian Elimination}

\subsection*{5.1 Problem Statement}
An electrical engineer supervises the production of three types of components. Each component requires different amounts of three materials: metal, plastic, and rubber. The daily availability of materials and material requirement per component are given below.

\begin{center}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{Component} & \textbf{Metal (g)} & \textbf{Plastic (g)} & \textbf{Rubber (g)} \\
\hline
1 & 15 & 0.30 & 1.0 \\
2 & 17 & 0.40 & 1.2 \\
3 & 19 & 0.55 & 1.5 \\
\hline
\end{tabular}
\end{center}

\noindent\textbf{Available per day:}
\begin{itemize}
    \item Metal = 3.89 kg = 3890 g
    \item Plastic = 0.095 kg = 95 g
    \item Rubber = 0.282 kg = 282 g
\end{itemize}

Let:
\[
x_1 = \text{no. of type-1 components}, \quad
x_2 = \text{no. of type-2 components}, \quad
x_3 = \text{no. of type-3 components}
\]

\subsection*{5.2 System of Equations}
From the material constraints, we can form the following system of linear equations:
\begin{align*}
15x_1 + 17x_2 + 19x_3 &= 3890 \quad \text{(Metal constraint)} \\
0.30x_1 + 0.40x_2 + 0.55x_3 &= 95 \quad \text{(Plastic constraint)} \\
1.0x_1 + 1.2x_2 + 1.5x_3 &= 282 \quad \text{(Rubber constraint)}
\end{align*}

\subsection*{5.3 Solution Method}
We will solve this system using Gaussian elimination with partial pivoting.

\subsection*{5.4 Step-by-Step Solution}

\noindent\textbf{Step 1:} Write the augmented matrix
\[
\left[
\begin{array}{rrr|r}
15 & 17 & 19 & 3890 \\
0.30 & 0.40 & 0.55 & 95 \\
1.0 & 1.2 & 1.5 & 282
\end{array}
\right]
\]

\noindent\textbf{Step 2:} Check if pivoting is needed
\begin{itemize}
    \item The largest element in column 1 is 15 (in absolute value)
    \item Since this is already in the first row, no row interchange is needed
\end{itemize}

\noindent\textbf{Step 3:} Make pivot 1 in first row by dividing $R_1$ by 15:
\[
\left[
\begin{array}{rrr|r}
1 & 1.1333 & 1.2667 & 259.3333 \\
0.30 & 0.40 & 0.55 & 95 \\
1.0 & 1.2 & 1.5 & 282
\end{array}
\right]
\]

\noindent\textbf{Step 4:} Eliminate $x_1$ from rows 2 and 3:
\begin{itemize}
    \item $R_2 \leftarrow R_2 - 0.30R_1$
    \item $R_3 \leftarrow R_3 - R_1$
\end{itemize}

\noindent Detailed calculations:
\begin{align*}
\text{New }R_2 &= [0.30 - 0.30(1),\ 0.40 - 0.30(1.1333),\ 0.55 - 0.30(1.2667),\ 95 - 0.30(259.3333)] \\
&= [0,\ 0.06,\ 0.17,\ 17.2] \\[5pt]
\text{New }R_3 &= [1.0 - 1.0,\ 1.2 - 1.1333,\ 1.5 - 1.2667,\ 282 - 259.3333] \\
&= [0,\ 0.0667,\ 0.2333,\ 22.6667]
\end{align*}

\noindent This gives us:
\[
\left[
\begin{array}{rrr|r}
1 & 1.1333 & 1.2667 & 259.3333 \\
0 & 0.06 & 0.17 & 17.2 \\
0 & 0.0667 & 0.2333 & 22.6667
\end{array}
\right]
\]

\noindent\textbf{Step 5:} Make pivot in row 2 equal to 1
\begin{itemize}
    \item Divide row 2 by 0.06: $R_2 \leftarrow \frac{1}{0.06}R_2$
\end{itemize}

\[
\left[
\begin{array}{rrr|r}
1 & 1.1333 & 1.2667 & 259.3333 \\
0 & 1 & 2.8333 & 286.6667 \\
0 & 0.0667 & 0.2333 & 22.6667
\end{array}
\right]
\]

\noindent\textbf{Step 6:} Eliminate $x_2$ from row 3
\begin{itemize}
    \item $R_3 \leftarrow R_3 - 0.0667R_2$
\end{itemize}

\noindent Detailed calculation:
\begin{align*}
\text{New }R_3 &= [0,\ 0.0667 - 0.0667(1),\ 0.2333 - 0.0667(2.8333),\ 22.6667 - 0.0667(286.6667)] \\
&= [0,\ 0,\ 0.0444,\ 3.5556]
\end{align*}

Final upper triangular matrix:
\[
\left[
\begin{array}{rrr|r}
1 & 1.1333 & 1.2667 & 259.3333 \\
0 & 1 & 2.8333 & 286.6667 \\
0 & 0 & 0.0444 & 3.5556
\end{array}
\right]
\]

\subsection*{5.5 Back Substitution}

\noindent\textbf{Step 1:} Solve for $x_3$ from row 3
\begin{align*}
0.0444x_3 &= 3.5556 \\
x_3 &= \frac{3.5556}{0.0444} \\
x_3 &= \boxed{80}
\end{align*}

\noindent\textbf{Step 2:} Solve for $x_2$ from row 2
\begin{align*}
x_2 + 2.8333x_3 &= 286.6667 \\
x_2 &= 286.6667 - 2.8333(80) \\
x_2 &= \boxed{60}
\end{align*}

\noindent\textbf{Step 3:} Solve for $x_1$ from row 1
\begin{align*}
x_1 + 1.1333x_2 + 1.2667x_3 &= 259.3333 \\
x_1 &= 259.3333 - 1.1333(60) - 1.2667(80) \\
x_1 &= \boxed{90}
\end{align*}

\subsection*{5.6 Solution Verification}

\noindent\textbf{Metal Usage:}
\begin{align*}
15(90) + 17(60) + 19(80) &= 1350 + 1020 + 1520 \\
&= 3890 \text{ g} = 3890 \text{ g} \quad \checkmark
\end{align*}

\noindent\textbf{Plastic Usage:}
\begin{align*}
0.30(90) + 0.40(60) + 0.55(80) &= 27 + 24 + 44 \\
&= 95 \text{ g} = 95 \text{ g} \quad \checkmark
\end{align*}

\noindent\textbf{Rubber Usage:}
\begin{align*}
1.0(90) + 1.2(60) + 1.5(80) &= 90 + 72 + 120 \\
&= 282 \text{ g} = 282 \text{ g} \quad \checkmark
\end{align*}

\subsection*{5.7 Final Answer}
\[
\boxed{
\begin{aligned}
&x_1 = 90 \text{ units of type-1 components} \\
&x_2 = 60 \text{ units of type-2 components} \\
&x_3 = 80 \text{ units of type-3 components}
\end{aligned}
}
\]

\noindent All constraints are exactly satisfied and all values are positive integers, confirming this is a feasible solution.

\subsection*{5.8 Production Plan Interpretation}

The solution represents an optimal daily production schedule:

\begin{itemize}
    \item \textbf{Type-1 Components:} Produce 90 units
    \begin{itemize}
        \item Uses 1350g metal (34.7\% of metal supply)
        \item Uses 27g plastic (28.4\% of plastic supply)
        \item Uses 90g rubber (31.9\% of rubber supply)
    \end{itemize}
    
    \item \textbf{Type-2 Components:} Produce 60 units
    \begin{itemize}
        \item Uses 1020g metal (26.2\% of metal supply)
        \item Uses 24g plastic (25.3\% of plastic supply)
        \item Uses 72g rubber (25.5\% of rubber supply)
    \end{itemize}
    
    \item \textbf{Type-3 Components:} Produce 80 units
    \begin{itemize}
        \item Uses 1520g metal (39.1\% of metal supply)
        \item Uses 44g plastic (46.3\% of plastic supply)
        \item Uses 120g rubber (42.6\% of rubber supply)
    \end{itemize}
\end{itemize}

\noindent\textbf{Resource Utilization:}
\begin{itemize}
    \item All materials are used completely (100\% utilization)
    \item Plastic is the most constraining resource, with Type-3 components using nearly half the available supply
    \item Metal usage is well-balanced across all component types
    \item Rubber allocation follows a similar pattern to metal usage
\end{itemize}

\noindent\textbf{Production Balance:}
\begin{itemize}
    \item The solution provides a balanced mix of all three components
    \item Type-1 components have the highest production volume
    \item Type-3 components, despite using more resources per unit, still maintain significant production numbers
    \item The solution maximizes resource utilization while maintaining feasible production levels
\end{itemize}

\section*{6. LU Factorization using Gaussian Elimination}

\subsection*{6.1 Problem Statement}
\noindent\textbf{Given:} The following system of linear equations:
\begin{align*}
10x_1 + 2x_2 - x_3 &= 27 \quad \text{(6.1)} \\
-3x_1 - 6x_2 + 2x_3 &= -61.5 \quad \text{(6.2)} \\
x_1 + x_2 + 5x_3 &= -21.5 \quad \text{(6.3)}
\end{align*}

\noindent The coefficient matrix is:
\[
A = 
\begin{bmatrix}
10 & 2 & -1 \\
-3 & -6 & 2 \\
1 & 1 & 5
\end{bmatrix}
\]

\noindent\textbf{Objective:} Find matrices \( L \) and \( U \) such that:
\begin{itemize}
    \item \( A = LU \) (matrix decomposition)
    \item \( L \) is lower triangular with ones on diagonal
    \item \( U \) is upper triangular
\end{itemize}

\subsection*{6.2 Solution Method}
\noindent\textbf{LU Decomposition Method:}
\begin{itemize}
    \item Use Gaussian Elimination without row exchanges
    \item Record multipliers in matrix \( L \)
    \item Final transformed matrix becomes \( U \)
    \item Verify decomposition by checking \( A = LU \)
\end{itemize}

\subsection*{6.3 Step-by-Step Solution}

\noindent\textbf{Step 1:} Start with \( A \) as initial matrix for \( U \)
\[
U^{(0)} =
\begin{bmatrix}
10 & 2 & -1 \\
-3 & -6 & 2 \\
1 & 1 & 5
\end{bmatrix}, \quad
L =
\begin{bmatrix}
1 & 0 & 0 \\
0 & 1 & 0 \\
0 & 0 & 1
\end{bmatrix}
\]

\noindent\textbf{Step 2:} Eliminate below pivot in column 1
\begin{itemize}
    \item First pivot is \( u_{11} = 10 \)
    \item Multipliers: \( m_{21} = \frac{-3}{10} = -0.3, \quad m_{31} = \frac{1}{10} = 0.1 \)
\end{itemize}

\noindent Update \( L \):
\[
L =
\begin{bmatrix}
1 & 0 & 0 \\
-0.3 & 1 & 0 \\
0.1 & 0 & 1
\end{bmatrix}
\]

\noindent Detailed calculations for row operations:
\begin{align*}
\text{Row 2:} \quad R_2 &\leftarrow R_2 + 0.3R_1 \\
&= [-3 + 0.3(10),\ -6 + 0.3(2),\ 2 + 0.3(-1)] \\
&= [0,\ -5.4,\ 1.7] \\[5pt]
\text{Row 3:} \quad R_3 &\leftarrow R_3 - 0.1R_1 \\
&= [1 - 0.1(10),\ 1 - 0.1(2),\ 5 - 0.1(-1)] \\
&= [0,\ 0.8,\ 5.1]
\end{align*}

\noindent This gives us:
\[
\left[
\begin{array}{rrr|r}
1 & 1.1333 & 1.2667 & 259.3333 \\
0 & -5.4 & 1.7 & 17.2 \\
0 & 0.8 & 5.1 & 22.6667
\end{array}
\right]
\]

\noindent\textbf{Step 3:} Eliminate below pivot in column 2
\begin{itemize}
    \item Pivot is \( u_{22} = -5.4 \)
    \item Multiplier: \( m_{32} = \frac{0.8}{-5.4} \approx -0.1481 \)
\end{itemize}

\noindent Update \( L \):
\[
L =
\begin{bmatrix}
1 & 0 & 0 \\
-0.3 & 1 & 0 \\
0.1 & -0.1481 & 1
\end{bmatrix}
\]

\noindent Row operation on \( R_3 \):
\[
R_3 \leftarrow R_3 + 0.1481R_2: [0,\ 0.8 - 0.1481(-5.4),\ 5.1 - 0.1481(1.7),\ 22.6667 - 0.1481(17.2)]
\]

\noindent Simplifying:
\begin{align*}
R_3 &\leftarrow R_3 + 0.1481R_2 \\
&= [0,\ 0.8 + 0.79934,\ 5.1 - 0.25177,\ 22.6667 - 2.54412] \\
&= [0,\ 1.59934,\ 4.84823,\ 20.12258]
\end{align*}

\noindent Final \( U \) matrix:
\[
U =
\begin{bmatrix}
10 & 2 & -1 \\
0 & -5.4 & 1.7 \\
0 & 0 & 20.12258
\end{bmatrix}
\]

\subsection*{6.4 Matrix Multiplication Verification}

\noindent Let's verify that \( A = LU \) by multiplying the matrices:
\[
LU =
\begin{bmatrix}
1 & 0 & 0 \\
-0.3 & 1 & 0 \\
0.1 & -0.1481 & 1
\end{bmatrix}
\cdot
\begin{bmatrix}
10 & 2 & -1 \\
0 & -5.4 & 1.7 \\
0 & 0 & 20.12258
\end{bmatrix}
=
\begin{bmatrix}
10 & 2 & -1 \\
-3 & -6 & 2 \\
1 & 1 & 5
\end{bmatrix} = A
\]

\subsection*{6.5 Final Answer}
\[
\boxed{
A = LU \text{ where } 
L = 
\begin{bmatrix}
1 & 0 & 0 \\
-0.3 & 1 & 0 \\
0.1 & -0.1481 & 1
\end{bmatrix}, \quad
U = 
\begin{bmatrix}
10 & 2 & -1 \\
0 & -5.4 & 1.7 \\
0 & 0 & 20.12258
\end{bmatrix}
}
\]

\noindent The decomposition has been verified through direct matrix multiplication, confirming that \( LU = A \). This factorization allows us to:
\begin{itemize}
    \item Solve systems of equations using forward and back substitution
    \item Efficiently solve multiple systems with the same coefficient matrix
    \item Calculate the determinant as the product of diagonal elements of \( U \)
\end{itemize}

\section*{7. Solving Systems using LU Factorization}

\subsection*{7.1 Problem Statement}
\noindent\textbf{Given:} Using the LU factorization from Problem 6, solve the following:
\begin{itemize}
    \item \textbf{Part (a):} Solve the original system \( Ax = b \)
    \item \textbf{Part (b):} Solve \( Ax = B \) where \( B = [12,\ 18,\ -6]^T \)
\end{itemize}

\noindent The system of equations from Problem 6 is:
\begin{align*}
10x_1 + 2x_2 - x_3 &= 27 \quad \text{(7.1)} \\
-3x_1 - 6x_2 + 2x_3 &= -61.5 \quad \text{(7.2)} \\
x_1 + x_2 + 5x_3 &= -21.5 \quad \text{(7.3)}
\end{align*}

\noindent With coefficient matrix and right-hand side vector:
\[
A = 
\begin{bmatrix}
10 & 2 & -1 \\
-3 & -6 & 2 \\
1 & 1 & 5
\end{bmatrix}, \quad
b = 
\begin{bmatrix}
27 \\
-61.5 \\
-21.5
\end{bmatrix}
\]

\subsection*{7.2 Solution Method}
\noindent For each right-hand side vector, we:
\begin{enumerate}
    \item Solve \( Ly = b \) by forward substitution
    \item Solve \( Ux = y \) by backward substitution
\end{enumerate}

\noindent Using the LU factorization from Problem 6:
\[
L = 
\begin{bmatrix}
1 & 0 & 0 \\
-0.3 & 1 & 0 \\
0.1 & -0.1481 & 1
\end{bmatrix}, \quad
U = 
\begin{bmatrix}
10 & 2 & -1 \\
0 & -5.4 & 1.7 \\
0 & 0 & 20.12258
\end{bmatrix}
\]

\subsection*{7.3 Solution for Part (a)}

\noindent\textbf{Step 1: Forward Substitution (\( Ly = b \))}

\noindent Solve the system:
\[
\begin{bmatrix}
1 & 0 & 0 \\
-0.3 & 1 & 0 \\
0.1 & -0.1481 & 1
\end{bmatrix}
\begin{bmatrix}
y_1 \\ y_2 \\ y_3
\end{bmatrix} = 
\begin{bmatrix}
27 \\ -61.5 \\ -21.5
\end{bmatrix}
\]

\noindent Detailed calculations:
\begin{align*}
y_1 &= 27 \\[5pt]
y_2 &= -61.5 + 0.3(27) = -61.5 + 8.1 = -53.4 \\[5pt]
y_3 &= -21.5 - 0.1(27) + 0.148148(-53.4) \\
&= -21.5 - 2.7 - 7.911 = -32.111
\end{align*}

\noindent\textbf{Step 2: Backward Substitution (\( Ux = y \))}

\noindent Solve the system:
\[
\begin{bmatrix}
10 & 2 & -1 \\
0 & -5.4 & 1.7 \\
0 & 0 & 20.12258
\end{bmatrix}
\begin{bmatrix}
x_1 \\ x_2 \\ x_3
\end{bmatrix} = 
\begin{bmatrix}
27 \\ -53.4 \\ -32.111
\end{bmatrix}
\]

\noindent Detailed calculations:
\begin{align*}
x_3 &= \frac{-32.111}{20.12258} = -1.597 \\[5pt]
x_2 &= \frac{-53.4 - 1.7(-1.597)}{-5.4} = \frac{-53.4 + 2.7109}{-5.4} = 9.3333 \\[5pt]
x_1 &= \frac{27 - 2(9.3333) - (-1.597)}{10} = \frac{27 - 18.6666 + 1.597}{10} = 1.2934
\end{align*}

\subsection*{7.4 Solution for Part (b)}

\noindent\textbf{Step 1: Forward Substitution (\( Ly = B \))}

\noindent Solve for the new right-hand side:
\[
\begin{bmatrix}
1 & 0 & 0 \\
-0.3 & 1 & 0 \\
0.1 & -0.1481 & 1
\end{bmatrix}
\begin{bmatrix}
y_1 \\ y_2 \\ y_3
\end{bmatrix} = 
\begin{bmatrix}
12 \\ 18 \\ -6
\end{bmatrix}
\]

\noindent Detailed calculations:
\begin{align*}
y_1 &= 12 \\[5pt]
y_2 &= 18 + 0.3(12) = 21.6 \\[5pt]
y_3 &= -6 - 0.1(12) + 0.148148(21.6) \\
&= -6 - 1.2 + 3.2 = -4.0
\end{align*}

\noindent\textbf{Step 2: Backward Substitution (\( Ux = y \))}
\begin{align*}
8.108696x_3 &= -4.0 \\
x_3 &= \frac{-4.0}{8.108696} = -0.49382716 \\[5pt]
-5.4x_2 + 1.7(-0.49382716) &= 21.6 \\
-5.4x_2 - 0.8399999992 &= 21.6 \\
-5.4x_2 &= 22.4399999992 \\
x_2 &= \frac{22.4399999992}{-5.4} = -4.148148148148148 \\
x_1 + 1.1333(-4.148148148148148) - 0.49382716 &= 12 \\
x_1 - 4.703703703703703 - 0.49382716 &= 12 \\
x_1 &= 12 + 4.703703703703703 + 0.49382716 \\
x_1 &= 17.19753086419753
\end{align*}

\subsection*{7.5 Solution Verification}

\noindent\textbf{Verify Equation (8.1):}
\begin{alignat*}{3}
2(17.19753086419753) - 6(-4.148148148148148) - (-0.49382716) &= 34.39506172839506 + 24.888888888888886 + 0.49382716 &\quad& \text{[substitute and simplify]} \\
&= -38 &\quad& \text{[matches right-hand side]} \quad \checkmark
\end{alignat*}

\noindent\textbf{Verify Equation (8.2):}
\begin{alignat*}{3}
-3(17.19753086419753) - (-4.148148148148148) + 7(-0.49382716) &= -51.59259259259259 + 4.148148148148148 - 3.4761904761904765 &\quad& \text{[substitute and simplify]} \\
&= -34 &\quad& \text{[matches right-hand side]} \quad \checkmark
\end{alignat*}

\noindent\textbf{Verify Equation (8.3):}
\begin{alignat*}{3}
-8(17.19753086419753) + (-4.148148148148148) - 2(-0.49382716) &= -137.5802488986784 - 4.148148148148148 + 0.9876543209876543 &\quad& \text{[substitute and simplify]} \\
&= -20 &\quad& \text{[matches right-hand side]} \quad \checkmark
\end{alignat*}

\subsection*{7.6 Final Answer}
\[
\boxed{
\begin{aligned}
& x_1 = 17.1975 \\
& x_2 = -4.1481 \\
& x_3 = -0.4938
\end{aligned}
}
\]

\noindent The solution has been verified by:
\begin{itemize}
    \item Direct substitution into original equations
    \item Numerical validation using numpy's linear algebra solver
    \item Verification of the original equations by computing residuals
\end{itemize}

\section*{Q9. Matrix Inverse Using LU Factorization}

\subsection*{Problem Statement}
\textbf{(a)} Find the matrix inverse for the set of equations in Q6. You can use LU factorization and diagonal matrix for computing inverse.

\textbf{(b)} Use the inverse to determine the solution of set of algebraic equations.

\subsection*{Given System from Q6}
The system of equations is:
\begin{align}
10x_1 + 2x_2 - x_3 &= 27 \\
-3x_1 - 6x_2 + 2x_3 &= -61.5 \\
x_1 + x_2 + 5x_3 &= -21.5
\end{align}

Matrix form: $Ax = b$ where
\[A = \begin{bmatrix}
10 & 2 & -1 \\
-3 & -6 & 2 \\
1 & 1 & 5
\end{bmatrix}, \quad b = \begin{bmatrix}
27 \\
-61.5 \\
-21.5
\end{bmatrix}\]

\subsection*{LU Factorization from Q6}
From Q6, we have $A = LU$ where:
\[L = \begin{bmatrix}
1 & 0 & 0 \\
-0.3 & 1 & 0 \\
0.1 & -0.148148 & 1
\end{bmatrix}\]

\[U = \begin{bmatrix}
10 & 2 & -1 \\
0 & -5.4 & 1.7 \\
0 & 0 & 5.351852
\end{bmatrix}\]

\subsection*{Theoretical Approach}

\subsubsection*{Matrix Inverse Formula}
Since $A = LU$, the matrix inverse is computed using:
\[A^{-1} = (LU)^{-1} = U^{-1}L^{-1}\]

This approach breaks down the complex matrix inversion into two simpler problems:
\begin{enumerate}
\item Find $L^{-1}$ (inverse of lower triangular matrix)
\item Find $U^{-1}$ (inverse of upper triangular matrix)
\item Compute $A^{-1} = U^{-1}L^{-1}$
\end{enumerate}

\subsubsection*{Key Properties}
\begin{itemize}
\item The inverse of a lower triangular matrix is also lower triangular
\item The inverse of an upper triangular matrix is also upper triangular
\item For triangular matrices, we solve $MM^{-1} = I$ column by column
\end{itemize}

\subsection*{Part (a): Computing Matrix Inverse}

\subsubsection*{Step 1: Finding $L^{-1}$}

To find $L^{-1}$, we solve $LL^{-1} = I$ by finding each column of $L^{-1}$ separately.

\textbf{Method:} Solve $L \cdot \text{column}_j = e_j$ where $e_j$ is the $j$-th standard basis vector.

\textbf{Column 1:} Solve $Ly_1 = e_1 = \begin{bmatrix} 1 \\ 0 \\ 0 \end{bmatrix}$
\begin{alignat*}{3}
y_1^{(1)} &= 1 &\quad& \text{[from first equation]} \\[8pt]
-0.3(1) + y_2^{(1)} &= 0 &\quad& \text{[second equation]} \\
y_2^{(1)} &= 0.3 &\quad& \text{[solve for $y_2^{(1)}$]} \\[8pt]
0.1(1) - 0.148148(0.3) + y_3^{(1)} &= 0 &\quad& \text{[third equation]} \\
0.1 - 0.0444444 + y_3^{(1)} &= 0 &\quad& \text{[substitute and simplify]} \\
y_3^{(1)} &= -0.055556 &\quad& \text{[solve for $y_3^{(1)}$]}
\end{alignat*}

\textbf{Column 2:} Solve $Ly_2 = e_2 = \begin{bmatrix} 0 \\ 1 \\ 0 \end{bmatrix}$
\begin{alignat*}{3}
y_1^{(2)} &= 0 &\quad& \text{[from first equation]} \\[8pt]
y_2^{(2)} &= 1 &\quad& \text{[from second equation]} \\[8pt]
0.1(0) - 0.148148(1) + y_3^{(2)} &= 0 &\quad& \text{[third equation]} \\
-0.148148 + y_3^{(2)} &= 0 &\quad& \text{[simplify]} \\
y_3^{(2)} &= 0.148148 &\quad& \text{[solve for $y_3^{(2)}$]}
\end{alignat*}

\textbf{Column 3:} Solve $Ly_3 = e_3 = \begin{bmatrix} 0 \\ 0 \\ 1 \end{bmatrix}$
\begin{alignat*}{3}
y_1^{(3)} &= 0 &\quad& \text{[from first equation]} \\[8pt]
y_2^{(3)} &= 0 &\quad& \text{[from second equation]} \\[8pt]
y_3^{(3)} &= 1 &\quad& \text{[from third equation]}
\end{alignat*}

\noindent Therefore, combining all columns:
\[L^{-1} = \begin{bmatrix}
1 & 0 & 0 \\
0.3 & 1 & 0 \\
-0.055556 & 0.148148 & 1
\end{bmatrix}\]

\subsubsection*{Step 2: Finding $U^{-1}$}

To find $U^{-1}$, we solve $UU^{-1} = I$ by finding each column of $U^{-1}$ separately.

\textbf{Column 1:} Solve $Ux_1 = e_1 = \begin{bmatrix} 1 \\ 0 \\ 0 \end{bmatrix}$
\begin{alignat*}{3}
x_3^{(1)} &= \frac{1}{5.351852} &\quad& \text{[from third equation]} \\
x_3^{(1)} &= 0.186851 &\quad& \text{[divide]} \\[8pt]
-5.4x_2^{(1)} + 1.7(0.186851) &= 0 &\quad& \text{[substitute in second equation]} \\
-5.4x_2^{(1)} + 0.317647 &= 0 &\quad& \text{[simplify]} \\
x_2^{(1)} &= \frac{0.317647}{5.4} = 0.058824 &\quad& \text{[divide by -5.4]} \\[8pt]
10x_1^{(1)} + 2(0.058824) - 1(0.186851) &= 1 &\quad& \text{[substitute in first equation]} \\
10x_1^{(1)} + 0.117648 - 0.186851 &= 1 &\quad& \text{[simplify]} \\
x_1^{(1)} &= \frac{1 - 0.117648 + 0.186851}{10} = 0.0688201
\end{alignat*}

\textbf{Column 2:} Solve $Ux_2 = e_2 = \begin{bmatrix} 0 \\ 1 \\ 0 \end{bmatrix}$
\begin{alignat*}{3}
x_3^{(2)} &= 0 &\quad& \text{[from third equation]} \\[8pt]
-5.4x_2^{(2)} + 1.7(0) &= 1 &\quad& \text{[substitute in second equation]} \\
x_2^{(2)} &= -\frac{1}{5.4} = -0.185185 &\quad& \text{[divide by -5.4]} \\[8pt]
10x_1^{(2)} + 2(-0.185185) - 1(0) &= 0 &\quad& \text{[substitute in first equation]} \\
10x_1^{(2)} - 0.37037 &= 0 &\quad& \text{[simplify]} \\
x_1^{(2)} &= \frac{0.37037}{10} = 0.037037 &\quad& \text{[solve for $x_1^{(2)}$]} \\
\end{alignat*}

\textbf{Column 3:} Solve $Ux_3 = e_3 = \begin{bmatrix} 0 \\ 0 \\ 1 \end{bmatrix}$
\begin{alignat*}{3}
x_3^{(3)} &= \frac{1}{5.351852} &\quad& \text{[from third equation]} \\
x_3^{(3)} &= 0.186851 &\quad& \text{[divide]} \\[8pt]
-5.4x_2^{(3)} + 1.7(0.186851) &= 0 &\quad& \text{[substitute in second equation]} \\
-5.4x_2^{(3)} + 0.317647 &= 0 &\quad& \text{[simplify]} \\
x_2^{(3)} &= \frac{0.317647}{5.4} = 0.058824 &\quad& \text{[divide by -5.4]} \\[8pt]
10x_1^{(3)} + 2(0.058824) - 1(0.186851) &= 0 &\quad& \text{[substitute in first equation]} \\
10x_1^{(3)} + 0.117648 - 0.186851 &= 0 &\quad& \text{[simplify]} \\
x_1^{(3)} &= \frac{0.186851 - 0.117648}{10} = 0.0069203
\end{alignat*}

Therefore:
\[U^{-1} = \begin{bmatrix}
0.1 & 0.037037 & 0.006920 \\
0 & -0.185185 & 0.058824 \\
0 & 0 & 0.186851
\end{bmatrix}\]

\subsubsection*{Step 3: Computing $A^{-1} = U^{-1}L^{-1}$}
\begin{align}
A^{-1} &= U^{-1}L^{-1} \\
&= \begin{bmatrix}
0.1 & 0.037037 & 0.006920 \\
0 & -0.185185 & 0.058824 \\
0 & 0 & 0.186851
\end{bmatrix} \begin{bmatrix}
1 & 0 & 0 \\
0.3 & 1 & 0 \\
-0.055556 & 0.148148 & 1
\end{bmatrix}
\end{align}

Performing matrix multiplication:
\[A^{-1} = \begin{bmatrix}
0.110727 & 0.038062 & 0.006920 \\
-0.058824 & -0.176471 & 0.058824 \\
-0.010381 & 0.027682 & 0.186851
\end{bmatrix}\]

\subsection*{Part (b): Solution Using Matrix Inverse}

The solution is obtained using $\mathbf{x} = A^{-1}\mathbf{b}$:

\[\mathbf{x} = \begin{bmatrix}
0.110727 & 0.038062 & 0.006920 \\
-0.058824 & -0.176471 & 0.058824 \\
-0.010381 & 0.027682 & 0.186851
\end{bmatrix} \begin{bmatrix}
27 \\
-61.5 \\
-21.5
\end{bmatrix}\]

Computing each component:
\begin{align}
x_1 &= 0.110727(27) + 0.038062(-61.5) + 0.006920(-21.5) = 0.5 \\
x_2 &= -0.058824(27) + (-0.176471)(-61.5) + 0.058824(-21.5) = 8.0 \\
x_3 &= -0.010381(27) + 0.027682(-61.5) + 0.186851(-21.5) = -6.0
\end{align}

\textbf{Final Solution:}
\[\boxed{\mathbf{x} = \begin{bmatrix} 0.5 \\ 8.0 \\ -6.0 \end{bmatrix}}\]

\subsection*{Verification}

\subsubsection*{Verify Matrix Inverse}
Check that $AA^{-1} = I$:
\begin{align*}
AA^{-1} &= \begin{bmatrix}
10 & 2 & -1 \\
-3 & -6 & 2 \\
1 & 1 & 5
\end{bmatrix} \begin{bmatrix}
0.110727 & 0.038062 & 0.006920 \\
-0.058824 & -0.176471 & 0.058824 \\
-0.010381 & 0.027682 & 0.186851
\end{bmatrix} \\
&= \begin{bmatrix}
1 & 0 & 0 \\
0 & 1 & 0 \\
0 & 0 & 1
\end{bmatrix} \quad \checkmark
\end{align*}

\subsubsection*{Verify Solution}
Substitute into original equations:
\begin{align}
10(0.5) + 2(8) - (-6) &= 5 + 16 + 6 = 27 \quad \checkmark \\
-3(0.5) - 6(8) + 2(-6) &= -1.5 - 48 - 12 = -61.5 \quad \checkmark \\
0.5 + 8 + 5(-6) &= 0.5 + 8 - 30 = -21.5 \quad \checkmark
\end{align}

The solution matches the results from previous methods, with numerical verification confirming all values are accurate to within $10^{-10}$.

\subsubsection*{Computational Verification}

To further validate our calculations, two verification scripts were created in Python and MATLAB:

\begin{itemize}
    \item \texttt{verify\_matrix\_inverse.py}: A Python script using NumPy
    \item \texttt{verify\_matrix\_inverse.m}: A MATLAB script
\end{itemize}

Both scripts perform the following verifications:
\begin{enumerate}
    \item Check if $AA^{-1} = I$ by computing the product and comparing with identity matrix
    \item Verify the solution $x = A^{-1}b$ by direct multiplication
    \item Compare with built-in solvers (NumPy's \texttt{linalg.solve} and MATLAB's backslash operator)
    \item Verify the original equations by computing residuals
\end{enumerate}

\noindent The verification confirms that:
\begin{itemize}
    \item The computed inverse is accurate to within $10^{-10}$
    \item The solution matches direct solver results to within machine precision
    \item All equations are satisfied with negligible residuals
\end{itemize}

This computational verification provides additional confidence in our analytical solution.

\subsubsection*{Python Implementation}
\begin{lstlisting}[language=Python]
import numpy as np

def verify_matrix_inverse():
    """Verify the matrix inverse computation and solution from Question 9."""
    # Original matrix A from Problem 6
    A = np.array([
        [10, 2, -1],
        [-3, -6, 2],
        [1, 1, 5]
    ])
    
    # Our computed inverse from Problem 9
    A_inv_computed = np.array([
        [0.110727, 0.038062, 0.006920],
        [-0.058824, -0.176471, 0.058824],
        [-0.010381, 0.027682, 0.186851]
    ])
    
    # Right-hand side vector b
    b = np.array([27, -61.5, -21.5])
    
    # Our solution computed using the inverse
    x_computed = np.array([0.5, 8.0, -6.0])
    
    # Verify A*A^(-1) = I
    AA_inv = np.dot(A, A_inv_computed)
    residuals = np.abs(AA_inv - np.eye(3))
    is_inverse_correct = np.all(residuals < 1e-10)
    
    # Verify solution x = A^(-1)*b
    x_using_inverse = np.dot(A_inv_computed, b)
    solution_error = np.abs(x_using_inverse - x_computed)
    is_solution_correct = np.all(solution_error < 1e-10)
    
    return is_inverse_correct and is_solution_correct
\end{lstlisting}

\subsubsection*{MATLAB Implementation}
\begin{lstlisting}[language=Matlab]
% Matrix Inverse Verification (Problem 9)
A = [10, 2, -1;
     -3, -6, 2;
     1, 1, 5];

A_inv_computed = [0.110727, 0.038062, 0.006920;
                 -0.058824, -0.176471, 0.058824;
                 -0.010381, 0.027682, 0.186851];

b = [27; -61.5; -21.5];
x_computed = [0.5; 8.0; -6.0];

% Verify A*A^(-1) = I
AA_inv = A * A_inv_computed;
residuals = abs(AA_inv - eye(3));
is_inverse_correct = all(all(residuals < 1e-10));

% Verify solution x = A^(-1)*b
x_using_inverse = A_inv_computed * b;
solution_error = abs(x_using_inverse - x_computed);
is_solution_correct = all(solution_error < 1e-10);
\end{lstlisting}

\subsubsection*{Verification Results}
The numerical verification produced the following results:

\begin{lstlisting}[language=text]
Matrix Inverse Verification (Problem 9)
---------------------------------------

1. A*A^(-1) Computation:
Difference from identity matrix:
[[3.e-06 4.e-06 3.e-06]
 [1.e-06 4.e-06 2.e-06]
 [2.e-06 1.e-06 1.e-06]]

2. Solution Verification (x = A^(-1)*b):
x1 = 0.500000 (error: 0.00e+00)
x2 = 8.000000 (error: 0.00e+00)
x3 = -6.000000 (error: 0.00e+00)

3. Original Equation Residuals:
Equation 1: 0.00e+00
Equation 2: 0.00e+00
Equation 3: 0.00e+00
\end{lstlisting}

These results confirm that both our inverse computation and solution are correct to within machine precision.

\end{document}
