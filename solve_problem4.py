import numpy as np

def solve_problem4():
    """Solve Problem 4 using Gaussian elimination with improved precision."""
    # Original system
    A = np.array([
        [6, -2, 4],
        [-3, 8, -1],
        [1, 1, 5]
    ], dtype=np.float64)
    b = np.array([27, -61.5, -21.5], dtype=np.float64)
    
    # Solve using Gaussian elimination
    n = len(A)
    # Create augmented matrix [A|b]
    Ab = np.column_stack([A, b])
    
    # Forward elimination
    for k in range(n-1):
        for i in range(k+1, n):
            factor = Ab[i,k] / Ab[k,k]
            Ab[i] = Ab[i] - factor * Ab[k]
    
    # Back substitution
    x = np.zeros(n)
    for i in range(n-1, -1, -1):
        x[i] = (Ab[i,-1] - np.dot(Ab[i,i+1:n], x[i+1:])) / Ab[i,i]
    
    # Verify solution using numpy
    x_numpy = np.linalg.solve(A, b)
    
    print("Problem 4 Solution Verification")
    print("-" * 50)
    print("\nSolution comparison:")
    print(f"{'':15} {'Our Solution':>12} {'Numpy Solution':>15} {'Difference':>12}")
    print("-" * 54)
    for i, (our_x, np_x) in enumerate(zip(x, x_numpy)):
        diff = abs(our_x - np_x)
        print(f"x{i+1:d}:{' ':11} {our_x:12.6f} {np_x:15.6f} {diff:12.2e}")
    
    # Calculate residuals
    residuals = np.dot(A, x) - b
    print("\nResiduals:")
    print(f"{'Equation':10} {'Our Value':>12} {'Should Be':>12} {'Residual':>12}")
    print("-" * 46)
    for i, (res, target) in enumerate(zip(np.dot(A, x), b)):
        print(f"{i+1:d}:{' ':9} {res:12.6f} {target:12.6f} {res-target:12.2e}")
    
    return x

if __name__ == "__main__":
    solution = solve_problem4()
