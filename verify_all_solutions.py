import numpy as np

def verify_solution(A, b, x, label):
    print(f"\nVerifying {label}:")
    print("-" * 50)
    print(f"Solution: x = {x}")
    
    # Compute residuals
    residuals = np.dot(A, x) - b
    print(f"Residuals: {residuals}")
    print(f"Max absolute residual: {np.max(np.abs(residuals))}")
    
    # Check if solution is correct within numerical precision
    is_correct = np.allclose(np.dot(A, x), b, rtol=1e-10, atol=1e-10)
    print(f"Solution is correct: {is_correct}")
    
    # Show detailed equation verification
    print("\nDetailed verification:")
    for i, (row, bi) in enumerate(zip(A, b)):
        result = np.dot(row, x)
        print(f"Equation {i+1}: {result} = {bi} (diff: {result - bi})")
    return is_correct

# Problem 3 (2x2 system)
print("\n=== Problem 3: Original System ===")
A3 = np.array([[0.5, -1], [1.02, -2]])
b3 = np.array([-9.5, -18.8])
x3 = np.array([10, 14.5])
verify_solution(A3, b3, x3, "Problem 3 - Original System")

print("\n=== Problem 3: Modified System ===")
A3_mod = np.array([[0.52, -1], [1.02, -2]])
b3_mod = np.array([-9.5, -18.8])
x3_mod = np.array([-10, 4.3])
verify_solution(A3_mod, b3_mod, x3_mod, "Problem 3 - Modified System")

# Problem 4 (3x3 system)
print("\n=== Problem 4 ===")
A4 = np.array([
    [10, 2, -3],
    [-3, 6, 2],
    [1, 1, 5]
])
b4 = np.array([27, -61.5, -21.5])
x4 = np.array([2.796174, -7.329924, -4.566038])
verify_solution(A4, b4, x4, "Problem 4")

# Problem 6 and 7 (LU factorization)
print("\n=== Problems 6 & 7 ===")
A67 = np.array([
    [10, 2, -1],
    [-3, -6, 2],
    [1, 1, 5]
])
b67 = np.array([27, -61.5, -21.5])
x67 = np.array([0.5, 8.0, -6.0])
verify_solution(A67, b67, x67, "Problems 6 & 7")

# Problem 8 (LU with pivoting)
print("\n=== Problem 8 ===")
A8 = np.array([
    [2, -6, -1],
    [-3, -1, 7],
    [-8, 1, -2]
])
b8 = np.array([-38, -34, -20])
x8 = np.array([4.0, 8.0, -2.0])
verify_solution(A8, b8, x8, "Problem 8")
