import numpy as np

def verify_problem4():
    """Verify the solution for Problem 4 with improved precision."""
    # Original system
    A = np.array([
        [6, -2, 4],
        [-3, 8, -1],
        [1, 1, 5]
    ])
    b = np.array([27, -61.5, -21.5])
    
    # Compute solution using numpy for reference
    x_numpy = np.linalg.solve(A, b)
    
    # Our solution from Gaussian elimination
    x_gauss = np.array([2.796174, -7.329924, -4.566038])
    
    # Calculate residuals
    residuals = np.dot(A, x_gauss) - b
    
    print("Problem 4 Verification")
    print("-" * 50)
    print("\nSolution comparison:")
    print(f"{'':15} {'Our Solution':>12} {'Numpy Solution':>15} {'Difference':>12}")
    print("-" * 54)
    for i, (our_x, np_x) in enumerate(zip(x_gauss, x_numpy)):
        diff = abs(our_x - np_x)
        print(f"x{i+1:d}:{' ':11} {our_x:12.6f} {np_x:15.6f} {diff:12.2e}")
    
    print("\nResiduals:")
    print(f"{'Equation':10} {'Our Value':>12} {'Should Be':>12} {'Residual':>12}")
    print("-" * 46)
    for i, (res, right) in enumerate(zip(np.dot(A, x_gauss), b)):
        print(f"{i+1:d}:{' ':9} {res:12.6f} {right:12.6f} {res-right:12.2e}")
    
    return x_numpy

if __name__ == "__main__":
    x_correct = verify_problem4()
