import numpy as np

def verify_production_planning():
    """Verify the production planning solution from Question 5."""
    # Material requirements per component
    A = np.array([
        [15, 17, 19],      # Metal (g)
        [0.30, 0.40, 0.55], # Plastic (g)
        [1.0, 1.2, 1.5]     # Rubber (g)
    ])
    
    # Available materials
    b = np.array([3890, 95, 282])  # [Metal, Plastic, Rubber] in grams
    
    # Given solution
    x_given = np.array([11, 153.55, 53.45])
    
    # Verify constraints
    usage = np.dot(A, x_given)
    materials = ['Metal', 'Plastic', 'Rubber']
    
    print("Production Planning Solution Verification")
    print("---------------------------------------")
    print(f"Components to produce: x1={x_given[0]}, x2={x_given[1]:.2f}, x3={x_given[2]:.2f}\n")
    
    print("Material Usage Check:")
    print(f"{'Material':<10} {'Used (g)':>12} {'Limit (g)':>12} {'Margin':>10}")
    print("-" * 44)
    
    all_constraints_met = True
    for i, (mat, used, limit) in enumerate(zip(materials, usage, b)):
        margin = limit - used
        valid = margin >= -0.01  # Allow for small numerical errors
        all_constraints_met &= valid
        status = "✓" if valid else "✗"
        print(f"{mat:<10} {used:>12.2f} {limit:>12.2f} {margin:>10.2f} {status}")
    
    # Verify non-negativity
    all_positive = np.all(x_given >= 0)
    print(f"\nNon-negativity check: {'✓' if all_positive else '✗'}")
    
    # Compare with numpy solution
    x_numpy = np.linalg.solve(A, b)
    residual = np.linalg.norm(np.dot(A, x_given) - b)
    print(f"\nSolution residual: {residual:.2e}")
    
    # Verify optimality
    print("\nNumerical solution vs given solution:")
    print(f"{'Component':<10} {'Given':>10} {'Numerical':>12} {'Diff':>10}")
    print("-" * 42)
    for i, (given, numerical) in enumerate(zip(x_given, x_numpy)):
        diff = abs(given - numerical)
        print(f"x{i+1:<9} {given:>10.2f} {numerical:>12.2f} {diff:>10.2e}")
    
    return all_constraints_met and all_positive and residual < 1e-10

if __name__ == "__main__":
    verify_production_planning()
