import numpy as np

# Original system
A = np.array([
    [10, 2, -3],
    [-3, 6, 2],
    [1, 1, 5]
])

b = np.array([27, -61.5, -21.5])

# Solve using numpy for verification
x = np.linalg.solve(A, b)

print("Solution:")
print(f"x1 = {x[0]:.6f}")
print(f"x2 = {x[1]:.6f}")
print(f"x3 = {x[2]:.6f}")

print("\nVerification:")
residuals = np.dot(A, x) - b
print(f"Equation 1 residual: {residuals[0]:.10f}")
print(f"Equation 2 residual: {residuals[1]:.10f}")
print(f"Equation 3 residual: {residuals[2]:.10f}")
