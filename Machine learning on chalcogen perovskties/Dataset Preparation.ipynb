{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f01acf27", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import tarfile\n", "from glob import glob as g"]}, {"cell_type": "code", "execution_count": 2, "id": "668942c4", "metadata": {}, "outputs": [], "source": ["tar_paths=g(r'/home/<USER>/Coding/Machine learning on chalcogen perovskties/Tar files/*.tar.gz')"]}, {"cell_type": "code", "execution_count": 3, "id": "7a990a9f", "metadata": {}, "outputs": [{"data": {"text/plain": ["['/home/<USER>/Coding/Machine learning on chalcogen perovskties/Tar files/BaNiO3.tar.gz',\n", " '/home/<USER>/Coding/Machine learning on chalcogen perovskties/Tar files/FePS3.tar.gz',\n", " '/home/<USER>/Coding/Machine learning on chalcogen perovskties/Tar files/NH4CdCl3.tar.gz',\n", " '/home/<USER>/Coding/Machine learning on chalcogen perovskties/Tar files/PbPS3.tar.gz',\n", " '/home/<USER>/Coding/Machine learning on chalcogen perovskties/Tar files/MnPSe3.tar.gz',\n", " '/home/<USER>/Coding/Machine learning on chalcogen perovskties/Tar files/Al2S3.tar.gz']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["tar_paths"]}, {"cell_type": "code", "execution_count": 4, "id": "be969946", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final Folder/BaNiO3\n", "Final Folder/FePS3\n", "Final Folder/NH4CdCl3\n", "Final Folder/PbPS3\n", "Final Folder/MnPSe3\n", "Final Folder/Al2S3\n"]}], "source": ["for path in tar_paths:\n", "    folder_name='Final Folder/'+path.split('/')[-1].split('.')[0]\n", "    print(folder_name)\n", "    with tarfile.open(path) as f:\n", "        f.extractall(folder_name)"]}, {"cell_type": "code", "execution_count": 5, "id": "dbc3c217", "metadata": {}, "outputs": [], "source": ["path_bandgaps=g('/home/<USER>/Coding/Machine learning on chalcogen perovskties/Final Folder/*/*/VASP_Output_data/*/bandgap.json')"]}, {"cell_type": "code", "execution_count": 6, "id": "073b3708", "metadata": {}, "outputs": [{"data": {"text/plain": ["5040"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(path_bandgaps)"]}, {"cell_type": "code", "execution_count": 7, "id": "d06a1e94", "metadata": {}, "outputs": [], "source": ["def json_to_dict(path):\n", "    with open(path, 'r') as f:\n", "        data = json.load(f)\n", "    return data"]}, {"cell_type": "code", "execution_count": 8, "id": "c95d5223", "metadata": {}, "outputs": [], "source": ["# Initialize empty list to store data instances\n", "se_data_list = []\n", "\n", "# Collect all instances\n", "for path in path_bandgaps:\n", "    id=path.split('/')[-2]\n", "    se_data_instance = json_to_dict(path)\n", "    se_data_instance['id'] = id\n", "    se_data_list.append(se_data_instance)\n", "\n", "# Convert list of instances to DataFrame\n", "Se_data = pd.DataFrame(se_data_list)\n"]}, {"cell_type": "code", "execution_count": 9, "id": "7fa65c3d", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "id", "rawType": "object", "type": "string"}, {"name": "Formula", "rawType": "object", "type": "string"}, {"name": "Prototype", "rawType": "object", "type": "string"}, {"name": "Spacegroup", "rawType": "object", "type": "string"}, {"name": "bandgap", "rawType": "object", "type": "string"}, {"name": "a", "rawType": "float64", "type": "float"}, {"name": "b", "rawType": "float64", "type": "float"}, {"name": "c", "rawType": "float64", "type": "float"}], "ref": "c91e6202-74de-45ac-aa3c-da0344c96155", "rows": [["0", "f4b77a8985a043682c725e63f421f758", "Nb4Y4Se12", "PbPS3", "Pc (7)", "0.0", "7.090935748696196", "7.169160335029264", "9.978596603868109"], ["1", "b4c429ed0893a3e44817edfba8b2d3a9", "Hg4Ti4Se12", "PbPS3", "Pc (7)", "0.3210000000000002", "8.748563693791347", "7.280762420183364", "9.520715901045344"], ["2", "10977461a09584b20959551b3b1b9582", "Cu4P4Se12", "PbPS3", "Pc (7)", "0.0", "5.487558091545747", "7.303066389241667", "10.809002731203478"], ["3", "ffd2a63a336537c06b8ba774bfbfabf2", "Cd4V4Se12", "PbPS3", "Pc (7)", "0.0", "6.758794548561826", "6.186140439249461", "10.654759097885723"], ["4", "e277e96ee540e721b81cc53b0f9023cc", "In4P4Se12", "PbPS3", "Pc (7)", "0.8681000000000001", "7.128898832066998", "6.712444068763036", "12.839402816852145"], ["5", "7ec71be83134ad329a71b85b38758d46", "Al4Nb4Se12", "PbPS3", "Pc (7)", "0.0", "7.0403046305075305", "6.260059517039861", "11.266328201930108"], ["6", "8f4dd6678b35a7e17e92ff3c3a452bb9", "K4Sb4Se12", "PbPS3", "Pc (7)", "0.8815", "7.977737071419057", "8.199542088686865", "9.749160252813938"], ["7", "bfb71cc303ccebd559119b529c3f4fad", "Mg4Se12Si4", "PbPS3", "Pc (7)", "0.6815000000000002", "5.94529649099003", "6.701909634302029", "12.222841572318817"], ["8", "462b0c8407abaa41d674bb8fb08e9674", "Ni4Sc4Se12", "PbPS3", "Pc (7)", "0.0", "5.918055101897931", "6.559696403485651", "10.923525598623709"], ["9", "9fa045cb1eee9d23b7750023b06a42e7", "Mg4V4Se12", "PbPS3", "Pc (7)", "0.03390000000000004", "6.700362416561757", "6.474120721946134", "10.157848711287585"], ["10", "6d2cbeb62d192784bfbd5a68992218fe", "Pb4V4Se12", "PbPS3", "Pc (7)", "0.0", "7.493085085816872", "8.155796024660003", "8.823562855851815"], ["11", "ea399d7f3ec2e6e2eea031e228a03ea4", "Rb4Ta4Se12", "PbPS3", "Pc (7)", "0.0", "6.025833185264134", "7.2490171073989424", "12.663939012291117"], ["12", "450cf5f2618041d0beed405b2537810b", "Ni4Ge4Se12", "PbPS3", "Pc (7)", "0.0", "5.216630739399002", "6.721946654517005", "11.195344827796093"], ["13", "dd2394f3c6b98583c8bc3fa8494372b4", "Ba4Sn4Se12", "PbPS3", "Pc (7)", "0.6708000000000003", "6.725708657782441", "6.822201615654541", "12.937667506920752"], ["14", "2ea800ff602ef346ba7952981ecd1024", "Pd4Y4Se12", "PbPS3", "Pc (7)", "0.36579999999999924", "7.212920564237467", "8.145438916090626", "8.694779723473742"], ["15", "e99eb0f9fff3b81f38cf51d91c809fad", "Nb4B4Se12", "PbPS3", "Pc (7)", "0.0", "5.829807616453551", "6.634640346937229", "11.098043219745277"], ["16", "3f31c47662ac63a58bbe2b06a2e7fb6c", "Mg4Os4Se12", "PbPS3", "Pc (7)", "0.0", "5.534554926039575", "6.356393543916192", "12.160925716986055"], ["17", "4359b56f49a2e54827e53cd2142457fb", "Ag4As4Se12", "PbPS3", "Pc (7)", "0.33260000000000023", "6.467303186329503", "7.224629520187475", "10.264202949746084"], ["18", "8b4e7c13fbca9988f3783f2eb6205f9f", "Hg4Pb4Se12", "PbPS3", "Pc (7)", "0.7568999999999999", "7.26299078630701", "8.35648503166508", "10.805970236578814"], ["19", "407a286cbded757aa359273901fcc968", "Ru4Ti4Se12", "PbPS3", "Pc (7)", "0.0", "6.386100868863916", "7.317209396871882", "9.463752372773156"], ["20", "be3c07340fc473e810d1101e9c058b21", "In4Pd4Se12", "PbPS3", "Pc (7)", "0.0", "6.253767668324957", "6.359295636974736", "11.162406210219116"], ["21", "7b5334dc73a7521d7ba53fd5d805decd", "Bi4Sb4Se12", "PbPS3", "Pc (7)", "0.3830999999999998", "6.77356974998797", "7.221018162458289", "13.605502199383135"], ["22", "6bf9af3451253c6a890fcdd6518e704d", "Ag4Sb4Se12", "PbPS3", "Pc (7)", "0.0", "6.205222634336557", "7.823874612491389", "10.626942155906958"], ["23", "1b213d5d3f2705ee1da0b6b4a5336504", "Ni4Se12Te4", "PbPS3", "Pc (7)", "0.0", "6.44826593858895", "6.858004668591566", "10.690431166175921"], ["24", "51b97ae2cf75e37caf44946c240a7baa", "Ti4Zn4Se12", "PbPS3", "Pc (7)", "0.3855999999999997", "7.105051054200013", "6.658976808649699", "10.893447997580573"], ["25", "e113928bed018e12fa3e5e462a1acbba", "Ti4V4Se12", "PbPS3", "Pc (7)", "0.0", "6.198850520713006", "5.909018002489345", "10.516029170184272"], ["26", "8da1c53d743a9edc6848fd9bb43d2baf", "Sc4Y4Se12", "PbPS3", "Pc (7)", "1.2696", "7.218777769845006", "6.808712793321453", "11.711070971545656"], ["27", "861e4464919429ca332c4699420b057e", "Bi4Cs4Se12", "PbPS3", "Pc (7)", "0.7537000000000003", "7.596855796257734", "8.97253404848386", "11.392174485303169"], ["28", "c02196884d37377ac0efa7f6bbfcb292", "Al4Os4Se12", "PbPS3", "Pc (7)", "0.0", "5.951842943400844", "6.504930153847549", "10.93699320018485"], ["29", "65c27550d4e020ab49c120bd8ab1160c", "Pd4Se12Si4", "PbPS3", "Pc (7)", "0.0", "5.903981737721693", "7.184235314621789", "10.025206570916767"], ["30", "9881b36d862e471be950376e19bfc489", "Ir4Os4Se12", "PbPS3", "Pc (7)", "0.0", "5.718815143455775", "6.007779514417373", "10.497978267834284"], ["31", "e1829100e49417f85524089d4df39e14", "Os4B4Se12", "PbPS3", "Pc (7)", "0.0", "5.531624653964291", "6.149319905201911", "10.449590907086026"], ["32", "1321b0da13d0e1090de6f1b729e3cb2e", "Rh4Ru4Se12", "PbPS3", "Pc (7)", "0.0", "5.644580571773943", "6.0601293059814845", "10.445174186538123"], ["33", "c034b25a51b039a67a3d1023ccfb1bcc", "Sc8Se12", "PbPS3", "Pc (7)", "1.0054000000000003", "6.906845454338428", "6.48207290517763", "11.3057648142363"], ["34", "c645eb870721f62e9e9bb0373449275e", "Cu4Zr4Se12", "PbPS3", "Pc (7)", "0.4234", "7.07789271788896", "6.96477512178863", "9.435166498730156"], ["35", "797a9e7bf752bda910954cce759fbdb9", "Ag4P4Se12", "PbPS3", "Pc (7)", "0.3106", "5.85374484857291", "6.245890862963455", "13.471292679062193"], ["36", "c2f593bba6c618fe332efc5ffce9f87e", "Os4Ta4Se12", "PbPS3", "Pc (7)", "0.0", "6.876025813127588", "7.339576718674546", "9.118213630677896"], ["37", "a489160f049257254ca2fa44c2a92165", "Tl4As4Se12", "PbPS3", "Pc (7)", "0.8862000000000001", "8.02242433154351", "7.370527109386227", "9.743582379078317"], ["38", "fb0edfd0c5001670b6cc691a1b4932bf", "Sc4P4Se12", "PbPS3", "Pc (7)", "0.7530000000000001", "6.48056368768155", "7.089803235920416", "11.278120657006864"], ["39", "9695a57bfdfb4b76dfedaa5931812933", "Ta4P4Se12", "PbPS3", "Pc (7)", "0.28569999999999984", "5.915090843410837", "6.940334800979867", "11.386173113140142"], ["40", "a589b4140a32769ef17f421df2fbd48b", "Ta4As4Se12", "PbPS3", "Pc (7)", "0.2812999999999999", "6.832354240859147", "7.0434496426255375", "10.226355520978414"], ["41", "eb7e5d0be9adf524bddc29903208f516", "Ag4As4Se12", "PbPS3", "Pc (7)", "0.46609999999999996", "5.494726584974277", "8.109428007825917", "10.969409475146037"], ["42", "21cec280aa8e454a79f9db443bb2fd6c", "Cu4Pb4Se12", "PbPS3", "Pc (7)", "0.0", "5.827077447093149", "7.868808187074892", "10.114497664175605"], ["43", "567abc43cc024425e0cafd8879787c32", "La4Tl4Se12", "PbPS3", "Pc (7)", "0.39480000000000004", "7.4050777476588205", "8.231973504376572", "9.37348195235785"], ["44", "60b1d29921dcf9a204623140ff325744", "K4P4Se12", "PbPS3", "Pc (7)", "1.0799", "6.650440885119644", "7.757953178956284", "11.565509839391039"], ["45", "5b98770ca62125d9d555d238a028d147", "Sc4Ta4Se12", "PbPS3", "Pc (7)", "0.0", "6.60752882228773", "6.15872921671758", "10.939273079188325"], ["46", "d3bbaafd216c652ccf0158135930464e", "Ni4Rh4Se12", "PbPS3", "Pc (7)", "0.0", "6.457023575968614", "7.1220168464749", "9.692393053381304"], ["47", "6b2d9cc5e1deb5d25db0d927b63466b3", "Hf4Re4Se12", "PbPS3", "Pc (7)", "0.0", "7.28612355743761", "7.603185977434069", "8.037378821287342"], ["48", "95ca5f1849b0c6a2722530542f7ac5e6", "Hf4Ge4Se12", "PbPS3", "Pc (7)", "0.5653000000000001", "7.176318357517434", "6.456682041342512", "11.215234350708739"], ["49", "80187dbbb3d538cca7059bdde4956440", "Re4Ti4Se12", "PbPS3", "Pc (7)", "0.0", "6.726774561787725", "6.073327583474655", "10.85136173761645"]], "shape": {"columns": 8, "rows": 5040}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>Formula</th>\n", "      <th>Prototype</th>\n", "      <th>Spacegroup</th>\n", "      <th>bandgap</th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>f4b77a8985a043682c725e63f421f758</td>\n", "      <td>Nb4Y4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.0</td>\n", "      <td>7.090936</td>\n", "      <td>7.169160</td>\n", "      <td>9.978597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>b4c429ed0893a3e44817edfba8b2d3a9</td>\n", "      <td>Hg4Ti4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.3210000000000002</td>\n", "      <td>8.748564</td>\n", "      <td>7.280762</td>\n", "      <td>9.520716</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10977461a09584b20959551b3b1b9582</td>\n", "      <td>Cu4P4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.0</td>\n", "      <td>5.487558</td>\n", "      <td>7.303066</td>\n", "      <td>10.809003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ffd2a63a336537c06b8ba774bfbfabf2</td>\n", "      <td>Cd4V4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.0</td>\n", "      <td>6.758795</td>\n", "      <td>6.186140</td>\n", "      <td>10.654759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>e277e96ee540e721b81cc53b0f9023cc</td>\n", "      <td>In4P4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.8681000000000001</td>\n", "      <td>7.128899</td>\n", "      <td>6.712444</td>\n", "      <td>12.839403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5035</th>\n", "      <td>2ad52e77a520d7fab562713c09cae02d</td>\n", "      <td>Os2Tl2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>7.152269</td>\n", "      <td>7.124567</td>\n", "      <td>6.195575</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5036</th>\n", "      <td>865f9991538c6f68d480d081dda5fb7b</td>\n", "      <td>Ru2Y2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>6.862196</td>\n", "      <td>6.362454</td>\n", "      <td>7.071562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5037</th>\n", "      <td>bc18ce034a8ae673035436c3be858a71</td>\n", "      <td>Al2Ta2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>5.918799</td>\n", "      <td>6.398925</td>\n", "      <td>7.495746</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5038</th>\n", "      <td>ec3d53e121b24244a2c0b86c1c405a86</td>\n", "      <td>Ir2Nb2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>6.634771</td>\n", "      <td>6.601556</td>\n", "      <td>5.850766</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5039</th>\n", "      <td>8a3a14dd1b71bccf33fd274b526f1bf6</td>\n", "      <td>Bi2Pd2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>7.290572</td>\n", "      <td>7.241057</td>\n", "      <td>5.958044</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5040 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                                    id     Formula Prototype Spacegroup  \\\n", "0     f4b77a8985a043682c725e63f421f758   Nb4Y4Se12     PbPS3     Pc (7)   \n", "1     b4c429ed0893a3e44817edfba8b2d3a9  Hg4Ti4Se12     PbPS3     Pc (7)   \n", "2     10977461a09584b20959551b3b1b9582   Cu4P4Se12     PbPS3     Pc (7)   \n", "3     ffd2a63a336537c06b8ba774bfbfabf2   Cd4V4Se12     PbPS3     Pc (7)   \n", "4     e277e96ee540e721b81cc53b0f9023cc   In4P4Se12     PbPS3     Pc (7)   \n", "...                                ...         ...       ...        ...   \n", "5035  2ad52e77a520d7fab562713c09cae02d   Os2Tl2Se6     FePS3  C2/m (12)   \n", "5036  865f9991538c6f68d480d081dda5fb7b    Ru2Y2Se6     FePS3  C2/m (12)   \n", "5037  bc18ce034a8ae673035436c3be858a71   Al2Ta2Se6     FePS3  C2/m (12)   \n", "5038  ec3d53e121b24244a2c0b86c1c405a86   Ir2Nb2Se6     FePS3  C2/m (12)   \n", "5039  8a3a14dd1b71bccf33fd274b526f1bf6   Bi2Pd2Se6     FePS3  C2/m (12)   \n", "\n", "                 bandgap         a         b          c  \n", "0                    0.0  7.090936  7.169160   9.978597  \n", "1     0.3210000000000002  8.748564  7.280762   9.520716  \n", "2                    0.0  5.487558  7.303066  10.809003  \n", "3                    0.0  6.758795  6.186140  10.654759  \n", "4     0.8681000000000001  7.128899  6.712444  12.839403  \n", "...                  ...       ...       ...        ...  \n", "5035                 0.0  7.152269  7.124567   6.195575  \n", "5036                 0.0  6.862196  6.362454   7.071562  \n", "5037                 0.0  5.918799  6.398925   7.495746  \n", "5038                 0.0  6.634771  6.601556   5.850766  \n", "5039                 0.0  7.290572  7.241057   5.958044  \n", "\n", "[5040 rows x 8 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Split lengths into separate columns\n", "Se_data[['a', 'b', 'c']] = pd.DataFrame(Se_data.lengths.tolist(), index=Se_data.index)\n", "Se_data.drop('lengths', axis=1, inplace=True)\n", "Se_data\n"]}, {"cell_type": "code", "execution_count": 10, "id": "ca0b607b", "metadata": {}, "outputs": [], "source": ["from ase.db import connect\n", "Se_db=connect('abse3.db')"]}, {"cell_type": "code", "execution_count": 11, "id": "ceb48d49", "metadata": {}, "outputs": [], "source": ["store=[]\n", "obj={}\n", "for entry in Se_db.select():\n", "    obj={}\n", "    obj['elements']=list(entry.count_atoms())\n", "    obj['id']=entry.unique_id\n", "    store.append(obj)\n", "Se_data_from_db=pd.DataFrame(store)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "1bda66cc", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "elements", "rawType": "object", "type": "unknown"}, {"name": "id", "rawType": "object", "type": "string"}], "ref": "e6e91f43-e3bd-41fb-a62c-96ece5a2b701", "rows": [["0", "['B', 'P', 'Se']", "2b3ab77d38ce77d41973c86f5493c5a4"], ["1", "['P', 'Rh', 'Se']", "acfe602dde004adfe28cc5110225f2d8"], ["2", "['Bi', 'P', 'Se']", "3c7ab78725b4272fb4259e657694a8d4"], ["3", "['P', 'Au', 'Se']", "1366f01f99c0d7b8148fe7ef09949448"], ["4", "['P', 'Ga', 'Se']", "42d8d887487aeb98e058e00b68e83193"], ["5", "['Ta', 'P', 'Se']", "ef87a908e3a28046906fada3437336f7"], ["6", "['P', 'Cu', 'Se']", "15190eea96f8b17ae834cec2dca26f0c"], ["7", "['Rb', 'P', 'Se']", "73a5707b55a13d374093227c4f96a683"], ["8", "['P', 'V', 'Se']", "c7ae024b3f4300b934f99ab1a4ede998"], ["9", "['Ni', 'P', 'Se']", "5b59ca0e5536d31c096aa248cb2682c4"], ["10", "['Os', 'P', 'Se']", "965dcd83c4458ed8c5c9250d7e4bb10f"], ["11", "['P', 'Nb', 'Se']", "69aa4b76ed0d7569ed3f20ec3ef57b9f"], ["12", "['Na', 'P', 'Se']", "9c7b2c3cc8a131ef0d151fe7aaba4664"], ["13", "['P', 'Ag', 'Se']", "46008c9772ebf81bde4debdd294c65ea"], ["14", "['P', 'Y', 'Se']", "1b42ddaeb78542c1d3dabb74206abeb1"], ["15", "['Rh', 'P', 'Se']", "5ffc3d9ec3909e869c735a516aff2e69"], ["16", "['Sc', 'P', 'Se']", "73c5ed931e14c53b57cfed39feb59f44"], ["17", "['Ga', 'P', 'Se']", "687b20a6505bb454a28f49f664d833c0"], ["18", "['Ti', 'P', 'Se']", "a3c27cafd1cc09da591862863b6f2600"], ["19", "['In', 'P', 'Se']", "cb6f3be50c2fe5f87be0147731a45bd1"], ["20", "['Ru', 'P', 'Se']", "97ecb29cc4f6da9366b0a574dca34c73"], ["21", "['P', 'In', 'Se']", "b347f851b584276fdfe15bef70b11dc0"], ["22", "['Au', 'P', 'Se']", "832fcbdf6823c8b4c1a409ef2c11bba2"], ["23", "['P', '<PERSON>', 'Se']", "b65a9f38006034624c633b2a6b27b427"], ["24", "['Nb', 'P', 'Se']", "ef5f6b6c94e278b0b469e6b4ad6b6570"], ["25", "['K', 'P', 'Se']", "3c89c1f976728ea0b2a1f3b0aec2daee"], ["26", "['La', 'P', 'Se']", "d85861e50e01569b9f449f98a5f7cfdb"], ["27", "['Y', 'P', 'Se']", "d9cdfcb9acf1847aeaafdc8d2e43516a"], ["28", "['P', 'O<PERSON>', 'Se']", "f7994552860584eb2e2519c3b694d8d4"], ["29", "['P', 'Li', 'Se']", "65aa4934b475cf6cd53d45e2cdd76fa0"], ["30", "['Tl', 'P', 'Se']", "7852fb8b0785284df989b7ccec3d0eee"], ["31", "['P', 'As', 'Se']", "ecf941a48da8cf0a9d98bcac2e85ceeb"], ["32", "['P', 'Ta', 'Se']", "42f1ca2fa379f6831e7c0a57b950029e"], ["33", "['P', 'Sc', 'Se']", "5e903342433c690a87c869d0f2df65a9"], ["34", "['P', 'Cs', 'Se']", "70b40e68ed38c11488a2911e797a49fb"], ["35", "['P', 'B', 'Se']", "5b8f07ba2aa7da9c854055320c2a6284"], ["36", "['P', 'Ni', 'Se']", "f2c941b02f6f8ba529562a3bd986e6a4"], ["37", "['P', 'Se']", "ec347e9737cbdccc1994d5b56d99fdb2"], ["38", "['Al', 'P', 'Se']", "ca697fa006e6db3ef7f8e242c863f5e8"], ["39", "['Ag', 'P', 'Se']", "4a4e3bec95fcbb2bae976446bd89d660"], ["40", "['Cu', 'P', 'Se']", "989f1ebcfe8d9e085df82a0fb3fe4c09"], ["41", "['Ir', 'P', 'Se']", "804a6e3b0aa2cccb98f5a8dc256b6e40"], ["42", "['P', 'K', 'Se']", "ce0f1545b1e500cd101fc2035d58194b"], ["43", "['P', 'Ru', 'Se']", "8e30f20045faf86a03fab1d8bb264b65"], ["44", "['Sb', 'P', 'Se']", "b997bfb0e1581f268c6a4cc06fee3d9c"], ["45", "['As', 'P', 'Se']", "5cfaa8e390b0f31e87fe1b1a9613e6d3"], ["46", "['P', 'La', 'Se']", "f950bf69106884cc531e84b1e432b3df"], ["47", "['Li', 'P', 'Se']", "71033dea650a28e8bdbf41f017a1134c"], ["48", "['Pd', 'P', 'Se']", "8790d61dcb048667636a9ae25cb27f3b"], ["49", "['Cs', 'P', 'Se']", "6a9a49a305102ae9e5ed16cdc054fe75"]], "shape": {"columns": 2, "rows": 5976}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>elements</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[B, P, Se]</td>\n", "      <td>2b3ab77d38ce77d41973c86f5493c5a4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[P, Rh, Se]</td>\n", "      <td>acfe602dde004adfe28cc5110225f2d8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[Bi, P, Se]</td>\n", "      <td>3c7ab78725b4272fb4259e657694a8d4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>[P, Au, Se]</td>\n", "      <td>1366f01f99c0d7b8148fe7ef09949448</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>[P, Ga, Se]</td>\n", "      <td>42d8d887487aeb98e058e00b68e83193</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5971</th>\n", "      <td>[Hg, Ir, Se]</td>\n", "      <td>30908c86b5cc1a808a7873dc9dbf3eaa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5972</th>\n", "      <td>[Sn, Hg, Se]</td>\n", "      <td>f9c573a97bfa07e5ed6b5d7bb183205f</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5973</th>\n", "      <td>[Hg, Ge, Se]</td>\n", "      <td>a94bde7423896546c7e50e4d7329e1df</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5974</th>\n", "      <td>[Y, As, Se]</td>\n", "      <td>29dcf7fdb3d363561f2537f975dedfcf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5975</th>\n", "      <td>[Si, Te, Se]</td>\n", "      <td>a3d08b3a87455c7dc1f084b09b78d942</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5976 rows × 2 columns</p>\n", "</div>"], "text/plain": ["          elements                                id\n", "0       [B, P, Se]  2b3ab77d38ce77d41973c86f5493c5a4\n", "1      [P, Rh, Se]  acfe602dde004adfe28cc5110225f2d8\n", "2      [Bi, P, Se]  3c7ab78725b4272fb4259e657694a8d4\n", "3      [P, <PERSON>, Se]  1366f01f99c0d7b8148fe7ef09949448\n", "4      [<PERSON>, <PERSON><PERSON>, <PERSON>]  42d8d887487aeb98e058e00b68e83193\n", "...            ...                               ...\n", "5971  [Hg, <PERSON>r, Se]  30908c86b5cc1a808a7873dc9dbf3eaa\n", "5972  [Sn, Hg, Se]  f9c573a97bfa07e5ed6b5d7bb183205f\n", "5973  [Hg, <PERSON><PERSON>, <PERSON>]  a94bde7423896546c7e50e4d7329e1df\n", "5974   [Y, <PERSON>, <PERSON>]  29dcf7fdb3d363561f2537f975dedfcf\n", "5975  [<PERSON>, <PERSON>, <PERSON>]  a3d08b3a87455c7dc1f084b09b78d942\n", "\n", "[5976 rows x 2 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["Se_data_from_db"]}, {"cell_type": "code", "execution_count": 13, "id": "fd357006", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "id", "rawType": "object", "type": "string"}, {"name": "Formula", "rawType": "object", "type": "string"}, {"name": "Prototype", "rawType": "object", "type": "string"}, {"name": "Spacegroup", "rawType": "object", "type": "string"}, {"name": "bandgap", "rawType": "object", "type": "string"}, {"name": "a", "rawType": "float64", "type": "float"}, {"name": "b", "rawType": "float64", "type": "float"}, {"name": "c", "rawType": "float64", "type": "float"}, {"name": "elements", "rawType": "object", "type": "unknown"}, {"name": "A", "rawType": "object", "type": "string"}, {"name": "B", "rawType": "object", "type": "string"}, {"name": "C", "rawType": "object", "type": "unknown"}], "ref": "73f99c5e-ef4e-4f4d-a638-ef0a309916bc", "rows": [["0", "f4b77a8985a043682c725e63f421f758", "Nb4Y4Se12", "PbPS3", "Pc (7)", "0.0", "7.090935748696196", "7.169160335029264", "9.978596603868109", "['Nb', 'Y', 'Se']", "Nb", "Y", "Se"], ["1", "b4c429ed0893a3e44817edfba8b2d3a9", "Hg4Ti4Se12", "PbPS3", "Pc (7)", "0.3210000000000002", "8.748563693791347", "7.280762420183364", "9.520715901045344", "['Hg', 'Ti', 'Se']", "Hg", "Ti", "Se"], ["2", "10977461a09584b20959551b3b1b9582", "Cu4P4Se12", "PbPS3", "Pc (7)", "0.0", "5.487558091545747", "7.303066389241667", "10.809002731203478", "['Cu', 'P', 'Se']", "<PERSON><PERSON>", "P", "Se"], ["3", "ffd2a63a336537c06b8ba774bfbfabf2", "Cd4V4Se12", "PbPS3", "Pc (7)", "0.0", "6.758794548561826", "6.186140439249461", "10.654759097885723", "['Cd', 'V', 'Se']", "Cd", "V", "Se"], ["4", "e277e96ee540e721b81cc53b0f9023cc", "In4P4Se12", "PbPS3", "Pc (7)", "0.8681000000000001", "7.128898832066998", "6.712444068763036", "12.839402816852145", "['In', 'P', 'Se']", "In", "P", "Se"], ["5", "7ec71be83134ad329a71b85b38758d46", "Al4Nb4Se12", "PbPS3", "Pc (7)", "0.0", "7.0403046305075305", "6.260059517039861", "11.266328201930108", "['Nb', 'Al', 'Se']", "Nb", "Al", "Se"], ["6", "8f4dd6678b35a7e17e92ff3c3a452bb9", "K4Sb4Se12", "PbPS3", "Pc (7)", "0.8815", "7.977737071419057", "8.199542088686865", "9.749160252813938", "['K', 'Sb', 'Se']", "K", "Sb", "Se"], ["7", "bfb71cc303ccebd559119b529c3f4fad", "Mg4Se12Si4", "PbPS3", "Pc (7)", "0.6815000000000002", "5.94529649099003", "6.701909634302029", "12.222841572318817", "['Mg', 'Si', 'Se']", "Mg", "Si", "Se"], ["8", "462b0c8407abaa41d674bb8fb08e9674", "Ni4Sc4Se12", "PbPS3", "Pc (7)", "0.0", "5.918055101897931", "6.559696403485651", "10.923525598623709", "['Sc', 'Ni', 'Se']", "Sc", "<PERSON>", "Se"], ["9", "9fa045cb1eee9d23b7750023b06a42e7", "Mg4V4Se12", "PbPS3", "Pc (7)", "0.03390000000000004", "6.700362416561757", "6.474120721946134", "10.157848711287585", "['V', 'Mg', 'Se']", "V", "Mg", "Se"], ["10", "6d2cbeb62d192784bfbd5a68992218fe", "Pb4V4Se12", "PbPS3", "Pc (7)", "0.0", "7.493085085816872", "8.155796024660003", "8.823562855851815", "['Pb', 'V', 'Se']", "Pb", "V", "Se"], ["11", "ea399d7f3ec2e6e2eea031e228a03ea4", "Rb4Ta4Se12", "PbPS3", "Pc (7)", "0.0", "6.025833185264134", "7.2490171073989424", "12.663939012291117", "['Rb', 'Ta', 'Se']", "Rb", "Ta", "Se"], ["12", "450cf5f2618041d0beed405b2537810b", "Ni4Ge4Se12", "PbPS3", "Pc (7)", "0.0", "5.216630739399002", "6.721946654517005", "11.195344827796093", "['Ge', 'Ni', 'Se']", "Ge", "<PERSON>", "Se"], ["13", "dd2394f3c6b98583c8bc3fa8494372b4", "Ba4Sn4Se12", "PbPS3", "Pc (7)", "0.6708000000000003", "6.725708657782441", "6.822201615654541", "12.937667506920752", "['Sn', 'Ba', 'Se']", "Sn", "Ba", "Se"], ["14", "2ea800ff602ef346ba7952981ecd1024", "Pd4Y4Se12", "PbPS3", "Pc (7)", "0.36579999999999924", "7.212920564237467", "8.145438916090626", "8.694779723473742", "['Pd', 'Y', 'Se']", "Pd", "Y", "Se"], ["15", "e99eb0f9fff3b81f38cf51d91c809fad", "Nb4B4Se12", "PbPS3", "Pc (7)", "0.0", "5.829807616453551", "6.634640346937229", "11.098043219745277", "['B', 'Nb', 'Se']", "B", "Nb", "Se"], ["16", "3f31c47662ac63a58bbe2b06a2e7fb6c", "Mg4Os4Se12", "PbPS3", "Pc (7)", "0.0", "5.534554926039575", "6.356393543916192", "12.160925716986055", "['Mg', 'O<PERSON>', 'Se']", "Mg", "<PERSON><PERSON>", "Se"], ["17", "4359b56f49a2e54827e53cd2142457fb", "Ag4As4Se12", "PbPS3", "Pc (7)", "0.33260000000000023", "6.467303186329503", "7.224629520187475", "10.264202949746084", "['Ag', 'As', 'Se']", "Ag", "As", "Se"], ["18", "8b4e7c13fbca9988f3783f2eb6205f9f", "Hg4Pb4Se12", "PbPS3", "Pc (7)", "0.7568999999999999", "7.26299078630701", "8.35648503166508", "10.805970236578814", "['Hg', 'Pb', 'Se']", "Hg", "Pb", "Se"], ["19", "407a286cbded757aa359273901fcc968", "Ru4Ti4Se12", "PbPS3", "Pc (7)", "0.0", "6.386100868863916", "7.317209396871882", "9.463752372773156", "['Ru', 'Ti', 'Se']", "<PERSON><PERSON>", "Ti", "Se"], ["20", "be3c07340fc473e810d1101e9c058b21", "In4Pd4Se12", "PbPS3", "Pc (7)", "0.0", "6.253767668324957", "6.359295636974736", "11.162406210219116", "['Pd', 'In', 'Se']", "Pd", "In", "Se"], ["21", "7b5334dc73a7521d7ba53fd5d805decd", "Bi4Sb4Se12", "PbPS3", "Pc (7)", "0.3830999999999998", "6.77356974998797", "7.221018162458289", "13.605502199383135", "['Sb', 'Bi', 'Se']", "Sb", "Bi", "Se"], ["22", "6bf9af3451253c6a890fcdd6518e704d", "Ag4Sb4Se12", "PbPS3", "Pc (7)", "0.0", "6.205222634336557", "7.823874612491389", "10.626942155906958", "['Ag', 'Sb', 'Se']", "Ag", "Sb", "Se"], ["23", "1b213d5d3f2705ee1da0b6b4a5336504", "Ni4Se12Te4", "PbPS3", "Pc (7)", "0.0", "6.44826593858895", "6.858004668591566", "10.690431166175921", "['Te', 'Ni', 'Se']", "Te", "<PERSON>", "Se"], ["24", "51b97ae2cf75e37caf44946c240a7baa", "Ti4Zn4Se12", "PbPS3", "Pc (7)", "0.3855999999999997", "7.105051054200013", "6.658976808649699", "10.893447997580573", "['Ti', 'Zn', 'Se']", "Ti", "Zn", "Se"], ["25", "e113928bed018e12fa3e5e462a1acbba", "Ti4V4Se12", "PbPS3", "Pc (7)", "0.0", "6.198850520713006", "5.909018002489345", "10.516029170184272", "['Ti', 'V', 'Se']", "Ti", "V", "Se"], ["26", "8da1c53d743a9edc6848fd9bb43d2baf", "Sc4Y4Se12", "PbPS3", "Pc (7)", "1.2696", "7.218777769845006", "6.808712793321453", "11.711070971545656", "['Y', 'Sc', 'Se']", "Y", "Sc", "Se"], ["27", "861e4464919429ca332c4699420b057e", "Bi4Cs4Se12", "PbPS3", "Pc (7)", "0.7537000000000003", "7.596855796257734", "8.97253404848386", "11.392174485303169", "['Cs', 'Bi', 'Se']", "Cs", "Bi", "Se"], ["28", "c02196884d37377ac0efa7f6bbfcb292", "Al4Os4Se12", "PbPS3", "Pc (7)", "0.0", "5.951842943400844", "6.504930153847549", "10.93699320018485", "['<PERSON>', '<PERSON><PERSON>', '<PERSON>']", "Al", "<PERSON><PERSON>", "Se"], ["29", "65c27550d4e020ab49c120bd8ab1160c", "Pd4Se12Si4", "PbPS3", "Pc (7)", "0.0", "5.903981737721693", "7.184235314621789", "10.025206570916767", "['Pd', 'Si', 'Se']", "Pd", "Si", "Se"], ["30", "9881b36d862e471be950376e19bfc489", "Ir4Os4Se12", "PbPS3", "Pc (7)", "0.0", "5.718815143455775", "6.007779514417373", "10.497978267834284", "['Os', 'Ir', 'Se']", "<PERSON><PERSON>", "<PERSON>r", "Se"], ["31", "e1829100e49417f85524089d4df39e14", "Os4B4Se12", "PbPS3", "Pc (7)", "0.0", "5.531624653964291", "6.149319905201911", "10.449590907086026", "['B', 'Os', 'Se']", "B", "<PERSON><PERSON>", "Se"], ["32", "1321b0da13d0e1090de6f1b729e3cb2e", "Rh4Ru4Se12", "PbPS3", "Pc (7)", "0.0", "5.644580571773943", "6.0601293059814845", "10.445174186538123", "['Ru', 'Rh', 'Se']", "<PERSON><PERSON>", "Rh", "Se"], ["33", "c034b25a51b039a67a3d1023ccfb1bcc", "Sc8Se12", "PbPS3", "Pc (7)", "1.0054000000000003", "6.906845454338428", "6.48207290517763", "11.3057648142363", "['Sc', 'Se']", "Sc", "Se", null], ["34", "c645eb870721f62e9e9bb0373449275e", "Cu4Zr4Se12", "PbPS3", "Pc (7)", "0.4234", "7.07789271788896", "6.96477512178863", "9.435166498730156", "['Cu', 'Zr', 'Se']", "<PERSON><PERSON>", "Zr", "Se"], ["35", "797a9e7bf752bda910954cce759fbdb9", "Ag4P4Se12", "PbPS3", "Pc (7)", "0.3106", "5.85374484857291", "6.245890862963455", "13.471292679062193", "['Ag', 'P', 'Se']", "Ag", "P", "Se"], ["36", "c2f593bba6c618fe332efc5ffce9f87e", "Os4Ta4Se12", "PbPS3", "Pc (7)", "0.0", "6.876025813127588", "7.339576718674546", "9.118213630677896", "['Os', '<PERSON>', 'Se']", "<PERSON><PERSON>", "Ta", "Se"], ["37", "a489160f049257254ca2fa44c2a92165", "Tl4As4Se12", "PbPS3", "Pc (7)", "0.8862000000000001", "8.02242433154351", "7.370527109386227", "9.743582379078317", "['As', 'Tl', 'Se']", "As", "Tl", "Se"], ["38", "fb0edfd0c5001670b6cc691a1b4932bf", "Sc4P4Se12", "PbPS3", "Pc (7)", "0.7530000000000001", "6.48056368768155", "7.089803235920416", "11.278120657006864", "['Sc', 'P', 'Se']", "Sc", "P", "Se"], ["39", "9695a57bfdfb4b76dfedaa5931812933", "Ta4P4Se12", "PbPS3", "Pc (7)", "0.28569999999999984", "5.915090843410837", "6.940334800979867", "11.386173113140142", "['P', 'Ta', 'Se']", "P", "Ta", "Se"], ["40", "a589b4140a32769ef17f421df2fbd48b", "Ta4As4Se12", "PbPS3", "Pc (7)", "0.2812999999999999", "6.832354240859147", "7.0434496426255375", "10.226355520978414", "['Ta', 'As', 'Se']", "Ta", "As", "Se"], ["41", "eb7e5d0be9adf524bddc29903208f516", "Ag4As4Se12", "PbPS3", "Pc (7)", "0.46609999999999996", "5.494726584974277", "8.109428007825917", "10.969409475146037", "['As', 'Ag', 'Se']", "As", "Ag", "Se"], ["42", "21cec280aa8e454a79f9db443bb2fd6c", "Cu4Pb4Se12", "PbPS3", "Pc (7)", "0.0", "5.827077447093149", "7.868808187074892", "10.114497664175605", "['Cu', 'Pb', 'Se']", "<PERSON><PERSON>", "Pb", "Se"], ["43", "567abc43cc024425e0cafd8879787c32", "La4Tl4Se12", "PbPS3", "Pc (7)", "0.39480000000000004", "7.4050777476588205", "8.231973504376572", "9.37348195235785", "['Tl', 'La', 'Se']", "Tl", "La", "Se"], ["44", "60b1d29921dcf9a204623140ff325744", "K4P4Se12", "PbPS3", "Pc (7)", "1.0799", "6.650440885119644", "7.757953178956284", "11.565509839391039", "['K', 'P', 'Se']", "K", "P", "Se"], ["45", "5b98770ca62125d9d555d238a028d147", "Sc4Ta4Se12", "PbPS3", "Pc (7)", "0.0", "6.60752882228773", "6.15872921671758", "10.939273079188325", "['Sc', 'Ta', 'Se']", "Sc", "Ta", "Se"], ["46", "d3bbaafd216c652ccf0158135930464e", "Ni4Rh4Se12", "PbPS3", "Pc (7)", "0.0", "6.457023575968614", "7.1220168464749", "9.692393053381304", "['Ni', 'Rh', 'Se']", "<PERSON>", "Rh", "Se"], ["47", "6b2d9cc5e1deb5d25db0d927b63466b3", "Hf4Re4Se12", "PbPS3", "Pc (7)", "0.0", "7.28612355743761", "7.603185977434069", "8.037378821287342", "['Re', 'Hf', 'Se']", "Re", "Hf", "Se"], ["48", "95ca5f1849b0c6a2722530542f7ac5e6", "Hf4Ge4Se12", "PbPS3", "Pc (7)", "0.5653000000000001", "7.176318357517434", "6.456682041342512", "11.215234350708739", "['Hf', 'Ge', 'Se']", "Hf", "Ge", "Se"], ["49", "80187dbbb3d538cca7059bdde4956440", "Re4Ti4Se12", "PbPS3", "Pc (7)", "0.0", "6.726774561787725", "6.073327583474655", "10.85136173761645", "['Ti', 'Re', 'Se']", "Ti", "Re", "Se"]], "shape": {"columns": 12, "rows": 5040}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>Formula</th>\n", "      <th>Prototype</th>\n", "      <th>Spacegroup</th>\n", "      <th>bandgap</th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>elements</th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>f4b77a8985a043682c725e63f421f758</td>\n", "      <td>Nb4Y4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.0</td>\n", "      <td>7.090936</td>\n", "      <td>7.169160</td>\n", "      <td>9.978597</td>\n", "      <td>[Nb, Y, Se]</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>b4c429ed0893a3e44817edfba8b2d3a9</td>\n", "      <td>Hg4Ti4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.3210000000000002</td>\n", "      <td>8.748564</td>\n", "      <td>7.280762</td>\n", "      <td>9.520716</td>\n", "      <td>[Hg, Ti, Se]</td>\n", "      <td>Hg</td>\n", "      <td>Ti</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10977461a09584b20959551b3b1b9582</td>\n", "      <td>Cu4P4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.0</td>\n", "      <td>5.487558</td>\n", "      <td>7.303066</td>\n", "      <td>10.809003</td>\n", "      <td>[Cu, P, Se]</td>\n", "      <td>Cu</td>\n", "      <td>P</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ffd2a63a336537c06b8ba774bfbfabf2</td>\n", "      <td>Cd4V4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.0</td>\n", "      <td>6.758795</td>\n", "      <td>6.186140</td>\n", "      <td>10.654759</td>\n", "      <td>[Cd, V, Se]</td>\n", "      <td>Cd</td>\n", "      <td>V</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>e277e96ee540e721b81cc53b0f9023cc</td>\n", "      <td>In4P4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>Pc (7)</td>\n", "      <td>0.8681000000000001</td>\n", "      <td>7.128899</td>\n", "      <td>6.712444</td>\n", "      <td>12.839403</td>\n", "      <td>[In, P, Se]</td>\n", "      <td>In</td>\n", "      <td>P</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5035</th>\n", "      <td>2ad52e77a520d7fab562713c09cae02d</td>\n", "      <td>Os2Tl2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>7.152269</td>\n", "      <td>7.124567</td>\n", "      <td>6.195575</td>\n", "      <td>[Tl, <PERSON><PERSON>, <PERSON>]</td>\n", "      <td>Tl</td>\n", "      <td>Os</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5036</th>\n", "      <td>865f9991538c6f68d480d081dda5fb7b</td>\n", "      <td>Ru2Y2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>6.862196</td>\n", "      <td>6.362454</td>\n", "      <td>7.071562</td>\n", "      <td>[Y, Ru, Se]</td>\n", "      <td>Y</td>\n", "      <td>Ru</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5037</th>\n", "      <td>bc18ce034a8ae673035436c3be858a71</td>\n", "      <td>Al2Ta2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>5.918799</td>\n", "      <td>6.398925</td>\n", "      <td>7.495746</td>\n", "      <td>[<PERSON>, <PERSON>, <PERSON>]</td>\n", "      <td>Ta</td>\n", "      <td>Al</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5038</th>\n", "      <td>ec3d53e121b24244a2c0b86c1c405a86</td>\n", "      <td>Ir2Nb2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>6.634771</td>\n", "      <td>6.601556</td>\n", "      <td>5.850766</td>\n", "      <td>[Ir, Nb, Se]</td>\n", "      <td>Ir</td>\n", "      <td>Nb</td>\n", "      <td>Se</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5039</th>\n", "      <td>8a3a14dd1b71bccf33fd274b526f1bf6</td>\n", "      <td>Bi2Pd2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>C2/m (12)</td>\n", "      <td>0.0</td>\n", "      <td>7.290572</td>\n", "      <td>7.241057</td>\n", "      <td>5.958044</td>\n", "      <td>[Bi, Pd, Se]</td>\n", "      <td>Bi</td>\n", "      <td>Pd</td>\n", "      <td>Se</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5040 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                                    id     Formula Prototype Spacegroup  \\\n", "0     f4b77a8985a043682c725e63f421f758   Nb4Y4Se12     PbPS3     Pc (7)   \n", "1     b4c429ed0893a3e44817edfba8b2d3a9  Hg4Ti4Se12     PbPS3     Pc (7)   \n", "2     10977461a09584b20959551b3b1b9582   Cu4P4Se12     PbPS3     Pc (7)   \n", "3     ffd2a63a336537c06b8ba774bfbfabf2   Cd4V4Se12     PbPS3     Pc (7)   \n", "4     e277e96ee540e721b81cc53b0f9023cc   In4P4Se12     PbPS3     Pc (7)   \n", "...                                ...         ...       ...        ...   \n", "5035  2ad52e77a520d7fab562713c09cae02d   Os2Tl2Se6     FePS3  C2/m (12)   \n", "5036  865f9991538c6f68d480d081dda5fb7b    Ru2Y2Se6     FePS3  C2/m (12)   \n", "5037  bc18ce034a8ae673035436c3be858a71   Al2Ta2Se6     FePS3  C2/m (12)   \n", "5038  ec3d53e121b24244a2c0b86c1c405a86   Ir2Nb2Se6     FePS3  C2/m (12)   \n", "5039  8a3a14dd1b71bccf33fd274b526f1bf6   Bi2Pd2Se6     FePS3  C2/m (12)   \n", "\n", "                 bandgap         a         b          c      elements   A   B  \\\n", "0                    0.0  7.090936  7.169160   9.978597   [Nb, Y, Se]  Nb   Y   \n", "1     0.3210000000000002  8.748564  7.280762   9.520716  [Hg, Ti, Se]  Hg  Ti   \n", "2                    0.0  5.487558  7.303066  10.809003   [<PERSON><PERSON>, <PERSON>, <PERSON>]  Cu   P   \n", "3                    0.0  6.758795  6.186140  10.654759   [Cd, V, Se]  Cd   V   \n", "4     0.8681000000000001  7.128899  6.712444  12.839403   [In, P, Se]  In   P   \n", "...                  ...       ...       ...        ...           ...  ..  ..   \n", "5035                 0.0  7.152269  7.124567   6.195575  [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>]  Tl  Os   \n", "5036                 0.0  6.862196  6.362454   7.071562   [Y, <PERSON><PERSON>, <PERSON>]   Y  Ru   \n", "5037                 0.0  5.918799  6.398925   7.495746  [<PERSON>, Al, Se]  Ta  Al   \n", "5038                 0.0  6.634771  6.601556   5.850766  [Ir, Nb, Se]  Ir  Nb   \n", "5039                 0.0  7.290572  7.241057   5.958044  [Bi, Pd, Se]  Bi  Pd   \n", "\n", "       C  \n", "0     Se  \n", "1     Se  \n", "2     Se  \n", "3     Se  \n", "4     Se  \n", "...   ..  \n", "5035  Se  \n", "5036  Se  \n", "5037  Se  \n", "5038  Se  \n", "5039  Se  \n", "\n", "[5040 rows x 12 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["Se_data = Se_data.merge(Se_data_from_db, on='id')\n", "\n", "# Create new columns A, B, C from the 'elements' column\n", "Se_data[['A', 'B', 'C']] = pd.DataFrame(Se_data['elements'].tolist(), index=Se_data.index)\n", "\n", "# Display the dataframe\n", "Se_data"]}, {"cell_type": "code", "execution_count": 14, "id": "1dea780e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ag4Bi4S12\n", "NH4CdCl3/Sn2S3\n", "0.3787571199\n"]}, {"data": {"text/plain": ["array([ 8.43995498,  4.06260537, 15.01996213])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from ase.db import connect\n", "db=connect('abs3.db')\n", "row=db.get(1)\n", "print(row.formula)\n", "print(row.prototype)\n", "print(row.PBEsol_gap)\n", "atoms=row.toatoms()\n", "lattice_params=atoms.get_cell().lengths()\n", "lattice_params"]}, {"cell_type": "code", "execution_count": 15, "id": "27d9f6b7", "metadata": {}, "outputs": [], "source": ["# Initialize empty lists to store data\n", "materials = []\n", "prototypes = []\n", "gaps = []\n", "a_params = []\n", "b_params = []\n", "c_params = []\n", "\n", "# Collect data from database\n", "for row in db.select():\n", "    try:\n", "        # Get basic properties\n", "        prototypes.append(row.prototype)\n", "        gaps.append(row.PBEsol_gap)\n", "        \n", "        # Get lattice parameters\n", "        atoms = row.toatoms()\n", "        a, b, c = atoms.get_cell().lengths()\n", "        a_params.append(a)\n", "        b_params.append(b)\n", "        c_params.append(c)\n", "        materials.append(row.formula)\n", "    except Exception as e:\n", "        continue\n", "\n", "\n", "S_data = pd.DataFrame({\n", "        'Material name': materials,\n", "        'prototype': prototypes,\n", "        'PBEsol_gap': gaps,\n", "        'a': a_params,\n", "        'b': b_params,\n", "        'c': c_params\n", "    })\n"]}, {"cell_type": "code", "execution_count": 16, "id": "859fd1e5", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Material name", "rawType": "object", "type": "string"}, {"name": "prototype", "rawType": "object", "type": "string"}, {"name": "PBEsol_gap", "rawType": "float64", "type": "float"}, {"name": "a", "rawType": "float64", "type": "float"}, {"name": "b", "rawType": "float64", "type": "float"}, {"name": "c", "rawType": "float64", "type": "float"}], "ref": "3668bace-42f9-47ad-a749-e262f29c6ba4", "rows": [["0", "Ag4Bi4S12", "NH4CdCl3/Sn2S3", "0.3787571199", "8.439954980600666", "4.062605373495314", "15.01996212549276"], ["1", "Ag4Bi4S12", "GdFeO3", "0.05269746418691046", "7.284344000000138", "8.433465000000059", "7.460852000000268"], ["2", "Ag4Bi4S12", "YScS3", "0.06709161039393408", "7.436515000000134", "7.327558000000068", "8.433497000000058"], ["3", "Ag4Bi4S12", "PbPS3", "0.0", "7.509630482047968", "7.836358750246571", "9.263844607688213"], ["4", "Ag2Bi2S6", "BaNiO3", "0.4132422291612432", "7.668871957759889", "7.264953822593644", "7.658717"], ["5", "Ag4Bi4S12", "FePS3", "0.5166075635455991", "7.8027290974405235", "11.407025", "5.760542384913507"], ["6", "AgBiS3", "cubic", "0.0", "5.11841", "5.11841", "5.11841"], ["7", "AgBiS3", "distorted", "0.65", "6.54138502280511", "5.195407426500544", "5.743931706402592"], ["8", "AgBiS3", "distorted", "0.0", "5.058357464075271", "5.073687107537574", "5.16223158451701"], ["9", "Ag4Ta4S12", "Pyroxene-CaIrO3", "0.11", "3.341325", "13.962403", "7.69272"], ["10", "Ag4Ta4S12", "NH4CdCl3/Sn2S3", "0.3797461319736515", "8.282521675065933", "3.398471753185246", "13.719144802917492"], ["11", "Ag4Ta4S12", "GdFeO3", "0.0", "6.326188000009563", "9.302842000013758", "6.378191"], ["12", "Ag4Ta4S12", "YScS3", "0.5461607876528562", "6.375273", "6.363357", "9.270779"], ["13", "Ag4Ta4S12", "PbPS3", "0.7531891668724509", "6.902190421610374", "7.069952567898105", "11.036549728068504"], ["14", "Ag2Ta2S6", "BaNiO3", "0.0", "5.20752", "6.0508300256368965", "6.475219560752052"], ["15", "Ag4Ta4S12", "FePS3", "0.0", "6.888309479895121", "11.198974", "6.268251707806571"], ["16", "AgTaS3", "cubic", "0.0", "4.75267", "4.75267", "4.75267"], ["17", "AgTaS3", "distorted", "1.05", "4.359007065639261", "4.929161929701539", "5.771515467154965"], ["18", "Ag4Ta4S12", "FePS3", "0.29881636150169477", "6.2432654386752615", "10.892912", "6.50112242246037"], ["19", "Ag4Ta4S12", "NH4CdCl3/Sn2S3", "0.17561962172660106", "11.3337", "3.4508", "10.8037"], ["20", "Ag4Ta4S12", "GdFeO3", "0.0", "6.082881000749974", "13.149708000906218", "4.797841000645707"], ["21", "Ag4Ta4S12", "YScS3", "0.0", "6.065279002884039", "4.825199000046112", "13.094976003051706"], ["22", "Ag4Ta4S12", "PbPS3", "0.3505118174077628", "6.428414247112036", "6.578554243458208", "10.441892489453721"], ["23", "Ag2Ta2S6", "BaNiO3", "0.7597650251371348", "6.517076723442805", "6.51816086472511", "4.607421"], ["24", "AgTaS3", "cubic", "0.0", "4.59352", "4.59352", "4.59352"], ["25", "AgTaS3", "distorted", "0.21", "3.37105", "6.012190001538208", "6.121436371025186"], ["26", "AgVS3", "distorted", "0.0", "2.9988602672850564", "6.095815635097324", "6.116892962124644"], ["27", "Ag4V4S12", "PbPS3", "0.22648037820029998", "5.947605767903165", "6.421710000499089", "10.333880374191924"], ["28", "Ag4V4S12", "GdFeO3", "0.0", "6.075326", "9.006749", "6.144669"], ["29", "Ag4V4S12", "YScS3", "0.0", "6.108912000000082", "6.192503", "8.950359"], ["30", "Ag2V2S6", "BaNiO3", "0.0", "4.909616", "5.899480000247141", "6.441290245434217"], ["31", "Ag4V4S12", "FePS3", "0.0", "8.69707521050991", "10.295989", "5.881880624265762"], ["32", "AgVS3", "cubic", "0.0", "4.52985", "4.52985", "4.52985"], ["33", "AgVS3", "distorted", "0.02", "6.048704656033885", "3.7380524434772178", "6.135582852267664"], ["34", "Ag4V4S12", "NH4CdCl3/Sn2S3", "0.15537268243324576", "8.22014100000663", "3.169197000000631", "13.40383000001078"], ["35", "Al8S12", "PbPS3", "2.130214197016561", "6.143910321151587", "6.843441262844959", "9.835864949112558"], ["36", "Al8S12", "GdFeO3", "1.1533818754687868", "6.115417", "9.2088", "5.942788"], ["37", "Al8S12", "YScS3", "2.7704856651511456", "6.240515", "6.075701", "8.808213"], ["38", "Al4S6", "BaNiO3", "2.2591257464859775", "6.181671238354721", "7.2975848834899475", "5.9447024999680185"], ["39", "Al8S12", "FePS3", "1.2690403289590755", "6.168697996821128", "10.643182", "6.149079381417433"], ["40", "Al2S3", "cubic", "0.0", "4.57138", "4.57138", "4.57138"], ["41", "Al2S3", "distorted", "1.98", "5.067542201984509", "3.5379266849529256", "6.160760422803421"], ["42", "Al8S12", "NH4CdCl3/Sn2S3", "2.0992696535172737", "8.782832", "3.489291", "13.106157"], ["43", "Al4Bi4S12", "NH4CdCl3/Sn2S3", "1.9041139498135564", "8.60058", "3.70873", "12.8943"], ["44", "Al4Bi4S12", "PbPS3", "1.295657040874742", "6.2483729929629686", "8.279668330330932", "9.740933218714416"], ["45", "Al4Bi4S12", "NH4CdCl3/Sn2S3", "1.5893076784244746", "11.068128", "3.700322", "10.114318"], ["46", "Al4Bi4S12", "GdFeO3", "0.5270071982142772", "6.561138", "10.450626", "6.700114"], ["47", "Al4Bi4S12", "YScS3", "1.7784772392134611", "6.51726", "7.023026", "9.86769"], ["48", "Al2Bi2S6", "BaNiO3", "0.0", "7.227858198906782", "7.233427282555621", "7.587731"], ["49", "Al4Bi4S12", "FePS3", "0.486833588865732", "7.039972188536898", "10.824608", "6.648557785722328"]], "shape": {"columns": 6, "rows": 1621}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Material name</th>\n", "      <th>prototype</th>\n", "      <th>PBEsol_gap</th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Ag4Bi4S12</td>\n", "      <td>NH4CdCl3/Sn2S3</td>\n", "      <td>0.378757</td>\n", "      <td>8.439955</td>\n", "      <td>4.062605</td>\n", "      <td>15.019962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Ag4Bi4S12</td>\n", "      <td>GdFeO3</td>\n", "      <td>0.052697</td>\n", "      <td>7.284344</td>\n", "      <td>8.433465</td>\n", "      <td>7.460852</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Ag4Bi4S12</td>\n", "      <td>YScS3</td>\n", "      <td>0.067092</td>\n", "      <td>7.436515</td>\n", "      <td>7.327558</td>\n", "      <td>8.433497</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Ag4Bi4S12</td>\n", "      <td>PbPS3</td>\n", "      <td>0.000000</td>\n", "      <td>7.509630</td>\n", "      <td>7.836359</td>\n", "      <td>9.263845</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Ag2Bi2S6</td>\n", "      <td>BaNiO3</td>\n", "      <td>0.413242</td>\n", "      <td>7.668872</td>\n", "      <td>7.264954</td>\n", "      <td>7.658717</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1616</th>\n", "      <td>Zn4Zr4S12</td>\n", "      <td>PbPS3</td>\n", "      <td>1.138659</td>\n", "      <td>6.596393</td>\n", "      <td>6.647446</td>\n", "      <td>11.164824</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1617</th>\n", "      <td>Zn2Zr2S6</td>\n", "      <td>BaNiO3</td>\n", "      <td>0.000000</td>\n", "      <td>6.590707</td>\n", "      <td>6.592268</td>\n", "      <td>4.525310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1618</th>\n", "      <td>ZnZrS3</td>\n", "      <td>cubic</td>\n", "      <td>0.000000</td>\n", "      <td>4.507850</td>\n", "      <td>4.507850</td>\n", "      <td>4.507850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1619</th>\n", "      <td>ZnZrS3</td>\n", "      <td>distorted</td>\n", "      <td>1.420000</td>\n", "      <td>3.660450</td>\n", "      <td>6.113486</td>\n", "      <td>5.365584</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1620</th>\n", "      <td>La8Y8S24</td>\n", "      <td>CeTmS3</td>\n", "      <td>0.480000</td>\n", "      <td>21.488800</td>\n", "      <td>3.993250</td>\n", "      <td>11.068100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1621 rows × 6 columns</p>\n", "</div>"], "text/plain": ["     Material name       prototype  PBEsol_gap          a         b          c\n", "0        Ag4Bi4S12  NH4CdCl3/Sn2S3    0.378757   8.439955  4.062605  15.019962\n", "1        Ag4Bi4S12          GdFeO3    0.052697   7.284344  8.433465   7.460852\n", "2        Ag4Bi4S12           YScS3    0.067092   7.436515  7.327558   8.433497\n", "3        Ag4Bi4S12           PbPS3    0.000000   7.509630  7.836359   9.263845\n", "4         Ag2Bi2S6          BaNiO3    0.413242   7.668872  7.264954   7.658717\n", "...            ...             ...         ...        ...       ...        ...\n", "1616     Zn4Zr4S12           PbPS3    1.138659   6.596393  6.647446  11.164824\n", "1617      Zn2Zr2S6          BaNiO3    0.000000   6.590707  6.592268   4.525310\n", "1618        ZnZrS3           cubic    0.000000   4.507850  4.507850   4.507850\n", "1619        ZnZrS3       distorted    1.420000   3.660450  6.113486   5.365584\n", "1620      La8Y8S24          CeTmS3    0.480000  21.488800  3.993250  11.068100\n", "\n", "[1621 rows x 6 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["S_data"]}, {"cell_type": "code", "execution_count": 17, "id": "571e5511", "metadata": {}, "outputs": [], "source": ["def parse_formula(formula):\n", "    import re\n", "    return re.findall('[A-Z][a-z]?', formula)\n"]}, {"cell_type": "code", "execution_count": 18, "id": "ae5877ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Se', 'Si', 'Te', None], dtype=object)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["Se_data[['A', 'B', 'C']] = pd.DataFrame(Se_data.Formula.apply(parse_formula).tolist(), index=Se_data.index)\n", "Se_data.C.unique()\n"]}, {"cell_type": "code", "execution_count": null, "id": "161fa9b6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}