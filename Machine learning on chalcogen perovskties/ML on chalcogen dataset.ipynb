{"cells": [{"cell_type": "code", "execution_count": 101, "id": "6cc0e636", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pandas as pd\n", "data=pd.read_csv('/home/<USER>/Coding/Machine learning on chalcogen perovskties/Final dataset for chalcogen perovskties.csv').drop('Unnamed: 0',axis=1)"]}, {"cell_type": "code", "execution_count": 102, "id": "1b41d8de", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Formula</th>\n", "      <th>Prototype</th>\n", "      <th>Bandgap</th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>Name_x</th>\n", "      <th>...</th>\n", "      <th>Electronegativity_A</th>\n", "      <th>Group_A</th>\n", "      <th>Electron affinity_A</th>\n", "      <th>Unpaired electrons_A</th>\n", "      <th>Name_y</th>\n", "      <th>Atomic Radius_B</th>\n", "      <th>Electronegativity_B</th>\n", "      <th>Group_B</th>\n", "      <th>Electron affinity_B</th>\n", "      <th>Unpaired electrons_B</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Nb4Y4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>0.00</td>\n", "      <td>7.09</td>\n", "      <td>7.17</td>\n", "      <td>9.98</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>1.60</td>\n", "      <td>5</td>\n", "      <td>0.92</td>\n", "      <td>5</td>\n", "      <td>Y</td>\n", "      <td>180.00</td>\n", "      <td>1.22</td>\n", "      <td>3</td>\n", "      <td>0.31</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Nb2Y2Se6</td>\n", "      <td>BaNiO3</td>\n", "      <td>0.00</td>\n", "      <td>5.12</td>\n", "      <td>7.07</td>\n", "      <td>7.07</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>1.60</td>\n", "      <td>5</td>\n", "      <td>0.92</td>\n", "      <td>5</td>\n", "      <td>Y</td>\n", "      <td>180.00</td>\n", "      <td>1.22</td>\n", "      <td>3</td>\n", "      <td>0.31</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Nb6Y6Se18</td>\n", "      <td>Al2S3</td>\n", "      <td>0.11</td>\n", "      <td>7.39</td>\n", "      <td>7.39</td>\n", "      <td>16.33</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>1.60</td>\n", "      <td>5</td>\n", "      <td>0.92</td>\n", "      <td>5</td>\n", "      <td>Y</td>\n", "      <td>180.00</td>\n", "      <td>1.22</td>\n", "      <td>3</td>\n", "      <td>0.31</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Nb4Y4Se12</td>\n", "      <td>NH4CdCl3</td>\n", "      <td>0.00</td>\n", "      <td>3.56</td>\n", "      <td>8.03</td>\n", "      <td>14.87</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>1.60</td>\n", "      <td>5</td>\n", "      <td>0.92</td>\n", "      <td>5</td>\n", "      <td>Y</td>\n", "      <td>180.00</td>\n", "      <td>1.22</td>\n", "      <td>3</td>\n", "      <td>0.31</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Nb2Y2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>0.00</td>\n", "      <td>5.78</td>\n", "      <td>7.57</td>\n", "      <td>5.86</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>1.60</td>\n", "      <td>5</td>\n", "      <td>0.92</td>\n", "      <td>5</td>\n", "      <td>Y</td>\n", "      <td>180.00</td>\n", "      <td>1.22</td>\n", "      <td>3</td>\n", "      <td>0.31</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6447</th>\n", "      <td>Co4Mn4S12</td>\n", "      <td>PbPS3</td>\n", "      <td>0.00</td>\n", "      <td>5.92</td>\n", "      <td>6.81</td>\n", "      <td>8.12</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>1.55</td>\n", "      <td>7</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>Co</td>\n", "      <td>135.00</td>\n", "      <td>1.88</td>\n", "      <td>9</td>\n", "      <td>0.66</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6448</th>\n", "      <td>Co2Mn2S6</td>\n", "      <td>BaNiO3</td>\n", "      <td>0.00</td>\n", "      <td>5.99</td>\n", "      <td>5.99</td>\n", "      <td>4.40</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>1.55</td>\n", "      <td>7</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>Co</td>\n", "      <td>135.00</td>\n", "      <td>1.88</td>\n", "      <td>9</td>\n", "      <td>0.66</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6449</th>\n", "      <td>Co4Mn4S12</td>\n", "      <td>FePS3</td>\n", "      <td>0.00</td>\n", "      <td>6.04</td>\n", "      <td>9.67</td>\n", "      <td>5.68</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>1.55</td>\n", "      <td>7</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>Co</td>\n", "      <td>135.00</td>\n", "      <td>1.88</td>\n", "      <td>9</td>\n", "      <td>0.66</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6450</th>\n", "      <td>CoMnS3</td>\n", "      <td>cubic</td>\n", "      <td>0.00</td>\n", "      <td>4.26</td>\n", "      <td>4.26</td>\n", "      <td>4.26</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>1.55</td>\n", "      <td>7</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>Co</td>\n", "      <td>135.00</td>\n", "      <td>1.88</td>\n", "      <td>9</td>\n", "      <td>0.66</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6451</th>\n", "      <td>CoMnS3</td>\n", "      <td>distorted</td>\n", "      <td>0.05</td>\n", "      <td>6.32</td>\n", "      <td>7.93</td>\n", "      <td>3.14</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>1.55</td>\n", "      <td>7</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>Co</td>\n", "      <td>135.00</td>\n", "      <td>1.88</td>\n", "      <td>9</td>\n", "      <td>0.66</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6452 rows × 21 columns</p>\n", "</div>"], "text/plain": ["        Formula  Prototype  Bandgap    a    b     c   A   B   C Name_x  ...  \\\n", "0     Nb4Y4Se12      PbPS3     0.00 7.09 7.17  9.98  Nb   Y  Se     Nb  ...   \n", "1      Nb2Y2Se6     BaNiO3     0.00 5.12 7.07  7.07  Nb   Y  Se     Nb  ...   \n", "2     Nb6Y6Se18      Al2S3     0.11 7.39 7.39 16.33  Nb   Y  Se     Nb  ...   \n", "3     Nb4Y4Se12   NH4CdCl3     0.00 3.56 8.03 14.87  Nb   Y  Se     Nb  ...   \n", "4      Nb2Y2Se6      FePS3     0.00 5.78 7.57  5.86  Nb   Y  Se     Nb  ...   \n", "...         ...        ...      ...  ...  ...   ...  ..  ..  ..    ...  ...   \n", "6447  Co4Mn4S12      PbPS3     0.00 5.92 6.81  8.12  Mn  Co   S     Mn  ...   \n", "6448   Co2Mn2S6     BaNiO3     0.00 5.99 5.99  4.40  Mn  Co   S     Mn  ...   \n", "6449  Co4Mn4S12      FePS3     0.00 6.04 9.67  5.68  Mn  Co   S     Mn  ...   \n", "6450     CoMnS3      cubic     0.00 4.26 4.26  4.26  Mn  Co   S     Mn  ...   \n", "6451     CoMnS3  distorted     0.05 6.32 7.93  3.14  Mn  Co   S     Mn  ...   \n", "\n", "      Electronegativity_A  Group_A  Electron affinity_A  Unpaired electrons_A  \\\n", "0                    1.60        5                 0.92                     5   \n", "1                    1.60        5                 0.92                     5   \n", "2                    1.60        5                 0.92                     5   \n", "3                    1.60        5                 0.92                     5   \n", "4                    1.60        5                 0.92                     5   \n", "...                   ...      ...                  ...                   ...   \n", "6447                 1.55        7                  NaN                     5   \n", "6448                 1.55        7                  NaN                     5   \n", "6449                 1.55        7                  NaN                     5   \n", "6450                 1.55        7                  NaN                     5   \n", "6451                 1.55        7                  NaN                     5   \n", "\n", "      Name_y Atomic Radius_B  Electronegativity_B  Group_B  \\\n", "0          Y          180.00                 1.22        3   \n", "1          Y          180.00                 1.22        3   \n", "2          Y          180.00                 1.22        3   \n", "3          Y          180.00                 1.22        3   \n", "4          Y          180.00                 1.22        3   \n", "...      ...             ...                  ...      ...   \n", "6447      Co          135.00                 1.88        9   \n", "6448      Co          135.00                 1.88        9   \n", "6449      Co          135.00                 1.88        9   \n", "6450      Co          135.00                 1.88        9   \n", "6451      Co          135.00                 1.88        9   \n", "\n", "      Electron affinity_B  Unpaired electrons_B  \n", "0                    0.31                     1  \n", "1                    0.31                     1  \n", "2                    0.31                     1  \n", "3                    0.31                     1  \n", "4                    0.31                     1  \n", "...                   ...                   ...  \n", "6447                 0.66                     3  \n", "6448                 0.66                     3  \n", "6449                 0.66                     3  \n", "6450                 0.66                     3  \n", "6451                 0.66                     3  \n", "\n", "[6452 rows x 21 columns]"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 103, "id": "7fa1ce7b", "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import OneHotEncoder\n", "\n", "encoder = OneHotEncoder(sparse_output=False)\n", "protos=encoder.fit_transform(data[['Prototype']])\n", "data[encoder.get_feature_names_out()]=protos"]}, {"cell_type": "code", "execution_count": 104, "id": "fdec7fa2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Formula</th>\n", "      <th>Prototype</th>\n", "      <th>Bandgap</th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>Name_x</th>\n", "      <th>...</th>\n", "      <th>Prototype_FePS3</th>\n", "      <th>Prototype_GdFeO3</th>\n", "      <th>Prototype_MnPSe3</th>\n", "      <th>Prototype_NH4CdCl3</th>\n", "      <th>Prototype_NH4CdCl3/Sn2S3</th>\n", "      <th>Prototype_PbPS3</th>\n", "      <th>Prototype_Pyroxene-CaIrO3</th>\n", "      <th>Prototype_YScS3</th>\n", "      <th>Prototype_cubic</th>\n", "      <th>Prototype_distorted</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Nb4Y4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>0.00</td>\n", "      <td>7.09</td>\n", "      <td>7.17</td>\n", "      <td>9.98</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Nb2Y2Se6</td>\n", "      <td>BaNiO3</td>\n", "      <td>0.00</td>\n", "      <td>5.12</td>\n", "      <td>7.07</td>\n", "      <td>7.07</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Nb6Y6Se18</td>\n", "      <td>Al2S3</td>\n", "      <td>0.11</td>\n", "      <td>7.39</td>\n", "      <td>7.39</td>\n", "      <td>16.33</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Nb4Y4Se12</td>\n", "      <td>NH4CdCl3</td>\n", "      <td>0.00</td>\n", "      <td>3.56</td>\n", "      <td>8.03</td>\n", "      <td>14.87</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Nb2Y2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>0.00</td>\n", "      <td>5.78</td>\n", "      <td>7.57</td>\n", "      <td>5.86</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6447</th>\n", "      <td>Co4Mn4S12</td>\n", "      <td>PbPS3</td>\n", "      <td>0.00</td>\n", "      <td>5.92</td>\n", "      <td>6.81</td>\n", "      <td>8.12</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6448</th>\n", "      <td>Co2Mn2S6</td>\n", "      <td>BaNiO3</td>\n", "      <td>0.00</td>\n", "      <td>5.99</td>\n", "      <td>5.99</td>\n", "      <td>4.40</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6449</th>\n", "      <td>Co4Mn4S12</td>\n", "      <td>FePS3</td>\n", "      <td>0.00</td>\n", "      <td>6.04</td>\n", "      <td>9.67</td>\n", "      <td>5.68</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6450</th>\n", "      <td>CoMnS3</td>\n", "      <td>cubic</td>\n", "      <td>0.00</td>\n", "      <td>4.26</td>\n", "      <td>4.26</td>\n", "      <td>4.26</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6451</th>\n", "      <td>CoMnS3</td>\n", "      <td>distorted</td>\n", "      <td>0.05</td>\n", "      <td>6.32</td>\n", "      <td>7.93</td>\n", "      <td>3.14</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6452 rows × 36 columns</p>\n", "</div>"], "text/plain": ["        Formula  Prototype  Bandgap    a    b     c   A   B   C Name_x  ...  \\\n", "0     Nb4Y4Se12      PbPS3     0.00 7.09 7.17  9.98  Nb   Y  Se     Nb  ...   \n", "1      Nb2Y2Se6     BaNiO3     0.00 5.12 7.07  7.07  Nb   Y  Se     Nb  ...   \n", "2     Nb6Y6Se18      Al2S3     0.11 7.39 7.39 16.33  Nb   Y  Se     Nb  ...   \n", "3     Nb4Y4Se12   NH4CdCl3     0.00 3.56 8.03 14.87  Nb   Y  Se     Nb  ...   \n", "4      Nb2Y2Se6      FePS3     0.00 5.78 7.57  5.86  Nb   Y  Se     Nb  ...   \n", "...         ...        ...      ...  ...  ...   ...  ..  ..  ..    ...  ...   \n", "6447  Co4Mn4S12      PbPS3     0.00 5.92 6.81  8.12  Mn  Co   S     Mn  ...   \n", "6448   Co2Mn2S6     BaNiO3     0.00 5.99 5.99  4.40  Mn  Co   S     Mn  ...   \n", "6449  Co4Mn4S12      FePS3     0.00 6.04 9.67  5.68  Mn  Co   S     Mn  ...   \n", "6450     CoMnS3      cubic     0.00 4.26 4.26  4.26  Mn  Co   S     Mn  ...   \n", "6451     CoMnS3  distorted     0.05 6.32 7.93  3.14  Mn  Co   S     Mn  ...   \n", "\n", "      Prototype_FePS3  Prototype_GdFeO3  Prototype_MnPSe3  Prototype_NH4CdCl3  \\\n", "0                0.00              0.00              0.00                0.00   \n", "1                0.00              0.00              0.00                0.00   \n", "2                0.00              0.00              0.00                0.00   \n", "3                0.00              0.00              0.00                1.00   \n", "4                1.00              0.00              0.00                0.00   \n", "...               ...               ...               ...                 ...   \n", "6447             0.00              0.00              0.00                0.00   \n", "6448             0.00              0.00              0.00                0.00   \n", "6449             1.00              0.00              0.00                0.00   \n", "6450             0.00              0.00              0.00                0.00   \n", "6451             0.00              0.00              0.00                0.00   \n", "\n", "      Prototype_NH4CdCl3/Sn2S3 Prototype_PbPS3  Prototype_Pyroxene-CaIrO3  \\\n", "0                         0.00            1.00                       0.00   \n", "1                         0.00            0.00                       0.00   \n", "2                         0.00            0.00                       0.00   \n", "3                         0.00            0.00                       0.00   \n", "4                         0.00            0.00                       0.00   \n", "...                        ...             ...                        ...   \n", "6447                      0.00            1.00                       0.00   \n", "6448                      0.00            0.00                       0.00   \n", "6449                      0.00            0.00                       0.00   \n", "6450                      0.00            0.00                       0.00   \n", "6451                      0.00            0.00                       0.00   \n", "\n", "      Prototype_YScS3  Prototype_cubic  Prototype_distorted  \n", "0                0.00             0.00                 0.00  \n", "1                0.00             0.00                 0.00  \n", "2                0.00             0.00                 0.00  \n", "3                0.00             0.00                 0.00  \n", "4                0.00             0.00                 0.00  \n", "...               ...              ...                  ...  \n", "6447             0.00             0.00                 0.00  \n", "6448             0.00             0.00                 0.00  \n", "6449             0.00             0.00                 0.00  \n", "6450             0.00             1.00                 0.00  \n", "6451             0.00             0.00                 1.00  \n", "\n", "[6452 rows x 36 columns]"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 105, "id": "8668f332", "metadata": {}, "outputs": [], "source": ["prot_counts=(data.Prototype.value_counts())"]}, {"cell_type": "code", "execution_count": 106, "id": "376964af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Formula</th>\n", "      <th>Prototype</th>\n", "      <th>Bandgap</th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>Name_x</th>\n", "      <th>...</th>\n", "      <th>Prototype_FePS3</th>\n", "      <th>Prototype_GdFeO3</th>\n", "      <th>Prototype_MnPSe3</th>\n", "      <th>Prototype_NH4CdCl3</th>\n", "      <th>Prototype_NH4CdCl3/Sn2S3</th>\n", "      <th>Prototype_PbPS3</th>\n", "      <th>Prototype_Pyroxene-CaIrO3</th>\n", "      <th>Prototype_YScS3</th>\n", "      <th>Prototype_cubic</th>\n", "      <th>Prototype_distorted</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Nb4Y4Se12</td>\n", "      <td>PbPS3</td>\n", "      <td>0.00</td>\n", "      <td>7.09</td>\n", "      <td>7.17</td>\n", "      <td>9.98</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Nb2Y2Se6</td>\n", "      <td>BaNiO3</td>\n", "      <td>0.00</td>\n", "      <td>5.12</td>\n", "      <td>7.07</td>\n", "      <td>7.07</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Nb6Y6Se18</td>\n", "      <td>Al2S3</td>\n", "      <td>0.11</td>\n", "      <td>7.39</td>\n", "      <td>7.39</td>\n", "      <td>16.33</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Nb4Y4Se12</td>\n", "      <td>NH4CdCl3</td>\n", "      <td>0.00</td>\n", "      <td>3.56</td>\n", "      <td>8.03</td>\n", "      <td>14.87</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Nb2Y2Se6</td>\n", "      <td>FePS3</td>\n", "      <td>0.00</td>\n", "      <td>5.78</td>\n", "      <td>7.57</td>\n", "      <td>5.86</td>\n", "      <td>Nb</td>\n", "      <td>Y</td>\n", "      <td>Se</td>\n", "      <td>Nb</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6447</th>\n", "      <td>Co4Mn4S12</td>\n", "      <td>PbPS3</td>\n", "      <td>0.00</td>\n", "      <td>5.92</td>\n", "      <td>6.81</td>\n", "      <td>8.12</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6448</th>\n", "      <td>Co2Mn2S6</td>\n", "      <td>BaNiO3</td>\n", "      <td>0.00</td>\n", "      <td>5.99</td>\n", "      <td>5.99</td>\n", "      <td>4.40</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6449</th>\n", "      <td>Co4Mn4S12</td>\n", "      <td>FePS3</td>\n", "      <td>0.00</td>\n", "      <td>6.04</td>\n", "      <td>9.67</td>\n", "      <td>5.68</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6450</th>\n", "      <td>CoMnS3</td>\n", "      <td>cubic</td>\n", "      <td>0.00</td>\n", "      <td>4.26</td>\n", "      <td>4.26</td>\n", "      <td>4.26</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6451</th>\n", "      <td>CoMnS3</td>\n", "      <td>distorted</td>\n", "      <td>0.05</td>\n", "      <td>6.32</td>\n", "      <td>7.93</td>\n", "      <td>3.14</td>\n", "      <td>Mn</td>\n", "      <td>Co</td>\n", "      <td>S</td>\n", "      <td>Mn</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6448 rows × 36 columns</p>\n", "</div>"], "text/plain": ["        Formula  Prototype  Bandgap    a    b     c   A   B   C Name_x  ...  \\\n", "0     Nb4Y4Se12      PbPS3     0.00 7.09 7.17  9.98  Nb   Y  Se     Nb  ...   \n", "1      Nb2Y2Se6     BaNiO3     0.00 5.12 7.07  7.07  Nb   Y  Se     Nb  ...   \n", "2     Nb6Y6Se18      Al2S3     0.11 7.39 7.39 16.33  Nb   Y  Se     Nb  ...   \n", "3     Nb4Y4Se12   NH4CdCl3     0.00 3.56 8.03 14.87  Nb   Y  Se     Nb  ...   \n", "4      Nb2Y2Se6      FePS3     0.00 5.78 7.57  5.86  Nb   Y  Se     Nb  ...   \n", "...         ...        ...      ...  ...  ...   ...  ..  ..  ..    ...  ...   \n", "6447  Co4Mn4S12      PbPS3     0.00 5.92 6.81  8.12  Mn  Co   S     Mn  ...   \n", "6448   Co2Mn2S6     BaNiO3     0.00 5.99 5.99  4.40  Mn  Co   S     Mn  ...   \n", "6449  Co4Mn4S12      FePS3     0.00 6.04 9.67  5.68  Mn  Co   S     Mn  ...   \n", "6450     CoMnS3      cubic     0.00 4.26 4.26  4.26  Mn  Co   S     Mn  ...   \n", "6451     CoMnS3  distorted     0.05 6.32 7.93  3.14  Mn  Co   S     Mn  ...   \n", "\n", "      Prototype_FePS3  Prototype_GdFeO3  Prototype_MnPSe3  Prototype_NH4CdCl3  \\\n", "0                0.00              0.00              0.00                0.00   \n", "1                0.00              0.00              0.00                0.00   \n", "2                0.00              0.00              0.00                0.00   \n", "3                0.00              0.00              0.00                1.00   \n", "4                1.00              0.00              0.00                0.00   \n", "...               ...               ...               ...                 ...   \n", "6447             0.00              0.00              0.00                0.00   \n", "6448             0.00              0.00              0.00                0.00   \n", "6449             1.00              0.00              0.00                0.00   \n", "6450             0.00              0.00              0.00                0.00   \n", "6451             0.00              0.00              0.00                0.00   \n", "\n", "      Prototype_NH4CdCl3/Sn2S3 Prototype_PbPS3  Prototype_Pyroxene-CaIrO3  \\\n", "0                         0.00            1.00                       0.00   \n", "1                         0.00            0.00                       0.00   \n", "2                         0.00            0.00                       0.00   \n", "3                         0.00            0.00                       0.00   \n", "4                         0.00            0.00                       0.00   \n", "...                        ...             ...                        ...   \n", "6447                      0.00            1.00                       0.00   \n", "6448                      0.00            0.00                       0.00   \n", "6449                      0.00            0.00                       0.00   \n", "6450                      0.00            0.00                       0.00   \n", "6451                      0.00            0.00                       0.00   \n", "\n", "      Prototype_YScS3  Prototype_cubic  Prototype_distorted  \n", "0                0.00             0.00                 0.00  \n", "1                0.00             0.00                 0.00  \n", "2                0.00             0.00                 0.00  \n", "3                0.00             0.00                 0.00  \n", "4                0.00             0.00                 0.00  \n", "...               ...              ...                  ...  \n", "6447             0.00             0.00                 0.00  \n", "6448             0.00             0.00                 0.00  \n", "6449             0.00             0.00                 0.00  \n", "6450             0.00             1.00                 0.00  \n", "6451             0.00             0.00                 1.00  \n", "\n", "[6448 rows x 36 columns]"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df = data[~data['Prototype'].isin((prot_counts.loc[prot_counts<10].index))]\n", "new_df\n"]}, {"cell_type": "code", "execution_count": 110, "id": "a3d0bbea", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b5cb2f87f39a47c8a3765ea6f30fe930", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/42 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.002910 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 1065\n", "[LightGBM] [Info] Number of data points in the train set: 5803, number of used features: 24\n", "[LightGBM] [Info] Start training from score 0.309142\n", "\n", "Model Performance Ranking:\n", "                               Adjusted R-Squared  R-Squared  RMSE  \\\n", "Model                                                                \n", "ExtraTreesRegressor                          0.79       0.80  0.23   \n", "RandomForestRegressor                        0.75       0.76  0.25   \n", "XGBRegressor                                 0.75       0.76  0.25   \n", "HistGradientBoostingRegressor                0.74       0.75  0.25   \n", "LGBMRegressor                                0.74       0.75  0.25   \n", "BaggingRegressor                             0.72       0.73  0.26   \n", "GaussianProcessRegressor                     0.69       0.71  0.27   \n", "MLPRegressor                                 0.65       0.67  0.29   \n", "KNeighborsRegressor                          0.62       0.64  0.30   \n", "SVR                                          0.61       0.62  0.31   \n", "NuSVR                                        0.60       0.62  0.31   \n", "ExtraTreeRegressor                           0.58       0.59  0.32   \n", "GradientBoostingRegressor                    0.57       0.59  0.32   \n", "DecisionTreeRegressor                        0.48       0.50  0.35   \n", "SGDRegressor                                 0.37       0.39  0.39   \n", "BayesianRidge                                0.37       0.39  0.39   \n", "ElasticNetCV                                 0.36       0.39  0.39   \n", "Ridge                                        0.36       0.39  0.39   \n", "RidgeCV                                      0.36       0.39  0.39   \n", "LassoCV                                      0.36       0.39  0.39   \n", "LassoLarsCV                                  0.36       0.39  0.39   \n", "LassoLarsIC                                  0.36       0.39  0.39   \n", "<PERSON>                                         0.36       0.39  0.39   \n", "LarsCV                                       0.36       0.39  0.39   \n", "LinearRegression                             0.36       0.39  0.39   \n", "TransformedTargetRegressor                   0.36       0.39  0.39   \n", "HuberRegressor                               0.30       0.33  0.41   \n", "TweedieRegressor                             0.24       0.27  0.43   \n", "LinearSVR                                    0.22       0.26  0.43   \n", "OrthogonalMatchingPursuitCV                  0.20       0.23  0.44   \n", "AdaBoostRegressor                            0.15       0.19  0.45   \n", "PoissonRegressor                             0.14       0.18  0.46   \n", "OrthogonalMatchingPursuit                    0.05       0.09  0.48   \n", "PassiveAggressiveRegressor                  -0.03       0.01  0.50   \n", "ElasticNet                                  -0.05      -0.00  0.50   \n", "Lasso                                       -0.05      -0.00  0.50   \n", "LassoLars                                   -0.05      -0.00  0.50   \n", "DummyRegressor                              -0.05      -0.00  0.50   \n", "KernelRidge                                 -0.05      -0.00  0.50   \n", "RANSACRegressor                             -0.46      -0.40  0.59   \n", "\n", "                               Time Taken  mae_score  \n", "Model                                                 \n", "ExtraTreesRegressor                  1.25       0.13  \n", "RandomForestRegressor                0.93       0.15  \n", "XGBRegressor                         0.44       0.17  \n", "HistGradientBoostingRegressor        1.82       0.17  \n", "LGBMRegressor                        0.22       0.16  \n", "BaggingRegressor                     0.25       0.16  \n", "GaussianProcessRegressor             5.40       0.16  \n", "MLPRegressor                         3.09       0.21  \n", "KNeighborsRegressor                  0.05       0.18  \n", "SVR                                  1.06       0.20  \n", "NuSVR                                1.59       0.20  \n", "ExtraTreeRegressor                   0.02       0.17  \n", "GradientBoostingRegressor            1.06       0.23  \n", "DecisionTreeRegressor                0.05       0.19  \n", "SGDRegressor                         0.02       0.29  \n", "BayesianRidge                        0.02       0.29  \n", "ElasticNetCV                         0.11       0.29  \n", "Ridge                                0.02       0.29  \n", "RidgeCV                              0.03       0.29  \n", "LassoCV                              0.14       0.29  \n", "LassoLarsCV                          0.05       0.29  \n", "LassoLarsIC                          0.02       0.29  \n", "<PERSON>                                 0.02       0.29  \n", "LarsCV                               0.05       0.29  \n", "LinearRegression                     0.02       0.29  \n", "TransformedTargetRegressor           0.02       0.29  \n", "HuberRegressor                       0.05       0.28  \n", "TweedieRegressor                     0.05       0.32  \n", "LinearSVR                            0.27       0.28  \n", "OrthogonalMatchingPursuitCV          0.05       0.33  \n", "AdaBoostRegressor                    0.31       0.40  \n", "PoissonRegressor                     0.02       0.35  \n", "OrthogonalMatchingPursuit            0.01       0.36  \n", "PassiveAggressiveRegressor           0.02       0.37  \n", "ElasticNet                           0.01       0.40  \n", "Lasso                                0.02       0.40  \n", "LassoLars                            0.02       0.40  \n", "DummyRegressor                       0.01       0.40  \n", "<PERSON><PERSON>R<PERSON>                          2.71       0.38  \n", "RANSACRegressor                      0.18       0.32  \n"]}], "source": ["from lazypredict.Supervised import LazyClassifier, LazyRegressor\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error\n", "# Select numerical features\n", "fin_data = new_df.select_dtypes(np.number)\n", "\n", "# Split data into features and target\n", "X = fin_data.drop('Bandgap', axis=1)  # Assuming 'Bandgap' is the target variable\n", "y = fin_data['Bandgap']\n", "\n", "# Split data into train and test sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)\n", "\n", "# Initialize LazyPredict classifier and regressor with custom MAE metric\n", "def mae_score(y_true, y_pred):\n", "    return mean_absolute_error(y_true, y_pred)\n", "\n", "reg = LazyRegressor(verbose=0, ignore_warnings=True, custom_metric=mae_score)\n", "\n", "# Fit and get model performance report\n", "models_train, predictions_train = reg.fit(X_train, X_test, y_train, y_test)\n", "\n", "# Display results\n", "print(\"\\nModel Performance Ranking:\")\n", "print(models_train)\n"]}, {"cell_type": "code", "execution_count": 113, "id": "7ba9253c", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a32e4af8579342899e881f7c15677def", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/29 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] Number of positive: 2516, number of negative: 3287\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000360 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 1065\n", "[LightGBM] [Info] Number of data points in the train set: 5803, number of used features: 24\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.433569 -> initscore=-0.267305\n", "[LightGBM] [Info] Start training from score -0.267305\n", "\n", "Model Performance Ranking:\n", "                               Accuracy  Balanced Accuracy  ROC AUC  F1 Score  \\\n", "Model                                                                           \n", "ExtraTreesClassifier               0.87               0.87     0.87      0.87   \n", "XGBClassifier                      0.87               0.87     0.87      0.87   \n", "RandomForestClassifier             0.87               0.86     0.86      0.87   \n", "LGBMClassifier                     0.85               0.85     0.85      0.85   \n", "BaggingClassifier                  0.85               0.84     0.84      0.85   \n", "LabelPropagation                   0.84               0.84     0.84      0.84   \n", "LabelSpreading                     0.84               0.84     0.84      0.84   \n", "SVC                                0.83               0.83     0.83      0.83   \n", "AdaBoostClassifier                 0.82               0.82     0.82      0.82   \n", "NuSVC                              0.81               0.80     0.80      0.81   \n", "KNeighborsClassifier               0.80               0.80     0.80      0.80   \n", "DecisionTreeClassifier             0.80               0.79     0.79      0.80   \n", "LinearSVC                          0.79               0.79     0.79      0.79   \n", "LogisticRegression                 0.79               0.79     0.79      0.79   \n", "CalibratedClassifierCV             0.79               0.78     0.78      0.79   \n", "LinearDiscriminantAnalysis         0.78               0.78     0.78      0.78   \n", "RidgeClassifier                    0.78               0.78     0.78      0.78   \n", "RidgeClassifierCV                  0.78               0.78     0.78      0.78   \n", "ExtraTreeClassifier                0.78               0.78     0.78      0.78   \n", "SGDClassifier                      0.76               0.76     0.76      0.76   \n", "NearestCentroid                    0.75               0.75     0.75      0.75   \n", "GaussianNB                         0.75               0.74     0.74      0.74   \n", "BernoulliNB                        0.74               0.74     0.74      0.74   \n", "QuadraticDiscriminantAnalysis      0.74               0.73     0.73      0.74   \n", "PassiveAggressiveClassifier        0.69               0.69     0.69      0.69   \n", "Perceptron                         0.68               0.67     0.67      0.67   \n", "DummyClassifier                    0.54               0.50     0.50      0.38   \n", "\n", "                               Time Taken  \n", "Model                                      \n", "ExtraTreesClassifier                 0.44  \n", "XGBClassifier                        2.85  \n", "RandomForestClassifier               0.77  \n", "LGBMClassifier                       0.24  \n", "BaggingClassifier                    0.25  \n", "LabelPropagation                     1.32  \n", "LabelSpreading                       1.70  \n", "SVC                                  0.83  \n", "AdaBoostClassifier                   0.32  \n", "NuSVC                                1.21  \n", "KNeighborsClassifier                 0.06  \n", "DecisionTreeClassifier               0.05  \n", "LinearSVC                            0.41  \n", "LogisticRegression                   0.03  \n", "CalibratedClassifierCV               1.45  \n", "LinearDiscriminantAnalysis           0.03  \n", "RidgeClassifier                      0.03  \n", "RidgeClassifierCV                    0.03  \n", "ExtraTreeClassifier                  0.02  \n", "SGDClassifier                        0.05  \n", "NearestCentroid                      0.01  \n", "GaussianNB                           0.02  \n", "BernoulliNB                          0.02  \n", "QuadraticDiscriminantAnalysis        0.03  \n", "PassiveAggressiveClassifier          0.03  \n", "Perceptron                           0.02  \n", "DummyClassifier                      0.01  \n"]}], "source": ["from lazypredict.Supervised import LazyClassifier, LazyRegressor\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error\n", "# Select numerical features\n", "fin_data = new_df.select_dtypes(np.number)\n", "\n", "# Split data into features and target\n", "X = fin_data.drop('Bandgap', axis=1)  # Assuming 'Bandgap' is the target variable\n", "y = fin_data['Bandgap']>0\n", "\n", "# Split data into train and test sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)\n", "\n", "# Initialize LazyPredict classifier and regressor with custom MAE metric\n", "\n", "classifier = LazyClassifier(verbose=0, ignore_warnings=True, custom_metric=None)\n", "\n", "# Fit and get model performance report\n", "models_train, predictions_train = classifier.fit(X_train, X_test, y_train, y_test)\n", "\n", "# Display results\n", "print(\"\\nModel Performance Ranking:\")\n", "print(models_train)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}