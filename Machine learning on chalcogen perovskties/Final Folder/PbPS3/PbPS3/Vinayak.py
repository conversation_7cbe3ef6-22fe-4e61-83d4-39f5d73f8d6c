from ase.io import read
from ase.calculators.vasp import Vasp
from ase.dft.kpoints import monkhorst_pack as mp
import os
import math
import numpy as np
import json
from ase.db import connect
from ase.dft.bandgap import bandgap

os.environ['VASP_PP_PATH']=r'/scratch/sumitkiuac/mypps'

def get_kgrid(atoms,kpr=0.035):
    grid=np.ceil(np.linalg.norm(atoms.cell.reciprocal(),axis=1)/kpr).astype(int)
    return grid


def energy_calculator(row):
    global id,unique,lengths,formula,prototype,spacegroup,directory
    atoms=row.toatoms()
    id=row.id
    unique=row.unique_id
    prototype=row.prototype
    spacegroup=row.space_group
    formula=row.formula
    lengths=atoms.get_cell().lengths()
    directory=f'VASP_Output_data/{unique}'
    os.makedirs(directory, exist_ok=True)
    calc = Vasp(
        xc='PBE',  encut=455, kpts=get_kgrid(atoms),
        ibrion=-1,nsw=0, isym=1,
        sigma=0.02, ismear=0, ediff=1E-6,
        ediffg=-0.001, kpar=8, npar=8,
        icharg=2, lcharg=True, prec="Accurate",
        directory=directory,
        setups='recommended',
        command=r'mpiexec /home/<USER>/vaspset/vasp.5.4.4/bin/vasp_std'
        
    )
    atoms.calc=calc
    energy = atoms.get_potential_energy()

def writer():
    vasprun_path = os.path.join(directory, "vasprun.xml")
    calc_load = Vasp(restart=True, directory=directory)
    print(type(bandgap(calc_load)))
    text = bandgap(calc_load)[0]
    bandgap_file = os.path.join(directory, "bandgap.json")
    data = {
        "id": id,
        'Formula':formula,
        'Prototype':prototype,
        'Spacegroup':spacegroup,
        "lengths": lengths.tolist(),
        "bandgap": str(text)
    }
    with open(bandgap_file, "w") as f:
        json.dump(data, f, indent=4)



data=connect(r'/scratch/sumitkiuac/New_zipped/final_run/prototype_dbs/PbPS3/PbPS3.db')
for row in data.select():
    id=row.id
    try:
        energy_calculator(row)
        writer()
    except Exception:
        print(f'\nError in {id}')
        continue
