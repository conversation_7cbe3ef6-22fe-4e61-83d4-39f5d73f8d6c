 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Mg Sn Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.117317519343E+04    0.11732E+04   -0.51775E+04  4576   0.142E+03
DAV:   2     0.135390380850E+03   -0.10378E+04   -0.99013E+03  4920   0.336E+02
DAV:   3    -0.623568412237E+02   -0.19775E+03   -0.19008E+03  5344   0.162E+02
DAV:   4    -0.767155256564E+02   -0.14359E+02   -0.14113E+02  5560   0.455E+01
DAV:   5    -0.771554022311E+02   -0.43988E+00   -0.43735E+00  5464   0.812E+00    0.122E+01
DAV:   6    -0.745303082394E+02    0.26251E+01   -0.35254E+00  6448   0.781E+00    0.761E+00
DAV:   7    -0.742764285770E+02    0.25388E+00   -0.25138E+00  5752   0.409E+00    0.249E+00
DAV:   8    -0.742532691192E+02    0.23159E-01   -0.26569E-01  5568   0.235E+00    0.885E-01
DAV:   9    -0.742566066191E+02   -0.33375E-02   -0.47644E-02  5280   0.838E-01    0.292E-01
DAV:  10    -0.742574601320E+02   -0.85351E-03   -0.45932E-03  6080   0.233E-01    0.158E-01
DAV:  11    -0.742581231864E+02   -0.66305E-03   -0.87853E-04  5208   0.936E-02    0.577E-02
DAV:  12    -0.742582480525E+02   -0.12487E-03   -0.20089E-04  5696   0.465E-02    0.304E-02
DAV:  13    -0.742583131099E+02   -0.65057E-04   -0.11477E-04  5248   0.334E-02    0.103E-02
DAV:  14    -0.742583170341E+02   -0.39242E-05   -0.73317E-06  5184   0.111E-02    0.711E-03
DAV:  15    -0.742583193252E+02   -0.22911E-05   -0.41005E-06  4264   0.565E-03    0.248E-03
DAV:  16    -0.742583198159E+02   -0.49072E-06   -0.44909E-07  3304   0.264E-03
   1 F= -.74258320E+02 E0= -.74258320E+02  d E =-.124270E-22
 writing wavefunctions
