 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Re V  Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.163316903807E+04    0.16332E+04   -0.66015E+04  9968   0.145E+03
DAV:   2     0.102790208720E+03   -0.15304E+04   -0.14612E+04 10704   0.781E+02
DAV:   3    -0.107194194650E+03   -0.20998E+03   -0.19409E+03 10240   0.313E+02
DAV:   4    -0.137978135869E+03   -0.30784E+02   -0.29427E+02 10792   0.101E+02
DAV:   5    -0.141217445680E+03   -0.32393E+01   -0.31514E+01 10504   0.280E+01    0.663E+01
DAV:   6    -0.159658349499E+03   -0.18441E+02   -0.20514E+02 10352   0.558E+01    0.646E+01
DAV:   7    -0.146474008007E+03    0.13184E+02   -0.39396E+02 10256   0.119E+02    0.323E+01
DAV:   8    -0.134517291376E+03    0.11957E+02   -0.49578E+01 10560   0.503E+01    0.241E+01
DAV:   9    -0.131166039372E+03    0.33513E+01   -0.48549E+00  9848   0.171E+01    0.106E+01
DAV:  10    -0.130514025242E+03    0.65201E+00   -0.13492E+00 10688   0.637E+00    0.779E+00
DAV:  11    -0.130391343791E+03    0.12268E+00   -0.16146E-01 10280   0.205E+00    0.710E+00
DAV:  12    -0.130132205690E+03    0.25914E+00   -0.20803E-01 10304   0.208E+00    0.513E+00
DAV:  13    -0.129848718246E+03    0.28349E+00   -0.94840E-01 10328   0.483E+00    0.123E+00
DAV:  14    -0.129850432727E+03   -0.17145E-02   -0.14160E-01 10512   0.204E+00    0.995E-01
DAV:  15    -0.129850112802E+03    0.31993E-03   -0.18857E-02 10224   0.117E+00    0.461E-01
DAV:  16    -0.129848811587E+03    0.13012E-02   -0.30044E-03 10416   0.527E-01    0.314E-01
DAV:  17    -0.129847964554E+03    0.84703E-03   -0.23713E-03 10800   0.307E-01    0.147E-01
DAV:  18    -0.129847952385E+03    0.12169E-04   -0.10215E-03 10304   0.168E-01    0.950E-02
DAV:  19    -0.129847969721E+03   -0.17336E-04   -0.11290E-04 10616   0.994E-02    0.798E-02
DAV:  20    -0.129847942744E+03    0.26977E-04   -0.24757E-05  8736   0.393E-02    0.590E-02
DAV:  21    -0.129847922617E+03    0.20127E-04   -0.63741E-05  9888   0.399E-02    0.176E-02
DAV:  22    -0.129847921540E+03    0.10764E-05   -0.87211E-06  6728   0.164E-02    0.793E-03
DAV:  23    -0.129847921651E+03   -0.11114E-06   -0.84150E-07  4792   0.175E-02
   1 F= -.12984792E+03 E0= -.12984668E+03  d E =-.249193E-02
 writing wavefunctions
