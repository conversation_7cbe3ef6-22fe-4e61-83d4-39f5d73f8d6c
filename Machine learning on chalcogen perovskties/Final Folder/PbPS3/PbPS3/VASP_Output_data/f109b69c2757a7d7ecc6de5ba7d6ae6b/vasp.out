 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Si Ge Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.156180497886E+04    0.15618E+04   -0.53384E+04  4608   0.213E+03
DAV:   2     0.231641143511E+03   -0.13302E+04   -0.12712E+04  3664   0.648E+02
DAV:   3    -0.614233623817E+02   -0.29306E+03   -0.27871E+03  4120   0.192E+02
DAV:   4    -0.870623261602E+02   -0.25639E+02   -0.25118E+02  4256   0.670E+01
DAV:   5    -0.881669448239E+02   -0.11046E+01   -0.10976E+01  4528   0.125E+01    0.153E+01
DAV:   6    -0.867833854666E+02    0.13836E+01   -0.22417E+00  4880   0.675E+00    0.901E+00
DAV:   7    -0.866083388073E+02    0.17505E+00   -0.20557E+00  4472   0.379E+00    0.166E+00
DAV:   8    -0.865919121250E+02    0.16427E-01   -0.24679E-01  4072   0.218E+00    0.923E-01
DAV:   9    -0.865959449665E+02   -0.40328E-02   -0.43211E-02  4472   0.605E-01    0.343E-01
DAV:  10    -0.865965850272E+02   -0.64006E-03   -0.32764E-03  4408   0.245E-01    0.170E-01
DAV:  11    -0.865972572957E+02   -0.67227E-03   -0.13769E-03  4128   0.124E-01    0.632E-02
DAV:  12    -0.865973377763E+02   -0.80481E-04   -0.14236E-04  4400   0.420E-02    0.348E-02
DAV:  13    -0.865974098862E+02   -0.72110E-04   -0.99241E-05  3544   0.300E-02    0.144E-02
DAV:  14    -0.865974166812E+02   -0.67950E-05   -0.95517E-06  4072   0.126E-02    0.854E-03
DAV:  15    -0.865974182992E+02   -0.16180E-05   -0.45481E-06  3456   0.780E-03    0.277E-03
DAV:  16    -0.865974187017E+02   -0.40245E-06   -0.87893E-07  2416   0.375E-03
   1 F= -.86597419E+02 E0= -.86597419E+02  d E =-.253389E-27
 writing wavefunctions
