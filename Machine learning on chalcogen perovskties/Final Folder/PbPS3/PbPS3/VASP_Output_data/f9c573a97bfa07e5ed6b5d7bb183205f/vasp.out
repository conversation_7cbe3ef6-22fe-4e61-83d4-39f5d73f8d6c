 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Sn Hg Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.176114302230E+04    0.17611E+04   -0.68017E+04  6728   0.151E+03
DAV:   2     0.238544774868E+03   -0.15226E+04   -0.14533E+04  6808   0.404E+02
DAV:   3    -0.407970629437E+02   -0.27934E+03   -0.26294E+03  7680   0.168E+02
DAV:   4    -0.632450187191E+02   -0.22448E+02   -0.21684E+02  7912   0.482E+01
DAV:   5    -0.640286045073E+02   -0.78359E+00   -0.77419E+00  7800   0.916E+00    0.170E+01
DAV:   6    -0.615393984892E+02    0.24892E+01   -0.38868E+00  8416   0.119E+01    0.134E+01
DAV:   7    -0.615477455882E+02   -0.83471E-02   -0.35941E+00  7968   0.502E+00    0.540E+00
DAV:   8    -0.615059168771E+02    0.41829E-01   -0.87381E-01  7424   0.468E+00    0.209E+00
DAV:   9    -0.614936646157E+02    0.12252E-01   -0.11573E-01  7320   0.228E+00    0.436E-01
DAV:  10    -0.614947827634E+02   -0.11181E-02   -0.13243E-02  7688   0.325E-01    0.344E-01
DAV:  11    -0.614934849586E+02    0.12978E-02   -0.59474E-03  7784   0.402E-01    0.897E-02
DAV:  12    -0.614938872449E+02   -0.40229E-03   -0.25039E-03  7344   0.139E-01    0.852E-02
DAV:  13    -0.614938672681E+02    0.19977E-04   -0.88879E-05  8016   0.429E-02    0.626E-02
DAV:  14    -0.614937736677E+02    0.93600E-04   -0.24182E-04  7256   0.719E-02    0.314E-02
DAV:  15    -0.614937789457E+02   -0.52780E-05   -0.10192E-04  6864   0.271E-02    0.154E-02
DAV:  16    -0.614937787554E+02    0.19032E-06   -0.30390E-06  5984   0.733E-03
   1 F= -.61493779E+02 E0= -.61493779E+02  d E =-.276448E-19
 writing wavefunctions
