 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Nb Y  Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.138530296627E+04    0.13853E+04   -0.75498E+04  5408   0.138E+03
DAV:   2     0.213319275605E+02   -0.13640E+04   -0.13136E+04  6784   0.435E+02
DAV:   3    -0.125357946190E+03   -0.14669E+03   -0.14420E+03  7704   0.119E+02
DAV:   4    -0.138529045130E+03   -0.13171E+02   -0.13095E+02  9760   0.335E+01
DAV:   5    -0.139344103248E+03   -0.81506E+00   -0.81268E+00  8168   0.654E+00    0.587E+01
DAV:   6    -0.132346803611E+03    0.69973E+01   -0.29828E+01  7864   0.158E+01    0.387E+01
DAV:   7    -0.132869772094E+03   -0.52297E+00   -0.15405E+01  7944   0.119E+01    0.206E+01
DAV:   8    -0.131617056861E+03    0.12527E+01   -0.72591E+00  7536   0.733E+00    0.349E+00
DAV:   9    -0.131646927786E+03   -0.29871E-01   -0.93570E-01  8672   0.272E+00    0.170E+00
DAV:  10    -0.131648220379E+03   -0.12926E-02   -0.12326E-01  8184   0.117E+00    0.116E+00
DAV:  11    -0.131648703334E+03   -0.48296E-03   -0.14504E-01  8608   0.112E+00    0.858E-01
DAV:  12    -0.131639990552E+03    0.87128E-02   -0.43402E-02  7968   0.664E-01    0.146E-01
DAV:  13    -0.131640270013E+03   -0.27946E-03   -0.38560E-03  7728   0.203E-01    0.865E-02
DAV:  14    -0.131640280361E+03   -0.10348E-04   -0.36528E-04  9480   0.664E-02    0.597E-02
DAV:  15    -0.131640293711E+03   -0.13350E-04   -0.67131E-05  7648   0.296E-02    0.251E-02
DAV:  16    -0.131640306871E+03   -0.13160E-04   -0.29238E-05  6752   0.149E-02    0.833E-03
DAV:  17    -0.131640307433E+03   -0.56241E-06   -0.37270E-06  5688   0.661E-03
   1 F= -.13164031E+03 E0= -.13163957E+03  d E =-.147662E-02
 writing wavefunctions
