 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Pt Pd Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.864531984426E+03    0.86453E+03   -0.64733E+04  4992   0.129E+03
DAV:   2    -0.254394365003E+02   -0.88997E+03   -0.85029E+03  5648   0.269E+02
DAV:   3    -0.891715455347E+02   -0.63732E+02   -0.61164E+02  5728   0.673E+01
DAV:   4    -0.907085441161E+02   -0.15370E+01   -0.15229E+01  5688   0.109E+01
DAV:   5    -0.907437528161E+02   -0.35209E-01   -0.35185E-01  5920   0.153E+00    0.287E+01
DAV:   6    -0.889668171664E+02    0.17769E+01   -0.13167E+01  5400   0.165E+01    0.186E+01
DAV:   7    -0.884390524926E+02    0.52776E+00   -0.53990E+00  5248   0.132E+01    0.904E+00
DAV:   8    -0.881724175704E+02    0.26663E+00   -0.16191E+00  5296   0.825E+00    0.294E+00
DAV:   9    -0.880616016362E+02    0.11082E+00   -0.45498E-01  5320   0.478E+00    0.911E-01
DAV:  10    -0.880586908080E+02    0.29108E-02   -0.50794E-02  5392   0.109E+00    0.445E-01
DAV:  11    -0.880580038764E+02    0.68693E-03   -0.71153E-03  5584   0.510E-01    0.251E-01
DAV:  12    -0.880577938469E+02    0.21003E-03   -0.21511E-03  5528   0.312E-01    0.134E-01
DAV:  13    -0.880578526453E+02   -0.58798E-04   -0.45036E-04  5392   0.111E-01    0.326E-02
DAV:  14    -0.880578539525E+02   -0.13072E-05   -0.60306E-05  5512   0.349E-02    0.156E-02
DAV:  15    -0.880578567972E+02   -0.28447E-05   -0.10095E-05  5736   0.158E-02    0.912E-03
DAV:  16    -0.880578577872E+02   -0.98993E-06   -0.28150E-06  3592   0.959E-03
   1 F= -.88057858E+02 E0= -.88057098E+02  d E =-.151881E-02
 writing wavefunctions
