 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ti La Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.203109273270E+04    0.20311E+04   -0.59096E+04  4176   0.177E+03
DAV:   2     0.412484341129E+03   -0.16186E+04   -0.15284E+04  4616   0.481E+02
DAV:   3    -0.278805111169E+02   -0.44036E+03   -0.41473E+03  5528   0.225E+02
DAV:   4    -0.120754960863E+03   -0.92874E+02   -0.88971E+02  6816   0.983E+01
DAV:   5    -0.132409472049E+03   -0.11655E+02   -0.11401E+02  7896   0.366E+01    0.263E+01
DAV:   6    -0.131863845393E+03    0.54563E+00   -0.33826E+02  7040   0.534E+01    0.279E+01
DAV:   7    -0.122755166033E+03    0.91087E+01   -0.11524E+02  7336   0.456E+01    0.109E+01
DAV:   8    -0.122863215144E+03   -0.10805E+00   -0.12252E+01  7336   0.159E+01    0.754E+00
DAV:   9    -0.122393436987E+03    0.46978E+00   -0.82942E+00  7312   0.109E+01    0.366E+00
DAV:  10    -0.122550049429E+03   -0.15661E+00   -0.14277E+00  8128   0.485E+00    0.383E+00
DAV:  11    -0.122455486815E+03    0.94563E-01   -0.59922E-01  7736   0.282E+00    0.274E+00
DAV:  12    -0.122342579551E+03    0.11291E+00   -0.99921E-01  6968   0.374E+00    0.382E-01
DAV:  13    -0.122345688786E+03   -0.31092E-02   -0.51978E-02  7608   0.124E+00    0.226E-01
DAV:  14    -0.122345358476E+03    0.33031E-03   -0.68758E-03  8152   0.437E-01    0.129E-01
DAV:  15    -0.122345378383E+03   -0.19907E-04   -0.24641E-03  8032   0.215E-01    0.456E-02
DAV:  16    -0.122345437949E+03   -0.59566E-04   -0.31716E-04  8048   0.899E-02    0.196E-02
DAV:  17    -0.122345439791E+03   -0.18426E-05   -0.72784E-05  8320   0.481E-02    0.958E-03
DAV:  18    -0.122345439943E+03   -0.15205E-06   -0.16935E-05  6720   0.232E-02    0.444E-03
DAV:  19    -0.122345440309E+03   -0.36567E-06   -0.35506E-06  4368   0.122E-02
   1 F= -.12234544E+03 E0= -.12234423E+03  d E =-.242121E-02
 writing wavefunctions
