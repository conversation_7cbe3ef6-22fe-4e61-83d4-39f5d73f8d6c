 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ta V  Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.177106491567E+04    0.17711E+04   -0.81281E+04  6152   0.142E+03
DAV:   2     0.277554244519E+02   -0.17433E+04   -0.16750E+04  5920   0.417E+02
DAV:   3    -0.139695216207E+03   -0.16745E+03   -0.16164E+03  7040   0.116E+02
DAV:   4    -0.147229108025E+03   -0.75339E+01   -0.74359E+01  6640   0.225E+01
DAV:   5    -0.147422901512E+03   -0.19379E+00   -0.19267E+00  6856   0.351E+00    0.477E+01
DAV:   6    -0.177852520971E+03   -0.30430E+02   -0.23753E+02  7088   0.502E+01    0.721E+01
DAV:   7    -0.138304401022E+03    0.39548E+02   -0.29986E+02  7192   0.818E+01    0.260E+01
DAV:   8    -0.137144725432E+03    0.11597E+01   -0.57837E+01  6824   0.163E+01    0.133E+01
DAV:   9    -0.135267264710E+03    0.18775E+01   -0.71967E+00  6664   0.898E+00    0.836E+00
DAV:  10    -0.135055375655E+03    0.21189E+00   -0.27040E+00  7184   0.525E+00    0.645E+00
DAV:  11    -0.134708227330E+03    0.34715E+00   -0.10148E+00  7176   0.308E+00    0.339E+00
DAV:  12    -0.134611157372E+03    0.97070E-01   -0.28000E-01  6872   0.204E+00    0.175E+00
DAV:  13    -0.134594321540E+03    0.16836E-01   -0.12585E-01  6448   0.134E+00    0.878E-01
DAV:  14    -0.134597699421E+03   -0.33779E-02   -0.29102E-02  7288   0.482E-01    0.673E-01
DAV:  15    -0.134593316690E+03    0.43827E-02   -0.19390E-02  6888   0.494E-01    0.180E-01
DAV:  16    -0.134593329005E+03   -0.12315E-04   -0.14288E-03  6368   0.110E-01    0.860E-02
DAV:  17    -0.134593328037E+03    0.96753E-06   -0.25195E-04  7056   0.419E-02    0.302E-02
DAV:  18    -0.134593327771E+03    0.26632E-06   -0.32455E-05  6544   0.182E-02    0.147E-02
DAV:  19    -0.134593328907E+03   -0.11359E-05   -0.57636E-06  5568   0.992E-03    0.615E-03
DAV:  20    -0.134593328622E+03    0.28459E-06   -0.76541E-07  3328   0.367E-03
   1 F= -.13459333E+03 E0= -.13459291E+03  d E =-.830094E-03
 writing wavefunctions
