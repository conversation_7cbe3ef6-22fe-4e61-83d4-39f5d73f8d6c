 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Na As Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.905467889869E+03    0.90547E+03   -0.47977E+04  3624   0.136E+03
DAV:   2     0.393082429576E+02   -0.86616E+03   -0.84163E+03  4000   0.307E+02
DAV:   3    -0.700334329928E+02   -0.10934E+03   -0.10725E+03  4240   0.121E+02
DAV:   4    -0.748131810192E+02   -0.47797E+01   -0.47199E+01  4200   0.256E+01
DAV:   5    -0.750043452682E+02   -0.19116E+00   -0.19041E+00  4832   0.482E+00    0.130E+01
DAV:   6    -0.737496875034E+02    0.12547E+01   -0.22416E+00  4264   0.443E+00    0.814E+00
DAV:   7    -0.736755172070E+02    0.74170E-01   -0.17364E+00  4336   0.358E+00    0.179E+00
DAV:   8    -0.736493471498E+02    0.26170E-01   -0.15578E-01  4328   0.146E+00    0.711E-01
DAV:   9    -0.736547920686E+02   -0.54449E-02   -0.44063E-02  3968   0.626E-01    0.217E-01
DAV:  10    -0.736553046925E+02   -0.51262E-03   -0.14539E-03  4600   0.160E-01    0.116E-01
DAV:  11    -0.736561096800E+02   -0.80499E-03   -0.13941E-03  3840   0.124E-01    0.314E-02
DAV:  12    -0.736561388946E+02   -0.29215E-04   -0.75491E-05  4496   0.402E-02    0.191E-02
DAV:  13    -0.736561596994E+02   -0.20805E-04   -0.59968E-05  3832   0.224E-02    0.962E-03
DAV:  14    -0.736561636788E+02   -0.39794E-05   -0.70774E-06  3800   0.114E-02    0.525E-03
DAV:  15    -0.736561647196E+02   -0.10408E-05   -0.20460E-06  2720   0.529E-03    0.155E-03
DAV:  16    -0.736561645921E+02    0.12749E-06   -0.28620E-07  2304   0.242E-03
   1 F= -.73656165E+02 E0= -.73656165E+02  d E =-.981640E-35
 writing wavefunctions
