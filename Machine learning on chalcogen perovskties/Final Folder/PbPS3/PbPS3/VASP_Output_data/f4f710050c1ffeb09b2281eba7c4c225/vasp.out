 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  As Na Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.897938288072E+03    0.89794E+03   -0.47971E+04  2936   0.136E+03
DAV:   2     0.385461117629E+02   -0.85939E+03   -0.83508E+03  3328   0.310E+02
DAV:   3    -0.699037739767E+02   -0.10845E+03   -0.10642E+03  3544   0.121E+02
DAV:   4    -0.745519341064E+02   -0.46482E+01   -0.45803E+01  3440   0.252E+01
DAV:   5    -0.747425091123E+02   -0.19058E+00   -0.18989E+00  4016   0.501E+00    0.130E+01
DAV:   6    -0.733436141024E+02    0.13989E+01   -0.24672E+00  3384   0.452E+00    0.815E+00
DAV:   7    -0.732327821069E+02    0.11083E+00   -0.15407E+00  3624   0.347E+00    0.229E+00
DAV:   8    -0.732257697823E+02    0.70123E-02   -0.93436E-02  3472   0.117E+00    0.697E-01
DAV:   9    -0.732306775588E+02   -0.49078E-02   -0.29430E-02  3344   0.520E-01    0.241E-01
DAV:  10    -0.732318901898E+02   -0.12126E-02   -0.26565E-03  3520   0.189E-01    0.107E-01
DAV:  11    -0.732326720630E+02   -0.78187E-03   -0.88744E-04  3248   0.954E-02    0.481E-02
DAV:  12    -0.732328723407E+02   -0.20028E-03   -0.18253E-04  3408   0.476E-02    0.212E-02
DAV:  13    -0.732329350510E+02   -0.62710E-04   -0.83816E-05  3264   0.290E-02    0.107E-02
DAV:  14    -0.732329395121E+02   -0.44611E-05   -0.68105E-06  3168   0.954E-03    0.354E-03
DAV:  15    -0.732329401760E+02   -0.66386E-06   -0.20061E-06  2256   0.485E-03
   1 F= -.73232940E+02 E0= -.73232940E+02  d E =-.156195E-20
 writing wavefunctions
