 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Tl P  Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.777175005132E+03    0.77718E+03   -0.60573E+04  5304   0.129E+03
DAV:   2    -0.107985634897E+02   -0.78797E+03   -0.75720E+03  6192   0.261E+02
DAV:   3    -0.731928517444E+02   -0.62394E+02   -0.60976E+02  6264   0.655E+01
DAV:   4    -0.747478889558E+02   -0.15550E+01   -0.15369E+01  6272   0.102E+01
DAV:   5    -0.747961405828E+02   -0.48252E-01   -0.48150E-01  6440   0.161E+00    0.167E+01
DAV:   6    -0.739814015576E+02    0.81474E+00   -0.12068E+00  6352   0.336E+00    0.103E+01
DAV:   7    -0.738911572367E+02    0.90244E-01   -0.14406E+00  6176   0.321E+00    0.133E+00
DAV:   8    -0.738682145938E+02    0.22943E-01   -0.21724E-01  5992   0.148E+00    0.606E-01
DAV:   9    -0.738698117863E+02   -0.15972E-02   -0.35186E-02  6216   0.477E-01    0.254E-01
DAV:  10    -0.738699250931E+02   -0.11331E-03   -0.21584E-03  6384   0.165E-01    0.124E-01
DAV:  11    -0.738701208842E+02   -0.19579E-03   -0.89120E-04  6256   0.937E-02    0.459E-02
DAV:  12    -0.738701246343E+02   -0.37500E-05   -0.10950E-04  6672   0.305E-02    0.259E-02
DAV:  13    -0.738701378496E+02   -0.13215E-04   -0.55607E-05  6048   0.194E-02    0.155E-02
DAV:  14    -0.738701398116E+02   -0.19621E-05   -0.23285E-06  4304   0.434E-03    0.990E-03
DAV:  15    -0.738701386941E+02    0.11175E-05   -0.12843E-06  3856   0.448E-03    0.317E-03
DAV:  16    -0.738701385858E+02    0.10831E-06   -0.12861E-06  3360   0.266E-03
   1 F= -.73870139E+02 E0= -.73870007E+02  d E =-.263994E-03
 writing wavefunctions
