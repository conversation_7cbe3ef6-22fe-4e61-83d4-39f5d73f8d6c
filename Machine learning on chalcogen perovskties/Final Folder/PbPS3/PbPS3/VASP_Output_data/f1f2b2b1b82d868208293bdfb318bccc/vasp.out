 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Be Si Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.455859459279E+03    0.45586E+03   -0.35065E+04  3328   0.112E+03
DAV:   2    -0.376149944700E+02   -0.49347E+03   -0.47025E+03  4104   0.199E+02
DAV:   3    -0.844128587258E+02   -0.46798E+02   -0.45960E+02  4144   0.673E+01
DAV:   4    -0.856507762482E+02   -0.12379E+01   -0.12270E+01  4120   0.120E+01
DAV:   5    -0.856836461007E+02   -0.32870E-01   -0.32818E-01  4104   0.185E+00    0.159E+01
DAV:   6    -0.834201926379E+02    0.22635E+01   -0.34780E+00  3912   0.608E+00    0.891E+00
DAV:   7    -0.830667152530E+02    0.35348E+00   -0.29170E+00  3992   0.519E+00    0.210E+00
DAV:   8    -0.830459567154E+02    0.20759E-01   -0.42709E-01  3792   0.218E+00    0.125E+00
DAV:   9    -0.830478736435E+02   -0.19169E-02   -0.45007E-02  3968   0.661E-01    0.305E-01
DAV:  10    -0.830472915476E+02    0.58210E-03   -0.57368E-03  3936   0.221E-01    0.141E-01
DAV:  11    -0.830477553501E+02   -0.46380E-03   -0.11613E-03  3936   0.120E-01    0.742E-02
DAV:  12    -0.830478654340E+02   -0.11008E-03   -0.21955E-04  4128   0.479E-02    0.323E-02
DAV:  13    -0.830478985534E+02   -0.33119E-04   -0.61066E-05  3888   0.252E-02    0.121E-02
DAV:  14    -0.830479022060E+02   -0.36526E-05   -0.11875E-05  3952   0.122E-02    0.618E-03
DAV:  15    -0.830479035619E+02   -0.13559E-05   -0.33316E-06  3488   0.623E-03    0.154E-03
DAV:  16    -0.830479036129E+02   -0.50996E-07   -0.43740E-07  2288   0.268E-03
   1 F= -.83047904E+02 E0= -.83047904E+02  d E =-.105585E-13
 writing wavefunctions
