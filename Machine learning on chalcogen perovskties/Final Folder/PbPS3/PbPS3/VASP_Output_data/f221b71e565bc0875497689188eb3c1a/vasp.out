 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Rh In Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.210786115966E+04    0.21079E+04   -0.80025E+04  6264   0.162E+03
DAV:   2     0.241943997353E+03   -0.18659E+04   -0.18036E+04  5944   0.475E+02
DAV:   3    -0.580292752382E+02   -0.29997E+03   -0.27850E+03  6632   0.185E+02
DAV:   4    -0.869290461036E+02   -0.28900E+02   -0.27749E+02  7456   0.542E+01
DAV:   5    -0.879950204118E+02   -0.10660E+01   -0.10553E+01  6816   0.108E+01    0.250E+01
DAV:   6    -0.873874563932E+02    0.60756E+00   -0.12486E+01  6256   0.233E+01    0.146E+01
DAV:   7    -0.868129982806E+02    0.57446E+00   -0.10389E+01  6824   0.188E+01    0.101E+01
DAV:   8    -0.860209893410E+02    0.79201E+00   -0.24853E+00  6912   0.774E+00    0.203E+00
DAV:   9    -0.860391447420E+02   -0.18155E-01   -0.44824E-01  6768   0.264E+00    0.200E+00
DAV:  10    -0.860170323662E+02    0.22112E-01   -0.13339E-01  6704   0.161E+00    0.136E+00
DAV:  11    -0.860166536361E+02    0.37873E-03   -0.26563E-02  7912   0.797E-01    0.581E-01
DAV:  12    -0.860166649986E+02   -0.11362E-04   -0.59842E-03  8304   0.481E-01    0.151E-01
DAV:  13    -0.860165779991E+02    0.86999E-04   -0.11333E-03  7024   0.185E-01    0.696E-02
DAV:  14    -0.860165702934E+02    0.77057E-05   -0.20007E-04  7048   0.687E-02    0.322E-02
DAV:  15    -0.860165723884E+02   -0.20950E-05   -0.42916E-05  7896   0.270E-02    0.120E-02
DAV:  16    -0.860165723355E+02    0.52965E-07   -0.71274E-06  5624   0.932E-03
   1 F= -.86016572E+02 E0= -.86014884E+02  d E =-.337636E-02
 writing wavefunctions
