 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Te Sr Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.803122666913E+03    0.80312E+03   -0.53283E+04  3520   0.119E+03
DAV:   2     0.518770565461E+01   -0.79793E+03   -0.77273E+03  4272   0.251E+02
DAV:   3    -0.754664055384E+02   -0.80654E+02   -0.79656E+02  4600   0.717E+01
DAV:   4    -0.787053443513E+02   -0.32389E+01   -0.32251E+01  5416   0.146E+01
DAV:   5    -0.788821021639E+02   -0.17676E+00   -0.17669E+00  5224   0.262E+00    0.387E+01
DAV:   6    -0.751497115329E+02    0.37324E+01   -0.71845E+00  3992   0.754E+00    0.209E+01
DAV:   7    -0.751748703844E+02   -0.25159E-01   -0.36121E+00  4072   0.447E+00    0.964E+00
DAV:   8    -0.751717960560E+02    0.30743E-02   -0.18639E-01  4392   0.127E+00    0.682E+00
DAV:   9    -0.751679167307E+02    0.38793E-02   -0.71430E-02  4000   0.705E-01    0.161E+00
DAV:  10    -0.751683889700E+02   -0.47224E-03   -0.12453E-02  4448   0.288E-01    0.499E-01
DAV:  11    -0.751689058574E+02   -0.51689E-03   -0.25758E-03  4880   0.119E-01    0.255E-01
DAV:  12    -0.751691618007E+02   -0.25594E-03   -0.35815E-04  4040   0.467E-02    0.150E-01
DAV:  13    -0.751693942122E+02   -0.23241E-03   -0.23402E-04  3936   0.441E-02    0.347E-02
DAV:  14    -0.751694274756E+02   -0.33263E-04   -0.43124E-05  4464   0.171E-02    0.348E-02
DAV:  15    -0.751694331191E+02   -0.56435E-05   -0.78474E-06  4056   0.702E-03    0.159E-02
DAV:  16    -0.751694347028E+02   -0.15836E-05   -0.13089E-06  2568   0.335E-03    0.771E-03
DAV:  17    -0.751694353060E+02   -0.60322E-06   -0.95787E-07  2504   0.267E-03
   1 F= -.75169435E+02 E0= -.75169435E+02  d E =-.853207E-51
 writing wavefunctions
