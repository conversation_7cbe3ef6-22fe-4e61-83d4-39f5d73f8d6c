 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ir B  Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.845006563110E+03    0.84501E+03   -0.47448E+04  4320   0.134E+03
DAV:   2     0.597431339059E+01   -0.83903E+03   -0.79890E+03  4504   0.332E+02
DAV:   3    -0.100022746391E+03   -0.10600E+03   -0.10138E+03  5664   0.110E+02
DAV:   4    -0.105689304451E+03   -0.56666E+01   -0.55409E+01  5600   0.266E+01
DAV:   5    -0.105941467604E+03   -0.25216E+00   -0.25049E+00  6040   0.489E+00    0.313E+01
DAV:   6    -0.101722792922E+03    0.42187E+01   -0.33174E+01  6520   0.280E+01    0.130E+01
DAV:   7    -0.101182524996E+03    0.54027E+00   -0.13518E+01  6552   0.245E+01    0.978E+00
DAV:   8    -0.100619048197E+03    0.56348E+00   -0.24332E+00  6280   0.100E+01    0.307E+00
DAV:   9    -0.100617800578E+03    0.12476E-02   -0.26516E-01  5384   0.193E+00    0.782E-01
DAV:  10    -0.100616713441E+03    0.10871E-02   -0.42380E-02  6088   0.123E+00    0.397E-01
DAV:  11    -0.100614824067E+03    0.18894E-02   -0.15623E-02  6280   0.920E-01    0.231E-01
DAV:  12    -0.100614766777E+03    0.57290E-04   -0.20640E-03  5952   0.288E-01    0.981E-02
DAV:  13    -0.100614844939E+03   -0.78162E-04   -0.36784E-04  5464   0.800E-02    0.398E-02
DAV:  14    -0.100614850366E+03   -0.54268E-05   -0.59724E-05  6096   0.424E-02    0.132E-02
DAV:  15    -0.100614853185E+03   -0.28189E-05   -0.12964E-05  5128   0.181E-02    0.597E-03
DAV:  16    -0.100614853799E+03   -0.61452E-06   -0.13736E-06  2880   0.618E-03
   1 F= -.10061485E+03 E0= -.10061477E+03  d E =-.167624E-03
 writing wavefunctions
