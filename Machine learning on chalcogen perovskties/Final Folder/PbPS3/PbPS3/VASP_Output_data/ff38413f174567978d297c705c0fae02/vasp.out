 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ga Os Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.172360771628E+04    0.17236E+04   -0.56674E+04  5384   0.176E+03
DAV:   2     0.241511847587E+03   -0.14821E+04   -0.14213E+04  5656   0.563E+02
DAV:   3    -0.656801885724E+02   -0.30719E+03   -0.28637E+03  5904   0.196E+02
DAV:   4    -0.998681799193E+02   -0.34188E+02   -0.32794E+02  6944   0.647E+01
DAV:   5    -0.101086274469E+03   -0.12181E+01   -0.11939E+01  6064   0.120E+01    0.329E+01
DAV:   6    -0.999746479639E+02    0.11116E+01   -0.13019E+01  7608   0.182E+01    0.139E+01
DAV:   7    -0.999447211389E+02    0.29927E-01   -0.13332E+01  7256   0.180E+01    0.151E+01
DAV:   8    -0.988448217601E+02    0.10999E+01   -0.52541E+00  7176   0.902E+00    0.434E+00
DAV:   9    -0.984974925602E+02    0.34733E+00   -0.17242E+00  6832   0.540E+00    0.108E+00
DAV:  10    -0.985062616208E+02   -0.87691E-02   -0.17890E-01  5776   0.152E+00    0.682E-01
DAV:  11    -0.985049501387E+02    0.13115E-02   -0.15847E-02  7744   0.503E-01    0.462E-01
DAV:  12    -0.985054186184E+02   -0.46848E-03   -0.36604E-03  6136   0.307E-01    0.150E-01
DAV:  13    -0.985053239570E+02    0.94661E-04   -0.12158E-03  6432   0.205E-01    0.693E-02
DAV:  14    -0.985053498389E+02   -0.25882E-04   -0.19632E-04  5976   0.618E-02    0.343E-02
DAV:  15    -0.985053695600E+02   -0.19721E-04   -0.38130E-05  5992   0.348E-02    0.119E-02
DAV:  16    -0.985053721073E+02   -0.25473E-05   -0.97399E-06  5752   0.188E-02    0.766E-03
DAV:  17    -0.985053722809E+02   -0.17368E-06   -0.12898E-06  3184   0.581E-03
   1 F= -.98505372E+02 E0= -.98504585E+02  d E =-.157487E-02
 writing wavefunctions
