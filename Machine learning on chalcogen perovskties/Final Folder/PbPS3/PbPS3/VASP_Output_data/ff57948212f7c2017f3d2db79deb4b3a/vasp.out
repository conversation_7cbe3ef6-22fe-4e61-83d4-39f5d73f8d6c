 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  As Os Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.669367619561E+03    0.66937E+03   -0.48755E+04  4800   0.122E+03
DAV:   2    -0.337244690043E+02   -0.70309E+03   -0.66737E+03  5792   0.290E+02
DAV:   3    -0.102923144598E+03   -0.69199E+02   -0.66211E+02  5840   0.852E+01
DAV:   4    -0.105380095388E+03   -0.24570E+01   -0.24054E+01  6408   0.171E+01
DAV:   5    -0.105446980629E+03   -0.66885E-01   -0.66644E-01  6032   0.240E+00    0.344E+01
DAV:   6    -0.105064634207E+03    0.38235E+00   -0.20728E+01  7056   0.181E+01    0.152E+01
DAV:   7    -0.104736716222E+03    0.32792E+00   -0.22876E+01  8296   0.239E+01    0.173E+01
DAV:   8    -0.102758388225E+03    0.19783E+01   -0.40158E+00  7656   0.966E+00    0.136E+00
DAV:   9    -0.102772935488E+03   -0.14547E-01   -0.45500E-01  5680   0.225E+00    0.822E-01
DAV:  10    -0.102772410126E+03    0.52536E-03   -0.29203E-02  6552   0.712E-01    0.688E-01
DAV:  11    -0.102772155044E+03    0.25508E-03   -0.64676E-03  6392   0.379E-01    0.487E-01
DAV:  12    -0.102771990491E+03    0.16455E-03   -0.33903E-03  5544   0.210E-01    0.109E-01
DAV:  13    -0.102772035332E+03   -0.44841E-04   -0.68205E-04  6152   0.916E-02    0.687E-02
DAV:  14    -0.102772034662E+03    0.67085E-06   -0.57683E-05  5832   0.326E-02    0.526E-02
DAV:  15    -0.102772040303E+03   -0.56413E-05   -0.19225E-05  5496   0.137E-02    0.179E-02
DAV:  16    -0.102772040465E+03   -0.16168E-06   -0.36458E-06  3984   0.657E-03
   1 F= -.10277204E+03 E0= -.10277080E+03  d E =-.248974E-02
 writing wavefunctions
