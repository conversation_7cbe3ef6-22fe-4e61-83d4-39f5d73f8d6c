 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Rb Sb Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.799824087392E+03    0.79982E+03   -0.47515E+04  2560   0.121E+03
DAV:   2     0.150024080978E+02   -0.78482E+03   -0.75769E+03  3104   0.237E+02
DAV:   3    -0.726063932075E+02   -0.87609E+02   -0.86288E+02  3304   0.811E+01
DAV:   4    -0.748521314251E+02   -0.22457E+01   -0.22298E+01  3056   0.142E+01
DAV:   5    -0.749813122038E+02   -0.12918E+00   -0.12911E+00  4216   0.261E+00    0.137E+01
DAV:   6    -0.726857461906E+02    0.22956E+01   -0.34774E+00  2856   0.485E+00    0.884E+00
DAV:   7    -0.725815885315E+02    0.10416E+00   -0.17180E+00  2984   0.299E+00    0.285E+00
DAV:   8    -0.725649084004E+02    0.16680E-01   -0.11955E-01  2944   0.101E+00    0.125E+00
DAV:   9    -0.725711444040E+02   -0.62360E-02   -0.26128E-02  2920   0.491E-01    0.235E-01
DAV:  10    -0.725718861939E+02   -0.74179E-03   -0.19167E-03  3320   0.118E-01    0.110E-01
DAV:  11    -0.725724993333E+02   -0.61314E-03   -0.77794E-04  2864   0.861E-02    0.445E-02
DAV:  12    -0.725725891451E+02   -0.89812E-04   -0.11003E-04  3120   0.315E-02    0.223E-02
DAV:  13    -0.725726120611E+02   -0.22916E-04   -0.33123E-05  2944   0.168E-02    0.954E-03
DAV:  14    -0.725726149242E+02   -0.28631E-05   -0.47432E-06  2576   0.614E-03    0.285E-03
DAV:  15    -0.725726156230E+02   -0.69877E-06   -0.76826E-07  1800   0.278E-03
   1 F= -.72572616E+02 E0= -.72572616E+02  d E =-.403173E-31
 writing wavefunctions
