 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ir Pt Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.106518924532E+04    0.10652E+04   -0.60603E+04  5280   0.138E+03
DAV:   2     0.182041084536E+02   -0.10470E+04   -0.99520E+03  5480   0.333E+02
DAV:   3    -0.100380518239E+03   -0.11858E+03   -0.11066E+03  6808   0.108E+02
DAV:   4    -0.106417759999E+03   -0.60372E+01   -0.58462E+01  6656   0.252E+01
DAV:   5    -0.106663213179E+03   -0.24545E+00   -0.24322E+00  6840   0.465E+00    0.371E+01
DAV:   6    -0.104005267193E+03    0.26579E+01   -0.24624E+01  7368   0.254E+01    0.214E+01
DAV:   7    -0.103182900897E+03    0.82237E+00   -0.11253E+01  7680   0.193E+01    0.112E+01
DAV:   8    -0.102753728697E+03    0.42917E+00   -0.31224E+00  7696   0.126E+01    0.385E+00
DAV:   9    -0.102512498117E+03    0.24123E+00   -0.82365E-01  7528   0.682E+00    0.110E+00
DAV:  10    -0.102506979762E+03    0.55184E-02   -0.12078E-01  7104   0.193E+00    0.606E-01
DAV:  11    -0.102504434875E+03    0.25449E-02   -0.21636E-02  7672   0.985E-01    0.380E-01
DAV:  12    -0.102503992254E+03    0.44262E-03   -0.51054E-03  7112   0.425E-01    0.177E-01
DAV:  13    -0.102503978819E+03    0.13435E-04   -0.12687E-03  5936   0.224E-01    0.964E-02
DAV:  14    -0.102503957068E+03    0.21752E-04   -0.25671E-04  5992   0.112E-01    0.387E-02
DAV:  15    -0.102503966200E+03   -0.91325E-05   -0.48801E-05  6248   0.322E-02    0.171E-02
DAV:  16    -0.102503967746E+03   -0.15460E-05   -0.11662E-05  6232   0.179E-02    0.449E-03
DAV:  17    -0.102503967516E+03    0.22983E-06   -0.10718E-06  2744   0.663E-03
   1 F= -.10250397E+03 E0= -.10250346E+03  d E =-.100630E-02
 writing wavefunctions
