 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  B  Sb Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.584044322250E+03    0.58404E+03   -0.37981E+04  3072   0.123E+03
DAV:   2    -0.250159279650E+02   -0.60906E+03   -0.59202E+03  3696   0.219E+02
DAV:   3    -0.878851565088E+02   -0.62869E+02   -0.61743E+02  3640   0.767E+01
DAV:   4    -0.900214979506E+02   -0.21363E+01   -0.21144E+01  3992   0.137E+01
DAV:   5    -0.900710038603E+02   -0.49506E-01   -0.49405E-01  3896   0.213E+00    0.171E+01
DAV:   6    -0.848092885054E+02    0.52617E+01   -0.19147E+01  3792   0.143E+01    0.887E+00
DAV:   7    -0.850180061579E+02   -0.20872E+00   -0.67668E+00  3728   0.724E+00    0.462E+00
DAV:   8    -0.849450766556E+02    0.72930E-01   -0.49747E-01  3672   0.240E+00    0.199E+00
DAV:   9    -0.849471289594E+02   -0.20523E-02   -0.80552E-02  3544   0.991E-01    0.364E-01
DAV:  10    -0.849475876373E+02   -0.45868E-03   -0.80693E-03  3936   0.279E-01    0.105E-01
DAV:  11    -0.849477313645E+02   -0.14373E-03   -0.90861E-04  3648   0.104E-01    0.590E-02
DAV:  12    -0.849478313137E+02   -0.99949E-04   -0.23239E-04  3912   0.485E-02    0.282E-02
DAV:  13    -0.849478696708E+02   -0.38357E-04   -0.51312E-05  3456   0.260E-02    0.813E-03
DAV:  14    -0.849478745140E+02   -0.48432E-05   -0.79930E-06  3736   0.940E-03    0.488E-03
DAV:  15    -0.849478755320E+02   -0.10179E-05   -0.15000E-06  2192   0.461E-03    0.135E-03
DAV:  16    -0.849478756806E+02   -0.14866E-06   -0.36801E-07  2112   0.218E-03
   1 F= -.84947876E+02 E0= -.84947722E+02  d E =-.307249E-03
 writing wavefunctions
