 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Sn Pd Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.179622890888E+04    0.17962E+04   -0.64602E+04  5648   0.166E+03
DAV:   2     0.241344409290E+03   -0.15549E+04   -0.14984E+04  5544   0.481E+02
DAV:   3    -0.550319837097E+02   -0.29638E+03   -0.27767E+03  6080   0.178E+02
DAV:   4    -0.824238553608E+02   -0.27392E+02   -0.26178E+02  6488   0.556E+01
DAV:   5    -0.835277353917E+02   -0.11039E+01   -0.10884E+01  6288   0.115E+01    0.195E+01
DAV:   6    -0.822378970623E+02    0.12898E+01   -0.57451E+00  5960   0.125E+01    0.123E+01
DAV:   7    -0.820556402847E+02    0.18226E+00   -0.38744E+00  6216   0.112E+01    0.613E+00
DAV:   8    -0.818304931530E+02    0.22515E+00   -0.71299E-01  5976   0.457E+00    0.118E+00
DAV:   9    -0.818268103266E+02    0.36828E-02   -0.11535E-01  6480   0.148E+00    0.543E-01
DAV:  10    -0.818252512887E+02    0.15590E-02   -0.15896E-02  5960   0.695E-01    0.313E-01
DAV:  11    -0.818251606734E+02    0.90615E-04   -0.37701E-03  6144   0.310E-01    0.165E-01
DAV:  12    -0.818252557212E+02   -0.95048E-04   -0.10104E-03  5736   0.178E-01    0.661E-02
DAV:  13    -0.818252711164E+02   -0.15395E-04   -0.20540E-04  6144   0.698E-02    0.163E-02
DAV:  14    -0.818252719638E+02   -0.84732E-06   -0.22717E-05  5944   0.245E-02    0.102E-02
DAV:  15    -0.818252727254E+02   -0.76162E-06   -0.57042E-06  5488   0.997E-03
   1 F= -.81825273E+02 E0= -.81825008E+02  d E =-.528779E-03
 writing wavefunctions
