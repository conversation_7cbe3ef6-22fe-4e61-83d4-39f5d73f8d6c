 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Pb Pt Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.154718188249E+04    0.15472E+04   -0.68491E+04  5408   0.144E+03
DAV:   2     0.152750143760E+03   -0.13944E+04   -0.13244E+04  5648   0.374E+02
DAV:   3    -0.729960995057E+02   -0.22575E+03   -0.21439E+03  7208   0.170E+02
DAV:   4    -0.868952294809E+02   -0.13899E+02   -0.13596E+02  7048   0.425E+01
DAV:   5    -0.873673344457E+02   -0.47210E+00   -0.46839E+00  6848   0.772E+00    0.231E+01
DAV:   6    -0.853129207184E+02    0.20544E+01   -0.92416E+00  6496   0.136E+01    0.144E+01
DAV:   7    -0.849563198631E+02    0.35660E+00   -0.40316E+00  6816   0.961E+00    0.601E+00
DAV:   8    -0.848244240249E+02    0.13190E+00   -0.50939E-01  6192   0.285E+00    0.156E+00
DAV:   9    -0.848120494068E+02    0.12375E-01   -0.15056E-01  6832   0.176E+00    0.536E-01
DAV:  10    -0.848100824973E+02    0.19669E-02   -0.18480E-02  6344   0.570E-01    0.292E-01
DAV:  11    -0.848102241099E+02   -0.14161E-03   -0.23487E-03  7096   0.157E-01    0.139E-01
DAV:  12    -0.848102366256E+02   -0.12516E-04   -0.38868E-04  6360   0.998E-02    0.589E-02
DAV:  13    -0.848102388751E+02   -0.22495E-05   -0.21435E-04  6096   0.587E-02    0.285E-02
DAV:  14    -0.848102317200E+02    0.71552E-05   -0.27801E-05  6192   0.269E-02    0.144E-02
DAV:  15    -0.848102313305E+02    0.38947E-06   -0.90459E-06  5624   0.109E-02
   1 F= -.84810231E+02 E0= -.84810204E+02  d E =-.553058E-04
 writing wavefunctions
