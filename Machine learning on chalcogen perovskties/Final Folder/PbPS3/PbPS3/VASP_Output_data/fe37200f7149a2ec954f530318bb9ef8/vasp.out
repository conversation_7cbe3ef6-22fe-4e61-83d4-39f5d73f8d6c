 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  V  Ru Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.188941631353E+04    0.18894E+04   -0.90875E+04  7824   0.141E+03
DAV:   2     0.482580096684E+02   -0.18412E+04   -0.17620E+04  7472   0.414E+02
DAV:   3    -0.120742752272E+03   -0.16900E+03   -0.16081E+03  8816   0.111E+02
DAV:   4    -0.128822016683E+03   -0.80793E+01   -0.79475E+01  8112   0.220E+01
DAV:   5    -0.129000774592E+03   -0.17876E+00   -0.17793E+00  8312   0.352E+00    0.454E+01
DAV:   6    -0.174008812628E+03   -0.45008E+02   -0.27344E+02  9040   0.518E+01    0.848E+01
DAV:   7    -0.123386047112E+03    0.50623E+02   -0.30485E+02  8920   0.875E+01    0.261E+01
DAV:   8    -0.120552323114E+03    0.28337E+01   -0.47686E+01  8384   0.219E+01    0.175E+01
DAV:   9    -0.117659368480E+03    0.28930E+01   -0.92299E+00  7712   0.123E+01    0.638E+00
DAV:  10    -0.117474366326E+03    0.18500E+00   -0.98070E-01  9000   0.278E+00    0.338E+00
DAV:  11    -0.117403048442E+03    0.71318E-01   -0.22219E-01  8336   0.198E+00    0.152E+00
DAV:  12    -0.117399944394E+03    0.31040E-02   -0.17789E-01  8760   0.193E+00    0.132E+00
DAV:  13    -0.117384910589E+03    0.15034E-01   -0.71754E-02  8736   0.122E+00    0.822E-01
DAV:  14    -0.117388425965E+03   -0.35154E-02   -0.25182E-02  8912   0.776E-01    0.102E+00
DAV:  15    -0.117384366421E+03    0.40595E-02   -0.11126E-02  8896   0.560E-01    0.585E-01
DAV:  16    -0.117381560784E+03    0.28056E-02   -0.78310E-03  8224   0.374E-01    0.740E-02
DAV:  17    -0.117381563759E+03   -0.29749E-05   -0.44343E-04  8272   0.652E-02    0.423E-02
DAV:  18    -0.117381561716E+03    0.20429E-05   -0.23217E-05  8048   0.133E-02    0.268E-02
DAV:  19    -0.117381563179E+03   -0.14622E-05   -0.11212E-05  7792   0.104E-02    0.135E-02
DAV:  20    -0.117381563190E+03   -0.11321E-07   -0.65198E-06  7008   0.991E-03
   1 F= -.11738156E+03 E0= -.11737997E+03  d E =-.317979E-02
 writing wavefunctions
