 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Cu As Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.121218827102E+04    0.12122E+04   -0.48400E+04  4224   0.137E+03
DAV:   2     0.124880308031E+03   -0.10873E+04   -0.10336E+04  4464   0.350E+02
DAV:   3    -0.652758193642E+02   -0.19016E+03   -0.17894E+03  5608   0.157E+02
DAV:   4    -0.808199074719E+02   -0.15544E+02   -0.15161E+02  6344   0.512E+01
DAV:   5    -0.811386223612E+02   -0.31871E+00   -0.31695E+00  6208   0.815E+00    0.270E+01
DAV:   6    -0.895010037230E+02   -0.83624E+01   -0.15234E+02  5952   0.194E+02    0.235E+01
DAV:   7    -0.754481435939E+02    0.14053E+02   -0.46854E+01  5872   0.891E+01    0.903E+00
DAV:   8    -0.755332581755E+02   -0.85115E-01   -0.17513E+01  6864   0.750E+01    0.893E+00
DAV:   9    -0.750150282510E+02    0.51823E+00   -0.84442E+00  6088   0.466E+01    0.282E+00
DAV:  10    -0.749518074444E+02    0.63221E-01   -0.95915E-01  6640   0.152E+01    0.827E-01
DAV:  11    -0.749631519075E+02   -0.11344E-01   -0.17602E-01  6544   0.440E+00    0.830E-01
DAV:  12    -0.749598059297E+02    0.33460E-02   -0.20077E-02  6536   0.229E+00    0.338E-01
DAV:  13    -0.749598544024E+02   -0.48473E-04   -0.71469E-03  6312   0.766E-01    0.115E-01
DAV:  14    -0.749596649075E+02    0.18949E-03   -0.64516E-04  6264   0.338E-01    0.596E-02
DAV:  15    -0.749596628139E+02    0.20936E-05   -0.27522E-04  6240   0.133E-01    0.287E-02
DAV:  16    -0.749596569427E+02    0.58712E-05   -0.13251E-04  6536   0.157E-01    0.145E-02
DAV:  17    -0.749596597153E+02   -0.27727E-05   -0.32417E-05  5888   0.508E-02    0.579E-03
DAV:  18    -0.749596608774E+02   -0.11621E-05   -0.25847E-06  3752   0.929E-03    0.565E-03
DAV:  19    -0.749596605394E+02    0.33802E-06   -0.31916E-06  3248   0.373E-02
   1 F= -.74959661E+02 E0= -.74958619E+02  d E =-.208301E-02
 writing wavefunctions
