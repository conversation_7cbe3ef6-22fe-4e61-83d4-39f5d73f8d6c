 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Cd V  Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.157780515307E+04    0.15778E+04   -0.81951E+04  6120   0.141E+03
DAV:   2     0.609464208414E+02   -0.15169E+04   -0.14538E+04  5872   0.394E+02
DAV:   3    -0.933959550241E+02   -0.15434E+03   -0.14921E+03  6816   0.115E+02
DAV:   4    -0.993425958818E+02   -0.59466E+01   -0.58691E+01  6352   0.199E+01
DAV:   5    -0.994777987527E+02   -0.13520E+00   -0.13469E+00  6480   0.293E+00    0.407E+01
DAV:   6    -0.135585506657E+03   -0.36108E+02   -0.28105E+02  6936   0.535E+01    0.748E+01
DAV:   7    -0.893628665337E+02    0.46223E+02   -0.28293E+02  6872   0.779E+01    0.213E+01
DAV:   8    -0.889578957275E+02    0.40497E+00   -0.27023E+01  6264   0.124E+01    0.127E+01
DAV:   9    -0.895824970154E+02   -0.62460E+00   -0.12511E+01  6808   0.902E+00    0.165E+01
DAV:  10    -0.874721189202E+02    0.21104E+01   -0.49877E+00  7056   0.832E+00    0.369E+00
DAV:  11    -0.873819181867E+02    0.90201E-01   -0.45427E-01  6640   0.193E+00    0.184E+00
DAV:  12    -0.873551108982E+02    0.26807E-01   -0.16119E-01  6704   0.140E+00    0.801E-01
DAV:  13    -0.873522130765E+02    0.28978E-02   -0.24561E-02  6336   0.419E-01    0.368E-01
DAV:  14    -0.873525754779E+02   -0.36240E-03   -0.49569E-03  6440   0.191E-01    0.286E-01
DAV:  15    -0.873520094342E+02    0.56604E-03   -0.15976E-03  6360   0.151E-01    0.107E-01
DAV:  16    -0.873519667943E+02    0.42640E-04   -0.37293E-04  6608   0.630E-02    0.430E-02
DAV:  17    -0.873519596215E+02    0.71728E-05   -0.95313E-05  6472   0.309E-02    0.163E-02
DAV:  18    -0.873519631326E+02   -0.35111E-05   -0.11389E-05  5824   0.134E-02    0.127E-02
DAV:  19    -0.873519637447E+02   -0.61219E-06   -0.15761E-06  3656   0.508E-03
   1 F= -.87351964E+02 E0= -.87350573E+02  d E =-.278070E-02
 writing wavefunctions
