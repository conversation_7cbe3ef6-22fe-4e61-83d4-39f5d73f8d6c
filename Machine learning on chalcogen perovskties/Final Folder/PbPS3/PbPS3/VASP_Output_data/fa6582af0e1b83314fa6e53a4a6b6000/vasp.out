 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ir Ca Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.125666999059E+04    0.12567E+04   -0.60578E+04  4576   0.138E+03
DAV:   2     0.668677577002E+02   -0.11898E+04   -0.11477E+04  4616   0.397E+02
DAV:   3    -0.935540255316E+02   -0.16042E+03   -0.15400E+03  6632   0.123E+02
DAV:   4    -0.103848406358E+03   -0.10294E+02   -0.10127E+02  6744   0.315E+01
DAV:   5    -0.104635533171E+03   -0.78713E+00   -0.78560E+00  8120   0.712E+00    0.315E+01
DAV:   6    -0.101579215080E+03    0.30563E+01   -0.40964E+01  6488   0.338E+01    0.176E+01
DAV:   7    -0.996321099745E+02    0.19471E+01   -0.18566E+01  6464   0.258E+01    0.994E+00
DAV:   8    -0.994581633339E+02    0.17395E+00   -0.20047E+00  5656   0.564E+00    0.275E+00
DAV:   9    -0.993953769947E+02    0.62786E-01   -0.55757E-01  6464   0.363E+00    0.121E+00
DAV:  10    -0.993845710307E+02    0.10806E-01   -0.21029E-01  6976   0.214E+00    0.631E-01
DAV:  11    -0.993848038169E+02   -0.23279E-03   -0.32882E-02  6120   0.750E-01    0.242E-01
DAV:  12    -0.993852862940E+02   -0.48248E-03   -0.50057E-03  6456   0.256E-01    0.124E-01
DAV:  13    -0.993855999845E+02   -0.31369E-03   -0.12564E-03  6288   0.138E-01    0.679E-02
DAV:  14    -0.993857804907E+02   -0.18051E-03   -0.28061E-04  5336   0.508E-02    0.301E-02
DAV:  15    -0.993858531961E+02   -0.72705E-04   -0.10620E-04  5248   0.349E-02    0.122E-02
DAV:  16    -0.993858558925E+02   -0.26964E-05   -0.13377E-05  6448   0.131E-02    0.402E-03
DAV:  17    -0.993858562188E+02   -0.32635E-06   -0.10875E-06  2992   0.441E-03
   1 F= -.99385856E+02 E0= -.99385027E+02  d E =-.165809E-02
 writing wavefunctions
