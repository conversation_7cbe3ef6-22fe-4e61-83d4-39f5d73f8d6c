 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ti Sn Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.213579411999E+04    0.21358E+04   -0.70690E+04  5880   0.162E+03
DAV:   2     0.319555795070E+03   -0.18162E+04   -0.17561E+04  5824   0.495E+02
DAV:   3    -0.690191072928E+02   -0.38857E+03   -0.35828E+03  7560   0.198E+02
DAV:   4    -0.112209968820E+03   -0.43191E+02   -0.42569E+02  7944   0.651E+01
DAV:   5    -0.116039462534E+03   -0.38295E+01   -0.38200E+01  9312   0.155E+01    0.252E+01
DAV:   6    -0.112213323398E+03    0.38261E+01   -0.81330E+01  8320   0.348E+01    0.323E+01
DAV:   7    -0.105576811689E+03    0.66365E+01   -0.80046E+01  7912   0.372E+01    0.123E+01
DAV:   8    -0.103778814203E+03    0.17980E+01   -0.72262E+00  7192   0.897E+00    0.557E+00
DAV:   9    -0.103462272534E+03    0.31654E+00   -0.22690E+00  8712   0.539E+00    0.297E+00
DAV:  10    -0.103414308754E+03    0.47964E-01   -0.42182E-01  7536   0.275E+00    0.673E-01
DAV:  11    -0.103421997838E+03   -0.76891E-02   -0.33947E-02  7720   0.985E-01    0.605E-01
DAV:  12    -0.103419636684E+03    0.23612E-02   -0.97571E-03  7640   0.781E-01    0.413E-01
DAV:  13    -0.103417900828E+03    0.17359E-02   -0.46041E-03  7624   0.452E-01    0.129E-01
DAV:  14    -0.103418063154E+03   -0.16233E-03   -0.10345E-03  7072   0.134E-01    0.599E-02
DAV:  15    -0.103418063745E+03   -0.59075E-06   -0.10611E-04  7728   0.407E-02    0.362E-02
DAV:  16    -0.103418072253E+03   -0.85081E-05   -0.26920E-05  6744   0.193E-02    0.183E-02
DAV:  17    -0.103418070956E+03    0.12970E-05   -0.11925E-05  7352   0.133E-02    0.404E-03
DAV:  18    -0.103418071849E+03   -0.89384E-06   -0.13451E-06  4184   0.409E-03
   1 F= -.10341807E+03 E0= -.10341807E+03  d E =-.145400E-27
 writing wavefunctions
