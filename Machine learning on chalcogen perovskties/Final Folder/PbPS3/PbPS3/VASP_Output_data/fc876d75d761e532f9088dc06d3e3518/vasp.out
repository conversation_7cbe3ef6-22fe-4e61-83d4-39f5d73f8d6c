 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Al Rh Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.101349275025E+04    0.10135E+04   -0.67458E+04  5296   0.135E+03
DAV:   2    -0.253507371050E+01   -0.10160E+04   -0.97487E+03  5400   0.315E+02
DAV:   3    -0.917565403633E+02   -0.89221E+02   -0.85590E+02  5560   0.838E+01
DAV:   4    -0.945701194240E+02   -0.28136E+01   -0.27827E+01  5224   0.147E+01
DAV:   5    -0.946340552404E+02   -0.63936E-01   -0.63810E-01  5520   0.225E+00    0.227E+01
DAV:   6    -0.935893766094E+02    0.10447E+01   -0.84156E+00  5168   0.150E+01    0.119E+01
DAV:   7    -0.934417915262E+02    0.14759E+00   -0.73058E+00  5176   0.161E+01    0.906E+00
DAV:   8    -0.927696637113E+02    0.67213E+00   -0.16607E+00  5016   0.777E+00    0.137E+00
DAV:   9    -0.927814860497E+02   -0.11822E-01   -0.24455E-01  5208   0.170E+00    0.659E-01
DAV:  10    -0.927772520966E+02    0.42340E-02   -0.24931E-02  5088   0.805E-01    0.269E-01
DAV:  11    -0.927776706335E+02   -0.41854E-03   -0.57788E-03  5272   0.466E-01    0.211E-01
DAV:  12    -0.927772626890E+02    0.40794E-03   -0.20421E-03  5256   0.313E-01    0.632E-02
DAV:  13    -0.927773219229E+02   -0.59234E-04   -0.39407E-04  4992   0.797E-02    0.303E-02
DAV:  14    -0.927773350471E+02   -0.13124E-04   -0.41141E-05  5096   0.311E-02    0.157E-02
DAV:  15    -0.927773409637E+02   -0.59166E-05   -0.11731E-05  4808   0.148E-02    0.434E-03
DAV:  16    -0.927773420841E+02   -0.11203E-05   -0.13563E-06  3160   0.418E-03    0.179E-03
DAV:  17    -0.927773421151E+02   -0.30981E-07   -0.34289E-07  2864   0.176E-03
   1 F= -.92777342E+02 E0= -.92776618E+02  d E =-.144876E-02
 writing wavefunctions
