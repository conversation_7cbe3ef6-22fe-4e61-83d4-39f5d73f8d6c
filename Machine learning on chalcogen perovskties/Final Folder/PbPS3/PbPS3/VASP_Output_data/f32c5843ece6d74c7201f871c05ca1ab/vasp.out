 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Pd La Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.169754005488E+04    0.16975E+04   -0.53364E+04  2944   0.183E+03
DAV:   2     0.365147855836E+03   -0.13324E+04   -0.12385E+04  3176   0.443E+02
DAV:   3     0.110919171481E+02   -0.35406E+03   -0.32768E+03  3400   0.213E+02
DAV:   4    -0.807178658766E+02   -0.91810E+02   -0.84949E+02  4872   0.935E+01
DAV:   5    -0.103467593720E+03   -0.22750E+02   -0.21736E+02  5224   0.456E+01    0.216E+01
DAV:   6    -0.105884586086E+03   -0.24170E+01   -0.11168E+02  5072   0.450E+01    0.200E+01
DAV:   7    -0.103808460874E+03    0.20761E+01   -0.21918E+01  4816   0.262E+01    0.823E+00
DAV:   8    -0.104129549754E+03   -0.32109E+00   -0.78200E+00  4816   0.114E+01    0.474E+00
DAV:   9    -0.103966343663E+03    0.16321E+00   -0.16642E+00  4808   0.739E+00    0.101E+00
DAV:  10    -0.103970981907E+03   -0.46382E-02   -0.29160E-01  5376   0.239E+00    0.554E-01
DAV:  11    -0.103970584613E+03    0.39729E-03   -0.44310E-02  5088   0.114E+00    0.390E-01
DAV:  12    -0.103970678638E+03   -0.94025E-04   -0.18594E-02  5112   0.686E-01    0.157E-01
DAV:  13    -0.103970851178E+03   -0.17254E-03   -0.29379E-03  4912   0.259E-01    0.791E-02
DAV:  14    -0.103971018729E+03   -0.16755E-03   -0.11088E-03  5336   0.141E-01    0.427E-02
DAV:  15    -0.103971064743E+03   -0.46014E-04   -0.17362E-04  5240   0.706E-02    0.213E-02
DAV:  16    -0.103971084545E+03   -0.19801E-04   -0.56931E-05  4640   0.365E-02    0.863E-03
DAV:  17    -0.103971087649E+03   -0.31042E-05   -0.10530E-05  4456   0.149E-02    0.389E-03
DAV:  18    -0.103971087762E+03   -0.11320E-06   -0.13821E-06  2328   0.927E-03
   1 F= -.10397109E+03 E0= -.10397022E+03  d E =-.172750E-02
 writing wavefunctions
