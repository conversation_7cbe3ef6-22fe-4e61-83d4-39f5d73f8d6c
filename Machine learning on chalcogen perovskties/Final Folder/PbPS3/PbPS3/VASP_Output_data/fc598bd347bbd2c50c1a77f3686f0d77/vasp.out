 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Tl Sc Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.178363396089E+04    0.17836E+04   -0.67439E+04  6496   0.133E+03
DAV:   2     0.133717138198E+03   -0.16499E+04   -0.16110E+04  6312   0.379E+02
DAV:   3    -0.858140454711E+02   -0.21953E+03   -0.20977E+03  9816   0.143E+02
DAV:   4    -0.100645825771E+03   -0.14832E+02   -0.14632E+02  9888   0.434E+01
DAV:   5    -0.100971182198E+03   -0.32536E+00   -0.32352E+00 10040   0.807E+00    0.201E+01
DAV:   6    -0.937587678228E+02    0.72124E+01   -0.24558E+01  9576   0.201E+01    0.909E+00
DAV:   7    -0.932529849457E+02    0.50578E+00   -0.37494E+00  9080   0.610E+00    0.504E+00
DAV:   8    -0.932093316628E+02    0.43653E-01   -0.10115E+00  9768   0.417E+00    0.145E+00
DAV:   9    -0.931951767634E+02    0.14155E-01   -0.13154E-01  9368   0.150E+00    0.587E-01
DAV:  10    -0.931980508041E+02   -0.28740E-02   -0.19076E-02  8576   0.577E-01    0.348E-01
DAV:  11    -0.931974490920E+02    0.60171E-03   -0.33938E-03  9752   0.290E-01    0.131E-01
DAV:  12    -0.931977562391E+02   -0.30715E-03   -0.64631E-04  9456   0.889E-02    0.442E-02
DAV:  13    -0.931978098965E+02   -0.53657E-04   -0.90480E-05  9336   0.460E-02    0.302E-02
DAV:  14    -0.931978125634E+02   -0.26669E-05   -0.20079E-05  8432   0.228E-02    0.142E-02
DAV:  15    -0.931978123721E+02    0.19130E-06   -0.76467E-06  7776   0.125E-02
   1 F= -.93197812E+02 E0= -.93197812E+02  d E =-.524363E-25
 writing wavefunctions
