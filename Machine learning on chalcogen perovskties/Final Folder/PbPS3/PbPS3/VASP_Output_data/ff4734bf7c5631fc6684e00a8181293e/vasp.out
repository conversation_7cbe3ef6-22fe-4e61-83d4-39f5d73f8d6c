 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Os Si Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.640327242924E+03    0.64033E+03   -0.47559E+04  4320   0.131E+03
DAV:   2    -0.366493683893E+02   -0.67698E+03   -0.64972E+03  5240   0.308E+02
DAV:   3    -0.107829010866E+03   -0.71180E+02   -0.69397E+02  5528   0.899E+01
DAV:   4    -0.110557557324E+03   -0.27285E+01   -0.27027E+01  5848   0.191E+01
DAV:   5    -0.110631086382E+03   -0.73529E-01   -0.73265E-01  5792   0.275E+00    0.321E+01
DAV:   6    -0.108825629261E+03    0.18055E+01   -0.29144E+01  6424   0.215E+01    0.148E+01
DAV:   7    -0.108239815556E+03    0.58581E+00   -0.28240E+01  7496   0.244E+01    0.137E+01
DAV:   8    -0.106933015133E+03    0.13068E+01   -0.34839E+00  6576   0.876E+00    0.201E+00
DAV:   9    -0.106889238031E+03    0.43777E-01   -0.67485E-01  5928   0.282E+00    0.954E-01
DAV:  10    -0.106885628258E+03    0.36098E-02   -0.46233E-02  5112   0.804E-01    0.529E-01
DAV:  11    -0.106885898635E+03   -0.27038E-03   -0.84751E-03  6784   0.485E-01    0.356E-01
DAV:  12    -0.106885082704E+03    0.81593E-03   -0.37820E-03  7152   0.404E-01    0.199E-01
DAV:  13    -0.106885059794E+03    0.22910E-04   -0.87973E-04  5936   0.145E-01    0.851E-02
DAV:  14    -0.106885073669E+03   -0.13875E-04   -0.16875E-04  6088   0.523E-02    0.405E-02
DAV:  15    -0.106885064422E+03    0.92470E-05   -0.39090E-05  6160   0.377E-02    0.153E-02
DAV:  16    -0.106885063605E+03    0.81665E-06   -0.11478E-05  4808   0.137E-02    0.723E-03
DAV:  17    -0.106885063614E+03   -0.92987E-08   -0.94340E-07  2880   0.384E-03
   1 F= -.10688506E+03 E0= -.10688450E+03  d E =-.112820E-02
 writing wavefunctions
