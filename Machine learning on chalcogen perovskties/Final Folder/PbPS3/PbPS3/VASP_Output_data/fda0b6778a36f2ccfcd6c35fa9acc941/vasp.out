 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ni Os Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.118511339605E+04    0.11851E+04   -0.55343E+04  4576   0.143E+03
DAV:   2     0.731050871063E+02   -0.11120E+04   -0.10617E+04  4704   0.396E+02
DAV:   3    -0.981889395680E+02   -0.17129E+03   -0.15710E+03  5360   0.156E+02
DAV:   4    -0.111136858263E+03   -0.12948E+02   -0.12456E+02  5936   0.417E+01
DAV:   5    -0.111710290622E+03   -0.57343E+00   -0.56736E+00  6656   0.845E+00    0.403E+01
DAV:   6    -0.127244531692E+03   -0.15534E+02   -0.10730E+02  6272   0.133E+02    0.476E+01
DAV:   7    -0.104915198003E+03    0.22329E+02   -0.50563E+01  6016   0.111E+02    0.120E+01
DAV:   8    -0.107986884311E+03   -0.30717E+01   -0.27274E+01  6864   0.356E+01    0.207E+01
DAV:   9    -0.106121836264E+03    0.18650E+01   -0.84844E+00  6568   0.383E+01    0.762E+00
DAV:  10    -0.105640838545E+03    0.48100E+00   -0.10658E+00  6752   0.127E+01    0.365E+00
DAV:  11    -0.105665979797E+03   -0.25141E-01   -0.38348E-01  7016   0.926E+00    0.384E+00
DAV:  12    -0.105612076033E+03    0.53904E-01   -0.13599E-01  6864   0.649E+00    0.270E+00
DAV:  13    -0.105558458883E+03    0.53617E-01   -0.80070E-02  6824   0.351E+00    0.931E-01
DAV:  14    -0.105555732802E+03    0.27261E-02   -0.20955E-02  6336   0.728E-01    0.695E-01
DAV:  15    -0.105555112793E+03    0.62001E-03   -0.27004E-03  6072   0.305E-01    0.616E-01
DAV:  16    -0.105554595753E+03    0.51704E-03   -0.30726E-04  6784   0.103E-01    0.551E-01
DAV:  17    -0.105553443398E+03    0.11524E-02   -0.29233E-04  7080   0.243E-01    0.397E-01
DAV:  18    -0.105552357390E+03    0.10860E-02   -0.11271E-03  7072   0.430E-01    0.137E-01
DAV:  19    -0.105552344046E+03    0.13344E-04   -0.34874E-04  5224   0.753E-02    0.106E-01
DAV:  20    -0.105552349343E+03   -0.52973E-05   -0.13402E-05  5784   0.168E-02    0.109E-01
DAV:  21    -0.105552346008E+03    0.33352E-05   -0.11990E-06  2912   0.575E-03    0.107E-01
DAV:  22    -0.105552271702E+03    0.74306E-04   -0.90696E-05  7240   0.105E-01    0.367E-02
DAV:  23    -0.105552267725E+03    0.39769E-05   -0.30016E-05  6640   0.291E-02    0.229E-02
DAV:  24    -0.105552267388E+03    0.33728E-06   -0.11269E-06  2912   0.638E-03
   1 F= -.10555227E+03 E0= -.10555163E+03  d E =-.127326E-02
 writing wavefunctions
