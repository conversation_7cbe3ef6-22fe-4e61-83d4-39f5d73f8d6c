 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Ga B  Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.133771655725E+04    0.13377E+04   -0.49752E+04  6472   0.160E+03
DAV:   2     0.171663006090E+03   -0.11661E+04   -0.11109E+04  7368   0.451E+02
DAV:   3    -0.610983120781E+02   -0.23276E+03   -0.21962E+03  7608   0.165E+02
DAV:   4    -0.830853027048E+02   -0.21987E+02   -0.21453E+02  8424   0.508E+01
DAV:   5    -0.838555279201E+02   -0.77023E+00   -0.76274E+00  7864   0.930E+00    0.172E+01
DAV:   6    -0.802602700864E+02    0.35953E+01   -0.79357E+00  8936   0.172E+01    0.851E+00
DAV:   7    -0.802547734332E+02    0.54967E-02   -0.39577E+00  7864   0.656E+00    0.357E+00
DAV:   8    -0.802151270091E+02    0.39646E-01   -0.40796E-01  8304   0.390E+00    0.137E+00
DAV:   9    -0.802208643215E+02   -0.57373E-02   -0.74468E-02  7144   0.199E+00    0.282E-01
DAV:  10    -0.802209375486E+02   -0.73227E-04   -0.10776E-02  8512   0.134E+00    0.138E-01
DAV:  11    -0.802216251767E+02   -0.68763E-03   -0.35305E-03  7512   0.444E-01    0.522E-02
DAV:  12    -0.802217924094E+02   -0.16723E-03   -0.99964E-04  8160   0.266E-01    0.342E-02
DAV:  13    -0.802218377825E+02   -0.45373E-04   -0.21382E-04  7856   0.207E-01    0.179E-02
DAV:  14    -0.802218771105E+02   -0.39328E-04   -0.14929E-04  8120   0.682E-02    0.520E-03
DAV:  15    -0.802218793163E+02   -0.22058E-05   -0.11370E-05  8032   0.142E-02    0.388E-03
DAV:  16    -0.802218797304E+02   -0.41404E-06   -0.82247E-07  4520   0.120E-02
   1 F= -.80221880E+02 E0= -.80221880E+02  d E =-.341404E-15
 writing wavefunctions
