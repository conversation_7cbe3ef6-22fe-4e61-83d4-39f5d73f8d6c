 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Bi Al Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.119673354883E+04    0.11967E+04   -0.58597E+04  3576   0.137E+03
DAV:   2     0.783720093697E+02   -0.11184E+04   -0.10759E+04  3920   0.341E+02
DAV:   3    -0.755351707014E+02   -0.15391E+03   -0.14954E+03  4408   0.146E+02
DAV:   4    -0.832799380018E+02   -0.77448E+01   -0.76546E+01  4088   0.352E+01
DAV:   5    -0.836550037136E+02   -0.37507E+00   -0.37424E+00  4976   0.756E+00    0.128E+01
DAV:   6    -0.810437567482E+02    0.26112E+01   -0.38746E+00  4472   0.662E+00    0.758E+00
DAV:   7    -0.809418038407E+02    0.10195E+00   -0.24324E+00  4584   0.408E+00    0.282E+00
DAV:   8    -0.809307070538E+02    0.11097E-01   -0.40875E-01  4144   0.255E+00    0.842E-01
DAV:   9    -0.809288297425E+02    0.18773E-02   -0.54159E-02  3872   0.928E-01    0.335E-01
DAV:  10    -0.809295921163E+02   -0.76237E-03   -0.10123E-02  4504   0.299E-01    0.169E-01
DAV:  11    -0.809299961052E+02   -0.40399E-03   -0.12700E-03  4168   0.134E-01    0.619E-02
DAV:  12    -0.809300579144E+02   -0.61809E-04   -0.30280E-04  4592   0.607E-02    0.341E-02
DAV:  13    -0.809301344679E+02   -0.76554E-04   -0.14967E-04  3840   0.449E-02    0.891E-03
DAV:  14    -0.809301380555E+02   -0.35876E-05   -0.11334E-05  3848   0.122E-02    0.795E-03
DAV:  15    -0.809301399548E+02   -0.18993E-05   -0.60323E-06  3520   0.650E-03    0.207E-03
DAV:  16    -0.809301399660E+02   -0.11209E-07   -0.34334E-07  2240   0.275E-03
   1 F= -.80930140E+02 E0= -.80930140E+02  d E =-.705345E-22
 writing wavefunctions
