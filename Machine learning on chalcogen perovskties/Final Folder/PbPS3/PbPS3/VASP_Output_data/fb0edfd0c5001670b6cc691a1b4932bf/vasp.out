 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Sc P  Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.165353326428E+04    0.16535E+04   -0.51151E+04  4808   0.125E+03
DAV:   2     0.177237426358E+03   -0.14763E+04   -0.14369E+04  4600   0.419E+02
DAV:   3    -0.853610651722E+02   -0.26260E+03   -0.24782E+03  7384   0.162E+02
DAV:   4    -0.108307920284E+03   -0.22947E+02   -0.22530E+02  7424   0.596E+01
DAV:   5    -0.109018204610E+03   -0.71028E+00   -0.70164E+00  7568   0.129E+01    0.219E+01
DAV:   6    -0.101433540826E+03    0.75847E+01   -0.27344E+01  7400   0.240E+01    0.109E+01
DAV:   7    -0.101211900945E+03    0.22164E+00   -0.27555E+00  7080   0.688E+00    0.555E+00
DAV:   8    -0.101177549133E+03    0.34352E-01   -0.74450E-01  7464   0.393E+00    0.124E+00
DAV:   9    -0.101165319715E+03    0.12229E-01   -0.17356E-01  7128   0.217E+00    0.653E-01
DAV:  10    -0.101165239253E+03    0.80462E-04   -0.37494E-02  7232   0.100E+00    0.295E-01
DAV:  11    -0.101165241837E+03   -0.25837E-05   -0.41281E-03  7264   0.352E-01    0.160E-01
DAV:  12    -0.101165508894E+03   -0.26706E-03   -0.30007E-03  7832   0.270E-01    0.698E-02
DAV:  13    -0.101165585696E+03   -0.76802E-04   -0.82301E-04  7472   0.156E-01    0.280E-02
DAV:  14    -0.101165620485E+03   -0.34789E-04   -0.10251E-04  6488   0.479E-02    0.147E-02
DAV:  15    -0.101165630098E+03   -0.96132E-05   -0.15436E-05  5320   0.142E-02    0.553E-03
DAV:  16    -0.101165632109E+03   -0.20113E-05   -0.38279E-06  5224   0.107E-02    0.266E-03
DAV:  17    -0.101165632291E+03   -0.18138E-06   -0.10400E-06  3664   0.599E-03
   1 F= -.10116563E+03 E0= -.10116563E+03  d E =-.202118E-17
 writing wavefunctions
