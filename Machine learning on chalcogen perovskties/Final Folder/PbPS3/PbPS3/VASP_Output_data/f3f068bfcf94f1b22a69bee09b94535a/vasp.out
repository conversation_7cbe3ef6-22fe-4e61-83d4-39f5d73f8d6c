 running on  384 total cores
 distrk:  each k-point on   48 cores,    8 groups
 distr:  one band on    6 cores,    8 groups
 using from now: INCAR     
 vasp.5.4.4.18Apr17-6-g9f103f2a35 (build Aug 10 2024 18:08:47) complex          
  
 POSCAR found type information on POSCAR  Cu Hf Se
 POSCAR found :  3 types and      20 ions
 scaLAPACK will be used

 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      For optimal performance we recommend to set                            |
|        NCORE= 4 - approx SQRT( number of cores)                             |
|      NCORE specifies how many cores store one orbital (NPAR=cpu/NCORE).     |
|      This setting can  greatly improve the performance of VASP for DFT.     |
|      The default,   NCORE=1            might be grossly inefficient         |
|      on modern multi-core architectures or massively parallel machines.     |
|      Do your own testing !!!!                                               |
|      Unfortunately you need to use the default for GW and RPA calculations. |
|      (for HF NCORE is supported but not extensively tested yet)             |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|           W    W    AA    RRRRR   N    N  II  N    N   GGGG   !!!           |
|           W    W   A  A   R    R  NN   N  II  NN   N  G    G  !!!           |
|           W    W  A    A  R    R  N N  N  II  N N  N  G       !!!           |
|           W WW W  AAAAAA  RRRRR   N  N N  II  N  N N  G  GGG   !            |
|           WW  WW  A    A  R   R   N   NN  II  N   NN  G    G                |
|           W    W  A    A  R    R  N    N  II  N    N   GGGG   !!!           |
|                                                                             |
|      You have enabled k-point parallelism (KPAR>1).                         |
|      This developmental code was originally  written by Paul Kent at ORNL,  |
|      and carefully double checked in Vienna.                                |
|      GW as well as linear response parallelism added by Martijn Marsman     |
|      and Georg Kresse.                                                      |
|      Carefully verify results versus KPAR=1.                                |
|      Report problems to Paul Kent and Vienna.                               |
|                                                                             |
 ----------------------------------------------------------------------------- 


 ----------------------------------------------------------------------------- 
|                                                                             |
|  ADVICE TO THIS USER RUNNING 'VASP/VAMP'   (HEAR YOUR MASTER'S VOICE ...):  |
|                                                                             |
|      You have a (more or less) 'large supercell' and for larger cells       |
|      it might be more efficient to use real space projection opertators     |
|      So try LREAL= Auto  in the INCAR   file.                               |
|      Mind:          For very  accurate calculation you might also keep the  |
|      reciprocal projection scheme          (i.e. LREAL=.FALSE.)             |
|                                                                             |
 ----------------------------------------------------------------------------- 

 LDA part: xc-table for Pade appr. of Perdew
 POSCAR, INCAR and KPOINTS ok, starting setup
 FFT: planning ...
 WAVECAR not read
 entering main loop
       N       E                     dE             d eps       ncg     rms          rms(c)
DAV:   1     0.149280872276E+04    0.14928E+04   -0.60287E+04  4992   0.150E+03
DAV:   2     0.134526404045E+03   -0.13583E+04   -0.12968E+04  5224   0.419E+02
DAV:   3    -0.988664659210E+02   -0.23339E+03   -0.22018E+03  6560   0.170E+02
DAV:   4    -0.119372733107E+03   -0.20506E+02   -0.20143E+02  7880   0.604E+01
DAV:   5    -0.119857429179E+03   -0.48470E+00   -0.48129E+00  7912   0.106E+01    0.897E+01
DAV:   6    -0.119273531901E+03    0.58390E+00   -0.14331E+02  7320   0.144E+02    0.578E+01
DAV:   7    -0.124324233291E+03   -0.50507E+01   -0.14542E+02  7136   0.171E+02    0.623E+01
DAV:   8    -0.121271742406E+03    0.30525E+01   -0.19484E+02  7192   0.178E+02    0.198E+01
DAV:   9    -0.116426802811E+03    0.48449E+01   -0.49108E+01  7112   0.916E+01    0.133E+01
DAV:  10    -0.113148119006E+03    0.32787E+01   -0.81768E+00  7784   0.368E+01    0.924E+00
DAV:  11    -0.112118127900E+03    0.10300E+01   -0.24544E+00  8064   0.160E+01    0.706E+00
DAV:  12    -0.111224164790E+03    0.89396E+00   -0.94940E-01  7992   0.197E+01    0.485E+00
DAV:  13    -0.110580760180E+03    0.64340E+00   -0.21619E+00  7968   0.339E+01    0.863E-01
DAV:  14    -0.110707177018E+03   -0.12642E+00   -0.12307E+00  7656   0.700E+00    0.225E+00
DAV:  15    -0.110708791888E+03   -0.16149E-02   -0.32063E-02  7880   0.157E+00    0.200E+00
DAV:  16    -0.110706696518E+03    0.20954E-02   -0.39387E-03  7232   0.118E+00    0.172E+00
DAV:  17    -0.110700483684E+03    0.62128E-02   -0.18619E-02  7560   0.286E+00    0.570E-01
DAV:  18    -0.110700566723E+03   -0.83039E-04   -0.48084E-03  7936   0.685E-01    0.471E-01
DAV:  19    -0.110700539148E+03    0.27575E-04   -0.75849E-04  7888   0.202E-01    0.353E-01
DAV:  20    -0.110700499497E+03    0.39652E-04   -0.10860E-04  8480   0.141E-01    0.236E-01
DAV:  21    -0.110700536461E+03   -0.36964E-04   -0.32285E-04  8528   0.404E-01    0.157E-01
DAV:  22    -0.110700475892E+03    0.60570E-04   -0.10083E-04  7368   0.170E-01    0.954E-02
DAV:  23    -0.110700452507E+03    0.23385E-04   -0.38653E-05  7344   0.941E-02    0.624E-02
DAV:  24    -0.110700440221E+03    0.12286E-04   -0.43083E-05  7504   0.126E-01    0.105E-02
DAV:  25    -0.110700441263E+03   -0.10417E-05   -0.18302E-05  6072   0.243E-02    0.634E-03
DAV:  26    -0.110700441128E+03    0.13472E-06   -0.16699E-06  3352   0.182E-02
   1 F= -.11070044E+03 E0= -.11070044E+03  d E =-.104724E-18
 writing wavefunctions
