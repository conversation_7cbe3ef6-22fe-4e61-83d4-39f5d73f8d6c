#!/bin/bash
#SBATCH -N 8
#SBATCH --ntasks-per-node=48
#SBATCH --time=6-00:00:00
#SBATCH --job-name=script
#SBATCH --error=job.%J.err_node_40
#SBATCH --output=job.%J.out_node_40
#SBATCH --partition=gpu
#SBATCH --exclusive

##ml compiler/oneapi2024/mpi/2021.11
export OMP_NUM_THREADS=1
ml spack/0.22
source /home/<USER>/SPACK/spack/share/spack/setup-env.sh
spack load intel-oneapi-compilers@2022.0.2
spack load intel-oneapi-mkl/f6euufh
spack load intel-oneapi-mpi/lf7pu7b
ulimit -s unlimited
python Vinayak.py
