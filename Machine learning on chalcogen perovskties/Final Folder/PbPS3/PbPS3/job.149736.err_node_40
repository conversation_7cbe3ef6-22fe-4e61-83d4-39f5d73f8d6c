
For better command line support, copy and paste the folowing,
 which will source the spack setup script:

. /home/<USER>/SPACK/spack/share/spack/setup-env.sh 


If using spack to install to system area, make sure to set umask 0002 
so that group write access is available to the software Linux group. 

[1;93m==>[0m Warning: The packages:all:compiler preference has been deprecated in Spack v1.0, and is currently ignored. It will be removed from config in Spack v1.2.
[1;93m==>[0m Warning: The packages:all:compiler preference has been deprecated in Spack v1.0, and is currently ignored. It will be removed from config in Spack v1.2.
[1;93m==>[0m Warning: The packages:all:compiler preference has been deprecated in Spack v1.0, and is currently ignored. It will be removed from config in Spack v1.2.
