==========================================
SLURM_CLUSTER_NAME = paramprabha
SLURM_JOB_ACCOUNT = iuac
SLURM_JOB_ID = 149736
SLURM_JOB_NAME = script
SLURM_JOB_NODELIST = rpgpu[015-019,028-030]
SLURM_JOB_USER = sumitkiuac
SLURM_JOB_UID = 52193
SLURM_JOB_PARTITION = gpu
SLURM_TASK_PID = 293738
SLURM_SUBMIT_DIR = /scratch/sumitkiuac/New_zipped/final_run/prototype_dbs/PbPS3
SLURM_CPUS_ON_NODE = 48
SLURM_NTASKS = 384
SLURM_TASK_PID = 293738
==========================================
Gap: 0.429 eV
Transition (v -> c):
  (s=0, k=14, n=51, [0.42, 0.12, 0.33]) -> (s=0, k=15, n=52, [-0.42, 0.12, 0.33])
<class 'tuple'>
Gap: 0.429 eV
Transition (v -> c):
  (s=0, k=14, n=51, [0.42, 0.12, 0.33]) -> (s=0, k=15, n=52, [-0.42, 0.12, 0.33])
Gap: 0.222 eV
Transition (v -> c):
  (s=0, k=3, n=75, [-0.00, 0.20, -0.00]) -> (s=0, k=2, n=76, [0.40, -0.00, -0.00])
<class 'tuple'>
Gap: 0.222 eV
Transition (v -> c):
  (s=0, k=3, n=75, [-0.00, 0.20, -0.00]) -> (s=0, k=2, n=76, [0.40, -0.00, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.257 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.257 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
Gap: 0.558 eV
Transition (v -> c):
  (s=0, k=13, n=71, [-0.00, 0.00, 0.33]) -> (s=0, k=5, n=72, [0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.558 eV
Transition (v -> c):
  (s=0, k=13, n=71, [-0.00, 0.00, 0.33]) -> (s=0, k=5, n=72, [0.40, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.124 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, 0.00, 0.00]) -> (s=0, k=12, n=72, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.124 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, 0.00, 0.00]) -> (s=0, k=12, n=72, [-0.20, 0.40, -0.00])
Gap: 0.930 eV
Transition (v -> c):
  (s=0, k=13, n=63, [-0.40, 0.12, 0.33]) -> (s=0, k=13, n=64, [-0.40, 0.12, 0.33])
<class 'tuple'>
Gap: 0.930 eV
Transition (v -> c):
  (s=0, k=13, n=63, [-0.40, 0.12, 0.33]) -> (s=0, k=13, n=64, [-0.40, 0.12, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.466 eV
Transition (v -> c):
  (s=0, k=3, n=67, [-0.42, 0.12, -0.00]) -> (s=0, k=7, n=68, [0.25, 0.38, 0.00])
<class 'tuple'>
Gap: 0.466 eV
Transition (v -> c):
  (s=0, k=3, n=67, [-0.42, 0.12, -0.00]) -> (s=0, k=7, n=68, [0.25, 0.38, 0.00])
Gap: 1.317 eV
Transition (v -> c):
  (s=0, k=18, n=67, [0.40, 0.20, 0.33]) -> (s=0, k=3, n=68, [-0.00, 0.20, 0.00])
<class 'tuple'>
Gap: 1.317 eV
Transition (v -> c):
  (s=0, k=18, n=67, [0.40, 0.20, 0.33]) -> (s=0, k=3, n=68, [-0.00, 0.20, 0.00])
Gap: 1.231 eV
Transition (v -> c):
  (s=0, k=8, n=59, [0.42, 0.38, -0.00]) -> (s=0, k=6, n=60, [0.08, 0.38, -0.00])
<class 'tuple'>
Gap: 1.231 eV
Transition (v -> c):
  (s=0, k=8, n=59, [0.42, 0.38, -0.00]) -> (s=0, k=6, n=60, [0.08, 0.38, -0.00])
Gap: 0.555 eV
Transition (v -> c):
  (s=0, k=1, n=63, [0.38, -0.00, -0.00]) -> (s=0, k=0, n=64, [0.12, 0.00, 0.00])
<class 'tuple'>
Gap: 0.555 eV
Transition (v -> c):
  (s=0, k=1, n=63, [0.38, -0.00, -0.00]) -> (s=0, k=0, n=64, [0.12, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.343 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.42, -0.00, -0.00]) -> (s=0, k=2, n=72, [0.42, -0.00, -0.00])
<class 'tuple'>
Gap: 0.343 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.42, -0.00, -0.00]) -> (s=0, k=2, n=72, [0.42, -0.00, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.753 eV
Transition (v -> c):
  (s=0, k=15, n=67, [0.40, -0.00, 0.33]) -> (s=0, k=15, n=68, [0.40, -0.00, 0.33])
<class 'tuple'>
Gap: 0.753 eV
Transition (v -> c):
  (s=0, k=15, n=67, [0.40, -0.00, 0.33]) -> (s=0, k=15, n=68, [0.40, -0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.429 eV
Transition (v -> c):
  (s=0, k=17, n=71, [0.42, -0.00, 0.33]) -> (s=0, k=0, n=72, [0.08, 0.00, 0.00])
<class 'tuple'>
Gap: 0.429 eV
Transition (v -> c):
  (s=0, k=17, n=71, [0.42, -0.00, 0.33]) -> (s=0, k=0, n=72, [0.08, 0.00, 0.00])
Gap: 0.157 eV
Transition (v -> c):
  (s=0, k=23, n=67, [-0.08, 0.20, 0.33]) -> (s=0, k=27, n=68, [-0.42, 0.40, 0.33])
<class 'tuple'>
Gap: 0.157 eV
Transition (v -> c):
  (s=0, k=23, n=67, [-0.08, 0.20, 0.33]) -> (s=0, k=27, n=68, [-0.42, 0.40, 0.33])
Gap: 0.026 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.026 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
Gap: 0.868 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=11, n=72, [-0.40, 0.40, -0.00])
<class 'tuple'>
Gap: 0.868 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=11, n=72, [-0.40, 0.40, -0.00])
Gap: 1.080 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.20, 0.38, 0.00]) -> (s=0, k=7, n=64, [0.40, 0.38, -0.00])
<class 'tuple'>
Gap: 1.080 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.20, 0.38, 0.00]) -> (s=0, k=7, n=64, [0.40, 0.38, -0.00])
Gap: 0.665 eV
Transition (v -> c):
  (s=0, k=3, n=67, [0.00, 0.20, -0.00]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.665 eV
Transition (v -> c):
  (s=0, k=3, n=67, [0.00, 0.20, -0.00]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
Gap: 1.005 eV
Transition (v -> c):
  (s=0, k=19, n=67, [-0.20, 0.38, 0.33]) -> (s=0, k=10, n=68, [0.00, 0.12, 0.33])
<class 'tuple'>
Gap: 1.005 eV
Transition (v -> c):
  (s=0, k=19, n=67, [-0.20, 0.38, 0.33]) -> (s=0, k=10, n=68, [0.00, 0.12, 0.33])
Gap: 0.765 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.00, 0.38, 0.00]) -> (s=0, k=5, n=68, [-0.00, 0.38, 0.00])
<class 'tuple'>
Gap: 0.765 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.00, 0.38, 0.00]) -> (s=0, k=5, n=68, [-0.00, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.187 eV
Transition (v -> c):
  (s=0, k=0, n=67, [-0.00, 0.12, -0.00]) -> (s=0, k=6, n=68, [0.20, 0.38, -0.00])
<class 'tuple'>
Gap: 1.187 eV
Transition (v -> c):
  (s=0, k=0, n=67, [-0.00, 0.12, -0.00]) -> (s=0, k=6, n=68, [0.20, 0.38, -0.00])
Gap: 0.286 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.00, 0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.286 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.00, 0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, -0.00])
Gap: 0.384 eV
Transition (v -> c):
  (s=0, k=13, n=55, [0.00, 0.00, 0.33]) -> (s=0, k=2, n=56, [0.40, 0.00, -0.00])
<class 'tuple'>
Gap: 0.384 eV
Transition (v -> c):
  (s=0, k=13, n=55, [0.00, 0.00, 0.33]) -> (s=0, k=2, n=56, [0.40, 0.00, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.820 eV
Transition (v -> c):
  (s=0, k=16, n=51, [-0.00, 0.20, 0.33]) -> (s=0, k=2, n=52, [0.40, 0.00, 0.00])
<class 'tuple'>
Gap: 0.820 eV
Transition (v -> c):
  (s=0, k=16, n=51, [-0.00, 0.20, 0.33]) -> (s=0, k=2, n=52, [0.40, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.351 eV
Transition (v -> c):
  (s=0, k=13, n=55, [0.00, 0.00, 0.33]) -> (s=0, k=2, n=56, [0.40, -0.00, -0.00])
<class 'tuple'>
Gap: 0.351 eV
Transition (v -> c):
  (s=0, k=13, n=55, [0.00, 0.00, 0.33]) -> (s=0, k=2, n=56, [0.40, -0.00, -0.00])
Gap: 0.266 eV
Transition (v -> c):
  (s=0, k=2, n=51, [0.42, 0.00, 0.00]) -> (s=0, k=2, n=52, [0.42, 0.00, 0.00])
<class 'tuple'>
Gap: 0.266 eV
Transition (v -> c):
  (s=0, k=2, n=51, [0.42, 0.00, 0.00]) -> (s=0, k=2, n=52, [0.42, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.615 eV
Transition (v -> c):
  (s=0, k=0, n=51, [0.00, 0.00, 0.12]) -> (s=0, k=12, n=52, [0.20, 0.33, 0.38])
<class 'tuple'>
Gap: 1.615 eV
Transition (v -> c):
  (s=0, k=0, n=51, [0.00, 0.00, 0.12]) -> (s=0, k=12, n=52, [0.20, 0.33, 0.38])
Gap: 0.235 eV
Transition (v -> c):
  (s=0, k=10, n=55, [0.40, 0.40, 0.00]) -> (s=0, k=2, n=56, [0.40, 0.00, 0.00])
<class 'tuple'>
Gap: 0.235 eV
Transition (v -> c):
  (s=0, k=10, n=55, [0.40, 0.40, 0.00]) -> (s=0, k=2, n=56, [0.40, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.781 eV
Transition (v -> c):
  (s=0, k=1, n=55, [0.20, 0.00, -0.00]) -> (s=0, k=7, n=56, [-0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 0.781 eV
Transition (v -> c):
  (s=0, k=1, n=55, [0.20, 0.00, -0.00]) -> (s=0, k=7, n=56, [-0.20, 0.20, 0.00])
Gap: 0.083 eV
Transition (v -> c):
  (s=0, k=22, n=65, [-0.25, 0.20, 0.33]) -> (s=0, k=1, n=66, [0.25, -0.00, 0.00])
<class 'tuple'>
Gap: 0.083 eV
Transition (v -> c):
  (s=0, k=22, n=65, [-0.25, 0.20, 0.33]) -> (s=0, k=1, n=66, [0.25, -0.00, 0.00])
Gap: 0.311 eV
Transition (v -> c):
  (s=0, k=17, n=67, [0.42, 0.00, 0.33]) -> (s=0, k=18, n=68, [0.08, 0.20, 0.33])
<class 'tuple'>
Gap: 0.311 eV
Transition (v -> c):
  (s=0, k=17, n=67, [0.42, 0.00, 0.33]) -> (s=0, k=18, n=68, [0.08, 0.20, 0.33])
Gap: 1.513 eV
Transition (v -> c):
  (s=0, k=9, n=51, [-0.20, 0.25, 0.25]) -> (s=0, k=1, n=52, [0.20, 0.08, 0.25])
<class 'tuple'>
Gap: 1.513 eV
Transition (v -> c):
  (s=0, k=9, n=51, [-0.20, 0.25, 0.25]) -> (s=0, k=1, n=52, [0.20, 0.08, 0.25])
No gap
<class 'tuple'>
No gap
Gap: 0.165 eV
Transition (v -> c):
  (s=0, k=4, n=71, [0.25, 0.20, 0.00]) -> (s=0, k=15, n=72, [0.08, 0.00, 0.33])
<class 'tuple'>
Gap: 0.165 eV
Transition (v -> c):
  (s=0, k=4, n=71, [0.25, 0.20, 0.00]) -> (s=0, k=15, n=72, [0.08, 0.00, 0.33])
Gap: 1.339 eV
Transition (v -> c):
  (s=0, k=2, n=63, [0.40, 0.12, 0.00]) -> (s=0, k=2, n=64, [0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 1.339 eV
Transition (v -> c):
  (s=0, k=2, n=63, [0.40, 0.12, 0.00]) -> (s=0, k=2, n=64, [0.40, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.863 eV
Transition (v -> c):
  (s=0, k=2, n=75, [0.40, 0.12, 0.12]) -> (s=0, k=17, n=76, [0.40, 0.38, 0.38])
<class 'tuple'>
Gap: 0.863 eV
Transition (v -> c):
  (s=0, k=2, n=75, [0.40, 0.12, 0.12]) -> (s=0, k=17, n=76, [0.40, 0.38, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.417 eV
Transition (v -> c):
  (s=0, k=0, n=69, [0.00, 0.00, 0.00]) -> (s=0, k=24, n=70, [-0.40, 0.40, 0.33])
<class 'tuple'>
Gap: 0.417 eV
Transition (v -> c):
  (s=0, k=0, n=69, [0.00, 0.00, 0.00]) -> (s=0, k=24, n=70, [-0.40, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 1.118 eV
Transition (v -> c):
  (s=0, k=2, n=55, [0.42, 0.12, 0.12]) -> (s=0, k=20, n=56, [0.42, 0.38, 0.38])
<class 'tuple'>
Gap: 1.118 eV
Transition (v -> c):
  (s=0, k=2, n=55, [0.42, 0.12, 0.12]) -> (s=0, k=20, n=56, [0.42, 0.38, 0.38])
Gap: 1.241 eV
Transition (v -> c):
  (s=0, k=11, n=59, [0.20, 0.12, 0.38]) -> (s=0, k=13, n=60, [-0.40, 0.12, 0.38])
<class 'tuple'>
Gap: 1.241 eV
Transition (v -> c):
  (s=0, k=11, n=59, [0.20, 0.12, 0.38]) -> (s=0, k=13, n=60, [-0.40, 0.12, 0.38])
Gap: 0.649 eV
Transition (v -> c):
  (s=0, k=23, n=47, [0.40, 0.40, 0.33]) -> (s=0, k=3, n=48, [-0.00, 0.20, 0.00])
<class 'tuple'>
Gap: 0.649 eV
Transition (v -> c):
  (s=0, k=23, n=47, [0.40, 0.40, 0.33]) -> (s=0, k=3, n=48, [-0.00, 0.20, 0.00])
Gap: 1.382 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=11, n=84, [-0.40, 0.40, 0.00])
<class 'tuple'>
Gap: 1.382 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=11, n=84, [-0.40, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.745 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.00, 0.38, -0.00]) -> (s=0, k=15, n=68, [-0.00, 0.38, 0.33])
<class 'tuple'>
Gap: 0.745 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.00, 0.38, -0.00]) -> (s=0, k=15, n=68, [-0.00, 0.38, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.506 eV
Transition (v -> c):
  (s=0, k=11, n=85, [-0.40, 0.40, 0.00]) -> (s=0, k=13, n=86, [-0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.506 eV
Transition (v -> c):
  (s=0, k=11, n=85, [-0.40, 0.40, 0.00]) -> (s=0, k=13, n=86, [-0.00, 0.00, 0.33])
Gap: 0.883 eV
Transition (v -> c):
  (s=0, k=4, n=75, [-0.20, 0.08, 0.00]) -> (s=0, k=0, n=76, [0.00, 0.08, 0.00])
<class 'tuple'>
Gap: 0.883 eV
Transition (v -> c):
  (s=0, k=4, n=75, [-0.20, 0.08, 0.00]) -> (s=0, k=0, n=76, [0.00, 0.08, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.405 eV
Transition (v -> c):
  (s=0, k=2, n=63, [0.40, -0.00, 0.00]) -> (s=0, k=12, n=64, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.405 eV
Transition (v -> c):
  (s=0, k=2, n=63, [0.40, -0.00, 0.00]) -> (s=0, k=12, n=64, [-0.20, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.633 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, 0.00, 0.33]) -> (s=0, k=9, n=84, [0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.633 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, 0.00, 0.33]) -> (s=0, k=9, n=84, [0.20, 0.40, -0.00])
Gap: 0.169 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.12, -0.00, -0.00]) -> (s=0, k=0, n=80, [0.12, -0.00, -0.00])
<class 'tuple'>
Gap: 0.169 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.12, -0.00, -0.00]) -> (s=0, k=0, n=80, [0.12, -0.00, -0.00])
Gap: 1.760 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.12, 0.12, 0.00]) -> (s=0, k=7, n=80, [-0.12, 0.38, -0.00])
<class 'tuple'>
Gap: 1.760 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.12, 0.12, 0.00]) -> (s=0, k=7, n=80, [-0.12, 0.38, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.013 eV
Transition (v -> c):
  (s=0, k=12, n=77, [-0.42, 0.40, 0.00]) -> (s=0, k=14, n=78, [-0.08, 0.40, 0.00])
<class 'tuple'>
Gap: 0.013 eV
Transition (v -> c):
  (s=0, k=12, n=77, [-0.42, 0.40, 0.00]) -> (s=0, k=14, n=78, [-0.08, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.111 eV
Transition (v -> c):
  (s=0, k=7, n=87, [0.40, 0.38, 0.12]) -> (s=0, k=10, n=88, [0.00, 0.12, 0.38])
<class 'tuple'>
Gap: 1.111 eV
Transition (v -> c):
  (s=0, k=7, n=87, [0.40, 0.38, 0.12]) -> (s=0, k=10, n=88, [0.00, 0.12, 0.38])
Gap: 0.455 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.12, 0.00]) -> (s=0, k=8, n=76, [-0.40, 0.38, -0.00])
<class 'tuple'>
Gap: 0.455 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.12, 0.00]) -> (s=0, k=8, n=76, [-0.40, 0.38, -0.00])
Gap: 0.671 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=6, n=84, [-0.40, 0.20, -0.00])
<class 'tuple'>
Gap: 0.671 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=6, n=84, [-0.40, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.008 eV
Transition (v -> c):
  (s=0, k=7, n=83, [-0.12, 0.38, 0.00]) -> (s=0, k=6, n=84, [-0.38, 0.38, -0.00])
<class 'tuple'>
Gap: 1.008 eV
Transition (v -> c):
  (s=0, k=7, n=83, [-0.12, 0.38, 0.00]) -> (s=0, k=6, n=84, [-0.38, 0.38, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.385 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.38, 0.12, 0.00]) -> (s=0, k=1, n=88, [0.38, 0.12, 0.00])
<class 'tuple'>
Gap: 1.385 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.38, 0.12, 0.00]) -> (s=0, k=1, n=88, [0.38, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.167 eV
Transition (v -> c):
  (s=0, k=9, n=83, [0.20, 0.40, 0.00]) -> (s=0, k=5, n=84, [0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.167 eV
Transition (v -> c):
  (s=0, k=9, n=83, [0.20, 0.40, 0.00]) -> (s=0, k=5, n=84, [0.40, 0.20, 0.00])
Gap: 0.068 eV
Transition (v -> c):
  (s=0, k=27, n=73, [-0.42, 0.40, 0.38]) -> (s=0, k=28, n=74, [-0.25, 0.40, 0.38])
<class 'tuple'>
Gap: 0.068 eV
Transition (v -> c):
  (s=0, k=27, n=73, [-0.42, 0.40, 0.38]) -> (s=0, k=28, n=74, [-0.25, 0.40, 0.38])
Gap: 1.338 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.12, 0.00]) -> (s=0, k=0, n=68, [-0.00, 0.12, 0.00])
<class 'tuple'>
Gap: 1.338 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.12, 0.00]) -> (s=0, k=0, n=68, [-0.00, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.022 eV
Transition (v -> c):
  (s=0, k=10, n=85, [-0.00, 0.12, 0.33]) -> (s=0, k=15, n=86, [0.00, 0.38, 0.33])
<class 'tuple'>
Gap: 0.022 eV
Transition (v -> c):
  (s=0, k=10, n=85, [-0.00, 0.12, 0.33]) -> (s=0, k=15, n=86, [0.00, 0.38, 0.33])
Gap: 0.764 eV
Transition (v -> c):
  (s=0, k=15, n=71, [0.08, 0.00, 0.33]) -> (s=0, k=22, n=72, [-0.25, 0.20, 0.33])
<class 'tuple'>
Gap: 0.764 eV
Transition (v -> c):
  (s=0, k=15, n=71, [0.08, 0.00, 0.33]) -> (s=0, k=22, n=72, [-0.25, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.642 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.38, 0.12, 0.00]) -> (s=0, k=0, n=84, [0.12, 0.12, 0.00])
<class 'tuple'>
Gap: 0.642 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.38, 0.12, 0.00]) -> (s=0, k=0, n=84, [0.12, 0.12, 0.00])
Gap: 0.395 eV
Transition (v -> c):
  (s=0, k=10, n=83, [0.00, 0.12, 0.38]) -> (s=0, k=14, n=84, [-0.20, 0.12, 0.38])
<class 'tuple'>
Gap: 0.395 eV
Transition (v -> c):
  (s=0, k=10, n=83, [0.00, 0.12, 0.38]) -> (s=0, k=14, n=84, [-0.20, 0.12, 0.38])
Gap: 0.030 eV
Transition (v -> c):
  (s=0, k=9, n=79, [0.20, 0.40, 0.00]) -> (s=0, k=9, n=80, [0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.030 eV
Transition (v -> c):
  (s=0, k=9, n=79, [0.20, 0.40, 0.00]) -> (s=0, k=9, n=80, [0.20, 0.40, 0.00])
Gap: 1.748 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=17, n=88, [0.20, 0.20, 0.33])
<class 'tuple'>
Gap: 1.748 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=17, n=88, [0.20, 0.20, 0.33])
Gap: 0.255 eV
Transition (v -> c):
  (s=0, k=8, n=67, [-0.40, 0.38, 0.00]) -> (s=0, k=18, n=68, [-0.40, 0.38, 0.33])
<class 'tuple'>
Gap: 0.255 eV
Transition (v -> c):
  (s=0, k=8, n=67, [-0.40, 0.38, 0.00]) -> (s=0, k=18, n=68, [-0.40, 0.38, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.931 eV
Transition (v -> c):
  (s=0, k=8, n=83, [0.12, 0.12, 0.33]) -> (s=0, k=3, n=84, [-0.12, 0.12, 0.00])
<class 'tuple'>
Gap: 0.931 eV
Transition (v -> c):
  (s=0, k=8, n=83, [0.12, 0.12, 0.33]) -> (s=0, k=3, n=84, [-0.12, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.920 eV
Transition (v -> c):
  (s=0, k=7, n=67, [0.40, 0.38, 0.12]) -> (s=0, k=18, n=68, [-0.40, 0.38, 0.38])
<class 'tuple'>
Gap: 0.920 eV
Transition (v -> c):
  (s=0, k=7, n=67, [0.40, 0.38, 0.12]) -> (s=0, k=18, n=68, [-0.40, 0.38, 0.38])
Gap: 0.134 eV
Transition (v -> c):
  (s=0, k=2, n=73, [0.42, -0.00, -0.00]) -> (s=0, k=17, n=74, [0.42, -0.00, 0.33])
<class 'tuple'>
Gap: 0.134 eV
Transition (v -> c):
  (s=0, k=2, n=73, [0.42, -0.00, -0.00]) -> (s=0, k=17, n=74, [0.42, -0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.596 eV
Transition (v -> c):
  (s=0, k=23, n=71, [-0.08, 0.20, 0.33]) -> (s=0, k=22, n=72, [-0.25, 0.20, 0.33])
<class 'tuple'>
Gap: 0.596 eV
Transition (v -> c):
  (s=0, k=23, n=71, [-0.08, 0.20, 0.33]) -> (s=0, k=22, n=72, [-0.25, 0.20, 0.33])
Gap: 0.685 eV
Transition (v -> c):
  (s=0, k=11, n=91, [-0.40, 0.40, 0.00]) -> (s=0, k=0, n=92, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.685 eV
Transition (v -> c):
  (s=0, k=11, n=91, [-0.40, 0.40, 0.00]) -> (s=0, k=0, n=92, [0.00, 0.00, 0.00])
Gap: 0.778 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.12, 0.38]) -> (s=0, k=14, n=84, [-0.20, 0.12, 0.38])
<class 'tuple'>
Gap: 0.778 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.12, 0.38]) -> (s=0, k=14, n=84, [-0.20, 0.12, 0.38])
Gap: 0.224 eV
Transition (v -> c):
  (s=0, k=3, n=75, [-0.00, 0.20, 0.00]) -> (s=0, k=11, n=76, [-0.40, 0.40, 0.00])
<class 'tuple'>
Gap: 0.224 eV
Transition (v -> c):
  (s=0, k=3, n=75, [-0.00, 0.20, 0.00]) -> (s=0, k=11, n=76, [-0.40, 0.40, 0.00])
Gap: 2.046 eV
Transition (v -> c):
  (s=0, k=1, n=63, [0.20, 0.00, -0.00]) -> (s=0, k=2, n=64, [0.40, 0.00, -0.00])
<class 'tuple'>
Gap: 2.046 eV
Transition (v -> c):
  (s=0, k=1, n=63, [0.20, 0.00, -0.00]) -> (s=0, k=2, n=64, [0.40, 0.00, -0.00])
Gap: 0.969 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, -0.00]) -> (s=0, k=19, n=64, [-0.12, 0.40, 0.33])
<class 'tuple'>
Gap: 0.969 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, -0.00]) -> (s=0, k=19, n=64, [-0.12, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.834 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.12, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.12, 0.00])
<class 'tuple'>
Gap: 0.834 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.12, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.12, 0.00])
Gap: 1.373 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.12, 0.00]) -> (s=0, k=6, n=76, [0.20, 0.38, -0.00])
<class 'tuple'>
Gap: 1.373 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.12, 0.00]) -> (s=0, k=6, n=76, [0.20, 0.38, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.465 eV
Transition (v -> c):
  (s=0, k=7, n=67, [-0.20, 0.20, -0.00]) -> (s=0, k=4, n=68, [0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 0.465 eV
Transition (v -> c):
  (s=0, k=7, n=67, [-0.20, 0.20, -0.00]) -> (s=0, k=4, n=68, [0.20, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.257 eV
Transition (v -> c):
  (s=0, k=6, n=83, [-0.38, 0.38, 0.00]) -> (s=0, k=3, n=84, [-0.12, 0.12, -0.00])
<class 'tuple'>
Gap: 0.257 eV
Transition (v -> c):
  (s=0, k=6, n=83, [-0.38, 0.38, 0.00]) -> (s=0, k=3, n=84, [-0.12, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.261 eV
Transition (v -> c):
  (s=0, k=0, n=79, [-0.00, 0.08, 0.00]) -> (s=0, k=0, n=80, [-0.00, 0.08, 0.00])
<class 'tuple'>
Gap: 1.261 eV
Transition (v -> c):
  (s=0, k=0, n=79, [-0.00, 0.08, 0.00]) -> (s=0, k=0, n=80, [-0.00, 0.08, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.149 eV
Transition (v -> c):
  (s=0, k=3, n=75, [0.08, 0.20, 0.00]) -> (s=0, k=9, n=76, [0.08, 0.40, 0.00])
<class 'tuple'>
Gap: 0.149 eV
Transition (v -> c):
  (s=0, k=3, n=75, [0.08, 0.20, 0.00]) -> (s=0, k=9, n=76, [0.08, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.238 eV
Transition (v -> c):
  (s=0, k=18, n=73, [-0.38, 0.40, 0.38]) -> (s=0, k=1, n=74, [0.38, -0.00, 0.12])
<class 'tuple'>
Gap: 0.238 eV
Transition (v -> c):
  (s=0, k=18, n=73, [-0.38, 0.40, 0.38]) -> (s=0, k=1, n=74, [0.38, -0.00, 0.12])
Gap: 0.114 eV
Transition (v -> c):
  (s=0, k=13, n=87, [0.00, 0.00, 0.33]) -> (s=0, k=7, n=88, [-0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 0.114 eV
Transition (v -> c):
  (s=0, k=13, n=87, [0.00, 0.00, 0.33]) -> (s=0, k=7, n=88, [-0.20, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.896 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=13, n=84, [-0.00, -0.00, 0.33])
<class 'tuple'>
Gap: 0.896 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=13, n=84, [-0.00, -0.00, 0.33])
Gap: 0.121 eV
Transition (v -> c):
  (s=0, k=5, n=75, [0.40, 0.20, 0.00]) -> (s=0, k=24, n=76, [-0.40, 0.40, 0.33])
<class 'tuple'>
Gap: 0.121 eV
Transition (v -> c):
  (s=0, k=5, n=75, [0.40, 0.20, 0.00]) -> (s=0, k=24, n=76, [-0.40, 0.40, 0.33])
Gap: 1.098 eV
Transition (v -> c):
  (s=0, k=5, n=79, [0.00, 0.38, 0.12]) -> (s=0, k=10, n=80, [0.00, 0.12, 0.38])
<class 'tuple'>
Gap: 1.098 eV
Transition (v -> c):
  (s=0, k=5, n=79, [0.00, 0.38, 0.12]) -> (s=0, k=10, n=80, [0.00, 0.12, 0.38])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.762 eV
Transition (v -> c):
  (s=0, k=13, n=79, [0.00, 0.00, 0.33]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.762 eV
Transition (v -> c):
  (s=0, k=13, n=79, [0.00, 0.00, 0.33]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.618 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=10, n=88, [0.40, 0.40, 0.00])
<class 'tuple'>
Gap: 0.618 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=10, n=88, [0.40, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.242 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.12, 0.33]) -> (s=0, k=6, n=84, [0.20, 0.38, -0.00])
<class 'tuple'>
Gap: 1.242 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.12, 0.33]) -> (s=0, k=6, n=84, [0.20, 0.38, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.386 eV
Transition (v -> c):
  (s=0, k=0, n=83, [-0.00, 0.08, 0.00]) -> (s=0, k=11, n=84, [0.20, 0.42, 0.00])
<class 'tuple'>
Gap: 0.386 eV
Transition (v -> c):
  (s=0, k=0, n=83, [-0.00, 0.08, 0.00]) -> (s=0, k=11, n=84, [0.20, 0.42, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.225 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.225 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
Gap: 0.119 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=12, n=72, [-0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.119 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=12, n=72, [-0.20, 0.40, 0.00])
Gap: 0.078 eV
Transition (v -> c):
  (s=0, k=15, n=75, [0.08, 0.00, 0.33]) -> (s=0, k=6, n=76, [-0.42, 0.20, 0.00])
<class 'tuple'>
Gap: 0.078 eV
Transition (v -> c):
  (s=0, k=15, n=75, [0.08, 0.00, 0.33]) -> (s=0, k=6, n=76, [-0.42, 0.20, 0.00])
Gap: 1.005 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.40, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 1.005 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.40, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.155 eV
Transition (v -> c):
  (s=0, k=5, n=87, [-0.00, 0.38, -0.00]) -> (s=0, k=17, n=88, [0.40, 0.38, 0.33])
<class 'tuple'>
Gap: 0.155 eV
Transition (v -> c):
  (s=0, k=5, n=87, [-0.00, 0.38, -0.00]) -> (s=0, k=17, n=88, [0.40, 0.38, 0.33])
Gap: 1.259 eV
Transition (v -> c):
  (s=0, k=13, n=63, [0.00, 0.00, 0.33]) -> (s=0, k=22, n=64, [0.20, 0.40, 0.33])
<class 'tuple'>
Gap: 1.259 eV
Transition (v -> c):
  (s=0, k=13, n=63, [0.00, 0.00, 0.33]) -> (s=0, k=22, n=64, [0.20, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.231 eV
Transition (v -> c):
  (s=0, k=21, n=73, [-0.42, 0.20, 0.33]) -> (s=0, k=0, n=74, [0.08, -0.00, -0.00])
<class 'tuple'>
Gap: 0.231 eV
Transition (v -> c):
  (s=0, k=21, n=73, [-0.42, 0.20, 0.33]) -> (s=0, k=0, n=74, [0.08, -0.00, -0.00])
Gap: 0.643 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, -0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, -0.00])
<class 'tuple'>
Gap: 0.643 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, -0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.394 eV
Transition (v -> c):
  (s=0, k=3, n=75, [0.08, 0.20, 0.00]) -> (s=0, k=5, n=76, [0.42, 0.20, 0.00])
<class 'tuple'>
Gap: 0.394 eV
Transition (v -> c):
  (s=0, k=3, n=75, [0.08, 0.20, 0.00]) -> (s=0, k=5, n=76, [0.42, 0.20, 0.00])
Gap: 0.218 eV
Transition (v -> c):
  (s=0, k=4, n=75, [0.20, 0.20, -0.00]) -> (s=0, k=13, n=76, [-0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.218 eV
Transition (v -> c):
  (s=0, k=4, n=75, [0.20, 0.20, -0.00]) -> (s=0, k=13, n=76, [-0.00, 0.00, 0.33])
Gap: 0.978 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.12, 0.00, 0.12]) -> (s=0, k=11, n=88, [0.38, -0.00, 0.38])
<class 'tuple'>
Gap: 0.978 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.12, 0.00, 0.12]) -> (s=0, k=11, n=88, [0.38, -0.00, 0.38])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.496 eV
Transition (v -> c):
  (s=0, k=21, n=79, [-0.00, 0.40, 0.33]) -> (s=0, k=19, n=80, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 1.496 eV
Transition (v -> c):
  (s=0, k=21, n=79, [-0.00, 0.40, 0.33]) -> (s=0, k=19, n=80, [-0.40, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.942 eV
Transition (v -> c):
  (s=0, k=4, n=87, [0.12, 0.38, 0.12]) -> (s=0, k=5, n=88, [0.38, 0.38, 0.12])
<class 'tuple'>
Gap: 0.942 eV
Transition (v -> c):
  (s=0, k=4, n=87, [0.12, 0.38, 0.12]) -> (s=0, k=5, n=88, [0.38, 0.38, 0.12])
No gap
<class 'tuple'>
No gap
Gap: 0.542 eV
Transition (v -> c):
  (s=0, k=4, n=83, [-0.20, 0.12, 0.12]) -> (s=0, k=12, n=84, [0.40, 0.12, 0.38])
<class 'tuple'>
Gap: 0.542 eV
Transition (v -> c):
  (s=0, k=4, n=83, [-0.20, 0.12, 0.12]) -> (s=0, k=12, n=84, [0.40, 0.12, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.681 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.40, 0.00, 0.00]) -> (s=0, k=25, n=80, [-0.20, 0.40, 0.33])
<class 'tuple'>
Gap: 0.681 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.40, 0.00, 0.00]) -> (s=0, k=25, n=80, [-0.20, 0.40, 0.33])
Gap: 0.331 eV
Transition (v -> c):
  (s=0, k=13, n=85, [-0.40, 0.12, 0.33]) -> (s=0, k=4, n=86, [-0.20, 0.12, 0.00])
<class 'tuple'>
Gap: 0.331 eV
Transition (v -> c):
  (s=0, k=13, n=85, [-0.40, 0.12, 0.33]) -> (s=0, k=4, n=86, [-0.20, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.133 eV
Transition (v -> c):
  (s=0, k=19, n=73, [-0.12, 0.40, 0.33]) -> (s=0, k=8, n=74, [-0.38, 0.40, 0.00])
<class 'tuple'>
Gap: 0.133 eV
Transition (v -> c):
  (s=0, k=19, n=73, [-0.12, 0.40, 0.33]) -> (s=0, k=8, n=74, [-0.38, 0.40, 0.00])
Gap: 1.279 eV
Transition (v -> c):
  (s=0, k=15, n=87, [0.40, -0.00, 0.38]) -> (s=0, k=11, n=88, [-0.40, 0.40, 0.12])
<class 'tuple'>
Gap: 1.279 eV
Transition (v -> c):
  (s=0, k=15, n=87, [0.40, -0.00, 0.38]) -> (s=0, k=11, n=88, [-0.40, 0.40, 0.12])
Gap: 0.219 eV
Transition (v -> c):
  (s=0, k=3, n=89, [-0.12, 0.12, 0.12]) -> (s=0, k=10, n=90, [-0.38, 0.12, 0.38])
<class 'tuple'>
Gap: 0.219 eV
Transition (v -> c):
  (s=0, k=3, n=89, [-0.12, 0.12, 0.12]) -> (s=0, k=10, n=90, [-0.38, 0.12, 0.38])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.526 eV
Transition (v -> c):
  (s=0, k=3, n=83, [-0.12, 0.12, 0.12]) -> (s=0, k=1, n=84, [0.38, 0.12, 0.12])
<class 'tuple'>
Gap: 0.526 eV
Transition (v -> c):
  (s=0, k=3, n=83, [-0.12, 0.12, 0.12]) -> (s=0, k=1, n=84, [0.38, 0.12, 0.12])
No gap
<class 'tuple'>
No gap
Gap: 1.057 eV
Transition (v -> c):
  (s=0, k=13, n=87, [-0.00, 0.00, 0.33]) -> (s=0, k=12, n=88, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 1.057 eV
Transition (v -> c):
  (s=0, k=13, n=87, [-0.00, 0.00, 0.33]) -> (s=0, k=12, n=88, [-0.20, 0.40, -0.00])
Gap: 0.007 eV
Transition (v -> c):
  (s=0, k=12, n=83, [-0.20, 0.40, 0.00]) -> (s=0, k=15, n=84, [0.40, -0.00, 0.33])
<class 'tuple'>
Gap: 0.007 eV
Transition (v -> c):
  (s=0, k=12, n=83, [-0.20, 0.40, 0.00]) -> (s=0, k=15, n=84, [0.40, -0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.717 eV
Transition (v -> c):
  (s=0, k=10, n=79, [0.25, 0.40, -0.00]) -> (s=0, k=0, n=80, [0.08, 0.00, -0.00])
<class 'tuple'>
Gap: 0.717 eV
Transition (v -> c):
  (s=0, k=10, n=79, [0.25, 0.40, -0.00]) -> (s=0, k=0, n=80, [0.08, 0.00, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.337 eV
Transition (v -> c):
  (s=0, k=9, n=87, [-0.20, 0.38, -0.00]) -> (s=0, k=8, n=88, [-0.40, 0.38, 0.00])
<class 'tuple'>
Gap: 1.337 eV
Transition (v -> c):
  (s=0, k=9, n=87, [-0.20, 0.38, -0.00]) -> (s=0, k=8, n=88, [-0.40, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.952 eV
Transition (v -> c):
  (s=0, k=8, n=63, [0.00, 0.40, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.952 eV
Transition (v -> c):
  (s=0, k=8, n=63, [0.00, 0.40, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
Gap: 0.034 eV
Transition (v -> c):
  (s=0, k=2, n=65, [0.40, -0.00, -0.00]) -> (s=0, k=8, n=66, [0.00, 0.40, 0.00])
<class 'tuple'>
Gap: 0.034 eV
Transition (v -> c):
  (s=0, k=2, n=65, [0.40, -0.00, -0.00]) -> (s=0, k=8, n=66, [0.00, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.816 eV
Transition (v -> c):
  (s=0, k=17, n=67, [0.40, 0.38, 0.33]) -> (s=0, k=17, n=68, [0.40, 0.38, 0.33])
<class 'tuple'>
Gap: 0.816 eV
Transition (v -> c):
  (s=0, k=17, n=67, [0.40, 0.38, 0.33]) -> (s=0, k=17, n=68, [0.40, 0.38, 0.33])
Gap: 0.086 eV
Transition (v -> c):
  (s=0, k=3, n=79, [0.00, 0.20, 0.00]) -> (s=0, k=19, n=80, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 0.086 eV
Transition (v -> c):
  (s=0, k=3, n=79, [0.00, 0.20, 0.00]) -> (s=0, k=19, n=80, [-0.40, 0.20, 0.33])
Gap: 0.198 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.12, 0.12]) -> (s=0, k=7, n=80, [0.40, 0.38, 0.12])
<class 'tuple'>
Gap: 0.198 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.12, 0.12]) -> (s=0, k=7, n=80, [0.40, 0.38, 0.12])
Gap: 0.943 eV
Transition (v -> c):
  (s=0, k=5, n=83, [0.38, 0.38, 0.12]) -> (s=0, k=4, n=84, [0.12, 0.38, 0.12])
<class 'tuple'>
Gap: 0.943 eV
Transition (v -> c):
  (s=0, k=5, n=83, [0.38, 0.38, 0.12]) -> (s=0, k=4, n=84, [0.12, 0.38, 0.12])
Gap: 0.485 eV
Transition (v -> c):
  (s=0, k=13, n=71, [0.00, 0.00, 0.33]) -> (s=0, k=14, n=72, [0.20, -0.00, 0.33])
<class 'tuple'>
Gap: 0.485 eV
Transition (v -> c):
  (s=0, k=13, n=71, [0.00, 0.00, 0.33]) -> (s=0, k=14, n=72, [0.20, -0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.210 eV
Transition (v -> c):
  (s=0, k=7, n=77, [-0.20, 0.20, 0.00]) -> (s=0, k=15, n=78, [0.40, 0.00, 0.33])
<class 'tuple'>
Gap: 0.210 eV
Transition (v -> c):
  (s=0, k=7, n=77, [-0.20, 0.20, 0.00]) -> (s=0, k=15, n=78, [0.40, 0.00, 0.33])
Gap: 0.684 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.12, 0.00, 0.12]) -> (s=0, k=3, n=72, [0.38, 0.20, 0.12])
<class 'tuple'>
Gap: 0.684 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.12, 0.00, 0.12]) -> (s=0, k=3, n=72, [0.38, 0.20, 0.12])
Gap: 0.949 eV
Transition (v -> c):
  (s=0, k=12, n=51, [-0.20, 0.40, 0.00]) -> (s=0, k=13, n=52, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.949 eV
Transition (v -> c):
  (s=0, k=12, n=51, [-0.20, 0.40, 0.00]) -> (s=0, k=13, n=52, [0.00, 0.00, 0.33])
Gap: 0.045 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.20, 0.00, 0.00]) -> (s=0, k=12, n=88, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.045 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.20, 0.00, 0.00]) -> (s=0, k=12, n=88, [-0.20, 0.40, -0.00])
Gap: 1.790 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=22, n=64, [0.20, 0.40, 0.33])
<class 'tuple'>
Gap: 1.790 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=22, n=64, [0.20, 0.40, 0.33])
Gap: 1.645 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 1.645 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.152 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.12, 0.00, -0.00]) -> (s=0, k=10, n=80, [0.12, 0.00, 0.33])
<class 'tuple'>
Gap: 0.152 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.12, 0.00, -0.00]) -> (s=0, k=10, n=80, [0.12, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.413 eV
Transition (v -> c):
  (s=0, k=9, n=79, [0.20, 0.40, -0.00]) -> (s=0, k=19, n=80, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 1.413 eV
Transition (v -> c):
  (s=0, k=9, n=79, [0.20, 0.40, -0.00]) -> (s=0, k=19, n=80, [-0.40, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.220 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=3, n=84, [0.00, 0.20, 0.00])
<class 'tuple'>
Gap: 0.220 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=3, n=84, [0.00, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.232 eV
Transition (v -> c):
  (s=0, k=9, n=81, [0.20, 0.40, -0.00]) -> (s=0, k=7, n=82, [-0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 0.232 eV
Transition (v -> c):
  (s=0, k=9, n=81, [0.20, 0.40, -0.00]) -> (s=0, k=7, n=82, [-0.20, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.650 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.38, 0.12, 0.12]) -> (s=0, k=11, n=68, [-0.12, 0.12, 0.38])
<class 'tuple'>
Gap: 0.650 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.38, 0.12, 0.12]) -> (s=0, k=11, n=68, [-0.12, 0.12, 0.38])
Gap: 0.606 eV
Transition (v -> c):
  (s=0, k=2, n=69, [0.40, -0.00, 0.00]) -> (s=0, k=0, n=70, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.606 eV
Transition (v -> c):
  (s=0, k=2, n=69, [0.40, -0.00, 0.00]) -> (s=0, k=0, n=70, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.147 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, 0.00, 0.33]) -> (s=0, k=9, n=84, [0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.147 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, 0.00, 0.33]) -> (s=0, k=9, n=84, [0.20, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.686 eV
Transition (v -> c):
  (s=0, k=19, n=83, [-0.20, 0.38, 0.33]) -> (s=0, k=0, n=84, [0.00, 0.12, -0.00])
<class 'tuple'>
Gap: 0.686 eV
Transition (v -> c):
  (s=0, k=19, n=83, [-0.20, 0.38, 0.33]) -> (s=0, k=0, n=84, [0.00, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.444 eV
Transition (v -> c):
  (s=0, k=10, n=79, [0.12, 0.00, 0.33]) -> (s=0, k=10, n=80, [0.12, 0.00, 0.33])
<class 'tuple'>
Gap: 0.444 eV
Transition (v -> c):
  (s=0, k=10, n=79, [0.12, 0.00, 0.33]) -> (s=0, k=10, n=80, [0.12, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.962 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, 0.08, 0.00]) -> (s=0, k=11, n=84, [0.20, 0.42, 0.00])
<class 'tuple'>
Gap: 0.962 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, 0.08, 0.00]) -> (s=0, k=11, n=84, [0.20, 0.42, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.547 eV
Transition (v -> c):
  (s=0, k=12, n=67, [0.40, 0.12, 0.38]) -> (s=0, k=7, n=68, [0.40, 0.38, 0.12])
<class 'tuple'>
Gap: 1.547 eV
Transition (v -> c):
  (s=0, k=12, n=67, [0.40, 0.12, 0.38]) -> (s=0, k=7, n=68, [0.40, 0.38, 0.12])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.082 eV
Transition (v -> c):
  (s=0, k=14, n=79, [-0.38, 0.38, 0.38]) -> (s=0, k=10, n=80, [-0.38, 0.12, 0.38])
<class 'tuple'>
Gap: 1.082 eV
Transition (v -> c):
  (s=0, k=14, n=79, [-0.38, 0.38, 0.38]) -> (s=0, k=10, n=80, [-0.38, 0.12, 0.38])
Gap: 1.611 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 1.611 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.918 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.12, -0.00, 0.00]) -> (s=0, k=0, n=84, [0.12, -0.00, 0.00])
<class 'tuple'>
Gap: 0.918 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.12, -0.00, 0.00]) -> (s=0, k=0, n=84, [0.12, -0.00, 0.00])
Gap: 0.282 eV
Transition (v -> c):
  (s=0, k=24, n=77, [-0.40, 0.40, 0.38]) -> (s=0, k=15, n=78, [0.40, -0.00, 0.38])
<class 'tuple'>
Gap: 0.282 eV
Transition (v -> c):
  (s=0, k=24, n=77, [-0.40, 0.40, 0.38]) -> (s=0, k=15, n=78, [0.40, -0.00, 0.38])
Gap: 0.072 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=12, n=84, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.072 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=12, n=84, [-0.20, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.517 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.517 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.377 eV
Transition (v -> c):
  (s=0, k=18, n=73, [-0.38, 0.40, 0.38]) -> (s=0, k=15, n=74, [-0.12, 0.20, 0.38])
<class 'tuple'>
Gap: 0.377 eV
Transition (v -> c):
  (s=0, k=18, n=73, [-0.38, 0.40, 0.38]) -> (s=0, k=15, n=74, [-0.12, 0.20, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 1.306 eV
Transition (v -> c):
  (s=0, k=5, n=91, [0.00, 0.38, 0.00]) -> (s=0, k=0, n=92, [-0.00, 0.12, 0.00])
<class 'tuple'>
Gap: 1.306 eV
Transition (v -> c):
  (s=0, k=5, n=91, [0.00, 0.38, 0.00]) -> (s=0, k=0, n=92, [-0.00, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.903 eV
Transition (v -> c):
  (s=0, k=2, n=63, [-0.38, 0.12, 0.00]) -> (s=0, k=1, n=64, [0.38, 0.12, -0.00])
<class 'tuple'>
Gap: 0.903 eV
Transition (v -> c):
  (s=0, k=2, n=63, [-0.38, 0.12, 0.00]) -> (s=0, k=1, n=64, [0.38, 0.12, -0.00])
Gap: 0.448 eV
Transition (v -> c):
  (s=0, k=1, n=75, [0.25, 0.00, 0.25]) -> (s=0, k=14, n=76, [-0.08, 0.40, 0.25])
<class 'tuple'>
Gap: 0.448 eV
Transition (v -> c):
  (s=0, k=1, n=75, [0.25, 0.00, 0.25]) -> (s=0, k=14, n=76, [-0.08, 0.40, 0.25])
No gap
<class 'tuple'>
No gap
Gap: 0.039 eV
Transition (v -> c):
  (s=0, k=8, n=75, [-0.00, 0.40, 0.00]) -> (s=0, k=5, n=76, [0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.039 eV
Transition (v -> c):
  (s=0, k=8, n=75, [-0.00, 0.40, 0.00]) -> (s=0, k=5, n=76, [0.40, 0.20, 0.00])
Gap: 0.264 eV
Transition (v -> c):
  (s=0, k=13, n=83, [-0.00, 0.00, 0.33]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.264 eV
Transition (v -> c):
  (s=0, k=13, n=83, [-0.00, 0.00, 0.33]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.088 eV
Transition (v -> c):
  (s=0, k=0, n=57, [0.00, 0.00, 0.00]) -> (s=0, k=1, n=58, [0.20, 0.00, -0.00])
<class 'tuple'>
Gap: 0.088 eV
Transition (v -> c):
  (s=0, k=0, n=57, [0.00, 0.00, 0.00]) -> (s=0, k=1, n=58, [0.20, 0.00, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.094 eV
Transition (v -> c):
  (s=0, k=3, n=83, [-0.40, 0.12, 0.00]) -> (s=0, k=0, n=84, [-0.00, 0.12, -0.00])
<class 'tuple'>
Gap: 1.094 eV
Transition (v -> c):
  (s=0, k=3, n=83, [-0.40, 0.12, 0.00]) -> (s=0, k=0, n=84, [-0.00, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.565 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, -0.00, 0.00]) -> (s=0, k=1, n=84, [0.20, -0.00, 0.00])
<class 'tuple'>
Gap: 0.565 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, -0.00, 0.00]) -> (s=0, k=1, n=84, [0.20, -0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.132 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.132 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
Gap: 0.433 eV
Transition (v -> c):
  (s=0, k=8, n=79, [-0.40, 0.38, -0.00]) -> (s=0, k=18, n=80, [-0.40, 0.38, 0.33])
<class 'tuple'>
Gap: 0.433 eV
Transition (v -> c):
  (s=0, k=8, n=79, [-0.40, 0.38, -0.00]) -> (s=0, k=18, n=80, [-0.40, 0.38, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.046 eV
Transition (v -> c):
  (s=0, k=10, n=91, [-0.25, 0.38, 0.00]) -> (s=0, k=13, n=92, [0.25, 0.12, 0.33])
<class 'tuple'>
Gap: 0.046 eV
Transition (v -> c):
  (s=0, k=10, n=91, [-0.25, 0.38, 0.00]) -> (s=0, k=13, n=92, [0.25, 0.12, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.743 eV
Transition (v -> c):
  (s=0, k=5, n=83, [0.40, 0.20, -0.00]) -> (s=0, k=1, n=84, [0.20, 0.00, -0.00])
<class 'tuple'>
Gap: 0.743 eV
Transition (v -> c):
  (s=0, k=5, n=83, [0.40, 0.20, -0.00]) -> (s=0, k=1, n=84, [0.20, 0.00, -0.00])
Gap: 0.665 eV
Transition (v -> c):
  (s=0, k=1, n=91, [0.38, 0.00, 0.00]) -> (s=0, k=11, n=92, [0.38, 0.00, 0.33])
<class 'tuple'>
Gap: 0.665 eV
Transition (v -> c):
  (s=0, k=1, n=91, [0.38, 0.00, 0.00]) -> (s=0, k=11, n=92, [0.38, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.464 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=17, n=68, [0.20, 0.20, 0.33])
<class 'tuple'>
Gap: 1.464 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=17, n=68, [0.20, 0.20, 0.33])
Gap: 0.456 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=88, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 0.456 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=88, [-0.40, 0.20, 0.33])
Gap: 0.275 eV
Transition (v -> c):
  (s=0, k=24, n=77, [-0.40, 0.40, 0.38]) -> (s=0, k=0, n=78, [0.00, -0.00, 0.12])
<class 'tuple'>
Gap: 0.275 eV
Transition (v -> c):
  (s=0, k=24, n=77, [-0.40, 0.40, 0.38]) -> (s=0, k=0, n=78, [0.00, -0.00, 0.12])
Gap: 1.050 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=9, n=64, [0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 1.050 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=9, n=64, [0.20, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.188 eV
Transition (v -> c):
  (s=0, k=16, n=81, [0.00, 0.20, 0.33]) -> (s=0, k=13, n=82, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.188 eV
Transition (v -> c):
  (s=0, k=16, n=81, [0.00, 0.20, 0.33]) -> (s=0, k=13, n=82, [0.00, 0.00, 0.33])
Gap: 0.297 eV
Transition (v -> c):
  (s=0, k=2, n=63, [0.40, -0.00, -0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.297 eV
Transition (v -> c):
  (s=0, k=2, n=63, [0.40, -0.00, -0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.540 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, -0.00, 0.00]) -> (s=0, k=2, n=88, [0.40, -0.00, 0.00])
<class 'tuple'>
Gap: 0.540 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, -0.00, 0.00]) -> (s=0, k=2, n=88, [0.40, -0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.777 eV
Transition (v -> c):
  (s=0, k=3, n=79, [-0.40, 0.12, 0.00]) -> (s=0, k=8, n=80, [-0.40, 0.38, 0.00])
<class 'tuple'>
Gap: 0.777 eV
Transition (v -> c):
  (s=0, k=3, n=79, [-0.40, 0.12, 0.00]) -> (s=0, k=8, n=80, [-0.40, 0.38, 0.00])
Gap: 0.971 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.00, -0.00]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.971 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.00, -0.00]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.069 eV
Transition (v -> c):
  (s=0, k=0, n=69, [0.00, 0.00, 0.12]) -> (s=0, k=0, n=70, [0.00, 0.00, 0.12])
<class 'tuple'>
Gap: 0.069 eV
Transition (v -> c):
  (s=0, k=0, n=69, [0.00, 0.00, 0.12]) -> (s=0, k=0, n=70, [0.00, 0.00, 0.12])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.350 eV
Transition (v -> c):
  (s=0, k=5, n=67, [0.40, 0.20, 0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 1.350 eV
Transition (v -> c):
  (s=0, k=5, n=67, [0.40, 0.20, 0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, 0.00])
Gap: 0.123 eV
Transition (v -> c):
  (s=0, k=6, n=87, [0.20, 0.38, 0.00]) -> (s=0, k=8, n=88, [-0.40, 0.38, 0.00])
<class 'tuple'>
Gap: 0.123 eV
Transition (v -> c):
  (s=0, k=6, n=87, [0.20, 0.38, 0.00]) -> (s=0, k=8, n=88, [-0.40, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.153 eV
Transition (v -> c):
  (s=0, k=0, n=59, [0.00, 0.00, 0.00]) -> (s=0, k=7, n=60, [-0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 0.153 eV
Transition (v -> c):
  (s=0, k=0, n=59, [0.00, 0.00, 0.00]) -> (s=0, k=7, n=60, [-0.20, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.050 eV
Transition (v -> c):
  (s=0, k=2, n=57, [0.40, 0.00, 0.00]) -> (s=0, k=7, n=58, [-0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 0.050 eV
Transition (v -> c):
  (s=0, k=2, n=57, [0.40, 0.00, 0.00]) -> (s=0, k=7, n=58, [-0.20, 0.20, -0.00])
Gap: 0.161 eV
Transition (v -> c):
  (s=0, k=5, n=83, [-0.08, 0.12, -0.00]) -> (s=0, k=8, n=84, [0.42, 0.38, -0.00])
<class 'tuple'>
Gap: 0.161 eV
Transition (v -> c):
  (s=0, k=5, n=83, [-0.08, 0.12, -0.00]) -> (s=0, k=8, n=84, [0.42, 0.38, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.820 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=84, [0.40, -0.00, -0.00])
<class 'tuple'>
Gap: 0.820 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=84, [0.40, -0.00, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.169 eV
Transition (v -> c):
  (s=0, k=0, n=85, [0.00, 0.00, 0.00]) -> (s=0, k=1, n=86, [0.20, -0.00, 0.00])
<class 'tuple'>
Gap: 0.169 eV
Transition (v -> c):
  (s=0, k=0, n=85, [0.00, 0.00, 0.00]) -> (s=0, k=1, n=86, [0.20, -0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.239 eV
Transition (v -> c):
  (s=0, k=13, n=61, [-0.25, 0.40, 0.00]) -> (s=0, k=13, n=62, [-0.25, 0.40, 0.00])
<class 'tuple'>
Gap: 0.239 eV
Transition (v -> c):
  (s=0, k=13, n=61, [-0.25, 0.40, 0.00]) -> (s=0, k=13, n=62, [-0.25, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.676 eV
Transition (v -> c):
  (s=0, k=6, n=87, [-0.40, 0.20, 0.00]) -> (s=0, k=12, n=88, [-0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.676 eV
Transition (v -> c):
  (s=0, k=6, n=87, [-0.40, 0.20, 0.00]) -> (s=0, k=12, n=88, [-0.20, 0.40, 0.00])
Gap: 0.008 eV
Transition (v -> c):
  (s=0, k=9, n=85, [0.20, 0.40, 0.00]) -> (s=0, k=13, n=86, [0.00, -0.00, 0.33])
<class 'tuple'>
Gap: 0.008 eV
Transition (v -> c):
  (s=0, k=9, n=85, [0.20, 0.40, 0.00]) -> (s=0, k=13, n=86, [0.00, -0.00, 0.33])
Gap: 0.356 eV
Transition (v -> c):
  (s=0, k=3, n=91, [-0.12, 0.12, 0.00]) -> (s=0, k=2, n=92, [-0.38, 0.12, 0.00])
<class 'tuple'>
Gap: 0.356 eV
Transition (v -> c):
  (s=0, k=3, n=91, [-0.12, 0.12, 0.00]) -> (s=0, k=2, n=92, [-0.38, 0.12, 0.00])
Gap: 0.496 eV
Transition (v -> c):
  (s=0, k=4, n=91, [-0.20, 0.12, 0.12]) -> (s=0, k=0, n=92, [0.00, 0.12, 0.12])
<class 'tuple'>
Gap: 0.496 eV
Transition (v -> c):
  (s=0, k=4, n=91, [-0.20, 0.12, 0.12]) -> (s=0, k=0, n=92, [0.00, 0.12, 0.12])
Gap: 1.189 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.38, 0.12, 0.12]) -> (s=0, k=11, n=84, [-0.12, 0.12, 0.38])
<class 'tuple'>
Gap: 1.189 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.38, 0.12, 0.12]) -> (s=0, k=11, n=84, [-0.12, 0.12, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.195 eV
Transition (v -> c):
  (s=0, k=17, n=91, [0.20, 0.20, 0.33]) -> (s=0, k=23, n=92, [0.40, 0.40, 0.33])
<class 'tuple'>
Gap: 0.195 eV
Transition (v -> c):
  (s=0, k=17, n=91, [0.20, 0.20, 0.33]) -> (s=0, k=23, n=92, [0.40, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.885 eV
Transition (v -> c):
  (s=0, k=11, n=83, [0.20, 0.12, 0.33]) -> (s=0, k=3, n=84, [-0.40, 0.12, -0.00])
<class 'tuple'>
Gap: 0.885 eV
Transition (v -> c):
  (s=0, k=11, n=83, [0.20, 0.12, 0.33]) -> (s=0, k=3, n=84, [-0.40, 0.12, -0.00])
Gap: 0.279 eV
Transition (v -> c):
  (s=0, k=13, n=63, [-0.00, -0.00, 0.33]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.279 eV
Transition (v -> c):
  (s=0, k=13, n=63, [-0.00, -0.00, 0.33]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
Gap: 0.296 eV
Transition (v -> c):
  (s=0, k=1, n=55, [0.20, -0.00, 0.12]) -> (s=0, k=12, n=56, [-0.20, 0.40, 0.12])
<class 'tuple'>
Gap: 0.296 eV
Transition (v -> c):
  (s=0, k=1, n=55, [0.20, -0.00, 0.12]) -> (s=0, k=12, n=56, [-0.20, 0.40, 0.12])
Gap: 0.621 eV
Transition (v -> c):
  (s=0, k=15, n=87, [0.00, 0.38, 0.33]) -> (s=0, k=8, n=88, [-0.40, 0.38, 0.00])
<class 'tuple'>
Gap: 0.621 eV
Transition (v -> c):
  (s=0, k=15, n=87, [0.00, 0.38, 0.33]) -> (s=0, k=8, n=88, [-0.40, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.428 eV
Transition (v -> c):
  (s=0, k=13, n=77, [-0.00, 0.00, 0.33]) -> (s=0, k=9, n=78, [0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.428 eV
Transition (v -> c):
  (s=0, k=13, n=77, [-0.00, 0.00, 0.33]) -> (s=0, k=9, n=78, [0.20, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.053 eV
Transition (v -> c):
  (s=0, k=7, n=51, [-0.25, 0.20, -0.00]) -> (s=0, k=22, n=52, [-0.25, 0.20, 0.33])
<class 'tuple'>
Gap: 0.053 eV
Transition (v -> c):
  (s=0, k=7, n=51, [-0.25, 0.20, -0.00]) -> (s=0, k=22, n=52, [-0.25, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.553 eV
Transition (v -> c):
  (s=0, k=4, n=75, [-0.20, 0.12, -0.00]) -> (s=0, k=7, n=76, [0.40, 0.38, 0.00])
<class 'tuple'>
Gap: 0.553 eV
Transition (v -> c):
  (s=0, k=4, n=75, [-0.20, 0.12, -0.00]) -> (s=0, k=7, n=76, [0.40, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.100 eV
Transition (v -> c):
  (s=0, k=14, n=91, [0.20, 0.00, 0.33]) -> (s=0, k=9, n=92, [0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 1.100 eV
Transition (v -> c):
  (s=0, k=14, n=91, [0.20, 0.00, 0.33]) -> (s=0, k=9, n=92, [0.20, 0.40, 0.00])
Gap: 1.086 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, 0.00, 0.33]) -> (s=0, k=9, n=84, [0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 1.086 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, 0.00, 0.33]) -> (s=0, k=9, n=84, [0.20, 0.40, -0.00])
Gap: 0.268 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.08, 0.00]) -> (s=0, k=20, n=80, [-0.00, 0.25, 0.33])
<class 'tuple'>
Gap: 0.268 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.08, 0.00]) -> (s=0, k=20, n=80, [-0.00, 0.25, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.434 eV
Transition (v -> c):
  (s=0, k=1, n=75, [0.38, -0.00, -0.00]) -> (s=0, k=15, n=76, [-0.12, 0.20, 0.33])
<class 'tuple'>
Gap: 1.434 eV
Transition (v -> c):
  (s=0, k=1, n=75, [0.38, -0.00, -0.00]) -> (s=0, k=15, n=76, [-0.12, 0.20, 0.33])
Gap: 0.151 eV
Transition (v -> c):
  (s=0, k=18, n=75, [0.40, 0.20, 0.33]) -> (s=0, k=18, n=76, [0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 0.151 eV
Transition (v -> c):
  (s=0, k=18, n=75, [0.40, 0.20, 0.33]) -> (s=0, k=18, n=76, [0.40, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.130 eV
Transition (v -> c):
  (s=0, k=29, n=79, [-0.08, 0.40, 0.33]) -> (s=0, k=4, n=80, [0.25, 0.20, 0.00])
<class 'tuple'>
Gap: 0.130 eV
Transition (v -> c):
  (s=0, k=29, n=79, [-0.08, 0.40, 0.33]) -> (s=0, k=4, n=80, [0.25, 0.20, 0.00])
Gap: 1.180 eV
Transition (v -> c):
  (s=0, k=11, n=87, [-0.40, 0.40, 0.00]) -> (s=0, k=6, n=88, [-0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 1.180 eV
Transition (v -> c):
  (s=0, k=11, n=87, [-0.40, 0.40, 0.00]) -> (s=0, k=6, n=88, [-0.40, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.949 eV
Transition (v -> c):
  (s=0, k=0, n=59, [-0.00, 0.08, 0.00]) -> (s=0, k=0, n=60, [-0.00, 0.08, 0.00])
<class 'tuple'>
Gap: 0.949 eV
Transition (v -> c):
  (s=0, k=0, n=59, [-0.00, 0.08, 0.00]) -> (s=0, k=0, n=60, [-0.00, 0.08, 0.00])
Gap: 1.710 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=15, n=80, [0.40, -0.00, 0.33])
<class 'tuple'>
Gap: 1.710 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=15, n=80, [0.40, -0.00, 0.33])
Gap: 0.618 eV
Transition (v -> c):
  (s=0, k=4, n=75, [-0.25, 0.12, -0.00]) -> (s=0, k=21, n=76, [-0.42, 0.38, 0.33])
<class 'tuple'>
Gap: 0.618 eV
Transition (v -> c):
  (s=0, k=4, n=75, [-0.25, 0.12, -0.00]) -> (s=0, k=21, n=76, [-0.42, 0.38, 0.33])
Gap: 1.510 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 1.510 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.211 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=76, [0.40, -0.00, 0.00])
<class 'tuple'>
Gap: 0.211 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=76, [0.40, -0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.870 eV
Transition (v -> c):
  (s=0, k=10, n=75, [0.40, 0.40, -0.00]) -> (s=0, k=5, n=76, [0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.870 eV
Transition (v -> c):
  (s=0, k=10, n=75, [0.40, 0.40, -0.00]) -> (s=0, k=5, n=76, [0.40, 0.20, 0.00])
Gap: 0.139 eV
Transition (v -> c):
  (s=0, k=14, n=81, [0.20, -0.00, 0.33]) -> (s=0, k=10, n=82, [0.40, 0.40, 0.00])
<class 'tuple'>
Gap: 0.139 eV
Transition (v -> c):
  (s=0, k=14, n=81, [0.20, -0.00, 0.33]) -> (s=0, k=10, n=82, [0.40, 0.40, 0.00])
Gap: 1.064 eV
Transition (v -> c):
  (s=0, k=13, n=87, [0.00, 0.00, 0.33]) -> (s=0, k=11, n=88, [-0.40, 0.40, 0.00])
<class 'tuple'>
Gap: 1.064 eV
Transition (v -> c):
  (s=0, k=13, n=87, [0.00, 0.00, 0.33]) -> (s=0, k=11, n=88, [-0.40, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.374 eV
Transition (v -> c):
  (s=0, k=14, n=87, [0.20, -0.00, 0.33]) -> (s=0, k=2, n=88, [0.40, -0.00, -0.00])
<class 'tuple'>
Gap: 0.374 eV
Transition (v -> c):
  (s=0, k=14, n=87, [0.20, -0.00, 0.33]) -> (s=0, k=2, n=88, [0.40, -0.00, -0.00])
Gap: 0.006 eV
Transition (v -> c):
  (s=0, k=1, n=79, [0.20, -0.00, 0.00]) -> (s=0, k=6, n=80, [-0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.006 eV
Transition (v -> c):
  (s=0, k=1, n=79, [0.20, -0.00, 0.00]) -> (s=0, k=6, n=80, [-0.40, 0.20, 0.00])
Gap: 0.287 eV
Transition (v -> c):
  (s=0, k=7, n=71, [-0.20, 0.20, 0.00]) -> (s=0, k=4, n=72, [0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 0.287 eV
Transition (v -> c):
  (s=0, k=7, n=71, [-0.20, 0.20, 0.00]) -> (s=0, k=4, n=72, [0.20, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.179 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.20, 0.00, 0.00]) -> (s=0, k=15, n=84, [0.40, 0.00, 0.33])
<class 'tuple'>
Gap: 1.179 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.20, 0.00, 0.00]) -> (s=0, k=15, n=84, [0.40, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.990 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=11, n=72, [-0.40, 0.40, 0.00])
<class 'tuple'>
Gap: 0.990 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=11, n=72, [-0.40, 0.40, 0.00])
Gap: 0.671 eV
Transition (v -> c):
  (s=0, k=7, n=83, [0.38, 0.40, 0.12]) -> (s=0, k=8, n=84, [-0.38, 0.40, 0.12])
<class 'tuple'>
Gap: 0.671 eV
Transition (v -> c):
  (s=0, k=7, n=83, [0.38, 0.40, 0.12]) -> (s=0, k=8, n=84, [-0.38, 0.40, 0.12])
Gap: 0.051 eV
Transition (v -> c):
  (s=0, k=3, n=81, [-0.40, 0.12, 0.12]) -> (s=0, k=8, n=82, [-0.40, 0.38, 0.12])
<class 'tuple'>
Gap: 0.051 eV
Transition (v -> c):
  (s=0, k=3, n=81, [-0.40, 0.12, 0.12]) -> (s=0, k=8, n=82, [-0.40, 0.38, 0.12])
No gap
<class 'tuple'>
No gap
Gap: 0.686 eV
Transition (v -> c):
  (s=0, k=8, n=51, [-0.00, 0.40, 0.00]) -> (s=0, k=11, n=52, [-0.40, 0.40, -0.00])
<class 'tuple'>
Gap: 0.686 eV
Transition (v -> c):
  (s=0, k=8, n=51, [-0.00, 0.40, 0.00]) -> (s=0, k=11, n=52, [-0.40, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.293 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.12, -0.00]) -> (s=0, k=6, n=68, [0.20, 0.38, 0.00])
<class 'tuple'>
Gap: 0.293 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.12, -0.00]) -> (s=0, k=6, n=68, [0.20, 0.38, 0.00])
Gap: 0.675 eV
Transition (v -> c):
  (s=0, k=7, n=63, [0.38, 0.40, 0.12]) -> (s=0, k=12, n=64, [0.12, 0.20, 0.38])
<class 'tuple'>
Gap: 0.675 eV
Transition (v -> c):
  (s=0, k=7, n=63, [0.38, 0.40, 0.12]) -> (s=0, k=12, n=64, [0.12, 0.20, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.741 eV
Transition (v -> c):
  (s=0, k=10, n=91, [0.00, 0.12, 0.33]) -> (s=0, k=8, n=92, [-0.40, 0.38, -0.00])
<class 'tuple'>
Gap: 0.741 eV
Transition (v -> c):
  (s=0, k=10, n=91, [0.00, 0.12, 0.33]) -> (s=0, k=8, n=92, [-0.40, 0.38, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.085 eV
Transition (v -> c):
  (s=0, k=6, n=83, [-0.38, 0.38, 0.00]) -> (s=0, k=15, n=84, [-0.12, 0.38, 0.33])
<class 'tuple'>
Gap: 1.085 eV
Transition (v -> c):
  (s=0, k=6, n=83, [-0.38, 0.38, 0.00]) -> (s=0, k=15, n=84, [-0.12, 0.38, 0.33])
Gap: 1.630 eV
Transition (v -> c):
  (s=0, k=4, n=67, [-0.20, 0.12, -0.00]) -> (s=0, k=2, n=68, [0.40, 0.12, -0.00])
<class 'tuple'>
Gap: 1.630 eV
Transition (v -> c):
  (s=0, k=4, n=67, [-0.20, 0.12, -0.00]) -> (s=0, k=2, n=68, [0.40, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.366 eV
Transition (v -> c):
  (s=0, k=11, n=77, [-0.40, 0.40, 0.12]) -> (s=0, k=11, n=78, [-0.40, 0.40, 0.12])
<class 'tuple'>
Gap: 0.366 eV
Transition (v -> c):
  (s=0, k=11, n=77, [-0.40, 0.40, 0.12]) -> (s=0, k=11, n=78, [-0.40, 0.40, 0.12])
Gap: 0.305 eV
Transition (v -> c):
  (s=0, k=0, n=81, [0.00, 0.08, -0.00]) -> (s=0, k=0, n=82, [0.00, 0.08, -0.00])
<class 'tuple'>
Gap: 0.305 eV
Transition (v -> c):
  (s=0, k=0, n=81, [0.00, 0.08, -0.00]) -> (s=0, k=0, n=82, [0.00, 0.08, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.620 eV
Transition (v -> c):
  (s=0, k=3, n=91, [-0.12, 0.12, 0.00]) -> (s=0, k=2, n=92, [-0.38, 0.12, -0.00])
<class 'tuple'>
Gap: 0.620 eV
Transition (v -> c):
  (s=0, k=3, n=91, [-0.12, 0.12, 0.00]) -> (s=0, k=2, n=92, [-0.38, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.511 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, 0.00, -0.00]) -> (s=0, k=2, n=84, [0.40, 0.00, -0.00])
<class 'tuple'>
Gap: 0.511 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, 0.00, -0.00]) -> (s=0, k=2, n=84, [0.40, 0.00, -0.00])
Gap: 0.079 eV
Transition (v -> c):
  (s=0, k=12, n=73, [0.40, 0.12, 0.33]) -> (s=0, k=17, n=74, [0.40, 0.38, 0.33])
<class 'tuple'>
Gap: 0.079 eV
Transition (v -> c):
  (s=0, k=12, n=73, [0.40, 0.12, 0.33]) -> (s=0, k=17, n=74, [0.40, 0.38, 0.33])
Gap: 1.020 eV
Transition (v -> c):
  (s=0, k=4, n=83, [-0.20, 0.12, 0.00]) -> (s=0, k=15, n=84, [0.00, 0.38, 0.33])
<class 'tuple'>
Gap: 1.020 eV
Transition (v -> c):
  (s=0, k=4, n=83, [-0.20, 0.12, 0.00]) -> (s=0, k=15, n=84, [0.00, 0.38, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 1.244 eV
Transition (v -> c):
  (s=0, k=5, n=79, [-0.08, 0.12, -0.00]) -> (s=0, k=5, n=80, [-0.08, 0.12, -0.00])
<class 'tuple'>
Gap: 1.244 eV
Transition (v -> c):
  (s=0, k=5, n=79, [-0.08, 0.12, -0.00]) -> (s=0, k=5, n=80, [-0.08, 0.12, -0.00])
Gap: 0.294 eV
Transition (v -> c):
  (s=0, k=9, n=81, [0.20, 0.40, 0.00]) -> (s=0, k=1, n=82, [0.20, 0.00, -0.00])
<class 'tuple'>
Gap: 0.294 eV
Transition (v -> c):
  (s=0, k=9, n=81, [0.20, 0.40, 0.00]) -> (s=0, k=1, n=82, [0.20, 0.00, -0.00])
Gap: 0.324 eV
Transition (v -> c):
  (s=0, k=9, n=59, [0.20, 0.40, -0.00]) -> (s=0, k=0, n=60, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.324 eV
Transition (v -> c):
  (s=0, k=9, n=59, [0.20, 0.40, -0.00]) -> (s=0, k=0, n=60, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.287 eV
Transition (v -> c):
  (s=0, k=13, n=67, [-0.00, -0.00, 0.33]) -> (s=0, k=15, n=68, [0.40, -0.00, 0.33])
<class 'tuple'>
Gap: 1.287 eV
Transition (v -> c):
  (s=0, k=13, n=67, [-0.00, -0.00, 0.33]) -> (s=0, k=15, n=68, [0.40, -0.00, 0.33])
Gap: 0.385 eV
Transition (v -> c):
  (s=0, k=0, n=71, [-0.00, 0.12, -0.00]) -> (s=0, k=8, n=72, [-0.40, 0.38, -0.00])
<class 'tuple'>
Gap: 0.385 eV
Transition (v -> c):
  (s=0, k=0, n=71, [-0.00, 0.12, -0.00]) -> (s=0, k=8, n=72, [-0.40, 0.38, -0.00])
Gap: 1.280 eV
Transition (v -> c):
  (s=0, k=10, n=83, [-0.38, 0.42, -0.00]) -> (s=0, k=4, n=84, [0.12, 0.25, -0.00])
<class 'tuple'>
Gap: 1.280 eV
Transition (v -> c):
  (s=0, k=10, n=83, [-0.38, 0.42, -0.00]) -> (s=0, k=4, n=84, [0.12, 0.25, -0.00])
Gap: 0.464 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.38, 0.00, 0.00]) -> (s=0, k=6, n=84, [0.12, 0.40, 0.00])
<class 'tuple'>
Gap: 0.464 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.38, 0.00, 0.00]) -> (s=0, k=6, n=84, [0.12, 0.40, 0.00])
Gap: 0.408 eV
Transition (v -> c):
  (s=0, k=3, n=89, [-0.00, 0.20, -0.00]) -> (s=0, k=5, n=90, [0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.408 eV
Transition (v -> c):
  (s=0, k=3, n=89, [-0.00, 0.20, -0.00]) -> (s=0, k=5, n=90, [0.40, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.426 eV
Transition (v -> c):
  (s=0, k=7, n=83, [-0.20, 0.20, 0.00]) -> (s=0, k=12, n=84, [-0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.426 eV
Transition (v -> c):
  (s=0, k=7, n=83, [-0.20, 0.20, 0.00]) -> (s=0, k=12, n=84, [-0.20, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.831 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.12, 0.38]) -> (s=0, k=14, n=84, [-0.20, 0.12, 0.38])
<class 'tuple'>
Gap: 0.831 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.12, 0.38]) -> (s=0, k=14, n=84, [-0.20, 0.12, 0.38])
Gap: 0.603 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.08, 0.12, -0.00]) -> (s=0, k=5, n=68, [-0.08, 0.12, 0.00])
<class 'tuple'>
Gap: 0.603 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.08, 0.12, -0.00]) -> (s=0, k=5, n=68, [-0.08, 0.12, 0.00])
Gap: 0.589 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.38, -0.00, 0.00]) -> (s=0, k=17, n=84, [0.38, 0.40, 0.33])
<class 'tuple'>
Gap: 0.589 eV
Transition (v -> c):
  (s=0, k=1, n=83, [0.38, -0.00, 0.00]) -> (s=0, k=17, n=84, [0.38, 0.40, 0.33])
Gap: 0.387 eV
Transition (v -> c):
  (s=0, k=3, n=55, [0.08, 0.20, -0.00]) -> (s=0, k=5, n=56, [0.42, 0.20, -0.00])
<class 'tuple'>
Gap: 0.387 eV
Transition (v -> c):
  (s=0, k=3, n=55, [0.08, 0.20, -0.00]) -> (s=0, k=5, n=56, [0.42, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.557 eV
Transition (v -> c):
  (s=0, k=0, n=91, [0.00, 0.00, 0.00]) -> (s=0, k=9, n=92, [0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.557 eV
Transition (v -> c):
  (s=0, k=0, n=91, [0.00, 0.00, 0.00]) -> (s=0, k=9, n=92, [0.20, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.193 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.42, 0.00, 0.25]) -> (s=0, k=2, n=80, [0.42, 0.00, 0.25])
<class 'tuple'>
Gap: 1.193 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.42, 0.00, 0.25]) -> (s=0, k=2, n=80, [0.42, 0.00, 0.25])
No gap
<class 'tuple'>
No gap
Gap: 0.299 eV
Transition (v -> c):
  (s=0, k=2, n=69, [0.40, -0.00, 0.00]) -> (s=0, k=0, n=70, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.299 eV
Transition (v -> c):
  (s=0, k=2, n=69, [0.40, -0.00, 0.00]) -> (s=0, k=0, n=70, [0.00, 0.00, 0.00])
Gap: 0.153 eV
Transition (v -> c):
  (s=0, k=5, n=87, [0.00, 0.38, 0.00]) -> (s=0, k=18, n=88, [-0.40, 0.38, 0.33])
<class 'tuple'>
Gap: 0.153 eV
Transition (v -> c):
  (s=0, k=5, n=87, [0.00, 0.38, 0.00]) -> (s=0, k=18, n=88, [-0.40, 0.38, 0.33])
Gap: 1.397 eV
Transition (v -> c):
  (s=0, k=2, n=59, [0.40, 0.00, -0.00]) -> (s=0, k=0, n=60, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 1.397 eV
Transition (v -> c):
  (s=0, k=2, n=59, [0.40, 0.00, -0.00]) -> (s=0, k=0, n=60, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.301 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=22, n=80, [0.20, 0.40, 0.33])
<class 'tuple'>
Gap: 1.301 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=22, n=80, [0.20, 0.40, 0.33])
Gap: 0.780 eV
Transition (v -> c):
  (s=0, k=23, n=67, [-0.08, 0.38, 0.33]) -> (s=0, k=14, n=68, [0.42, 0.12, 0.33])
<class 'tuple'>
Gap: 0.780 eV
Transition (v -> c):
  (s=0, k=23, n=67, [-0.08, 0.38, 0.33]) -> (s=0, k=14, n=68, [0.42, 0.12, 0.33])
Gap: 0.918 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=72, [0.40, 0.00, -0.00])
<class 'tuple'>
Gap: 0.918 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=72, [0.40, 0.00, -0.00])
Gap: 0.812 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, 0.00, -0.00]) -> (s=0, k=2, n=72, [0.40, 0.00, -0.00])
<class 'tuple'>
Gap: 0.812 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, 0.00, -0.00]) -> (s=0, k=2, n=72, [0.40, 0.00, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.158 eV
Transition (v -> c):
  (s=0, k=12, n=67, [-0.20, 0.40, 0.00]) -> (s=0, k=23, n=68, [0.40, 0.40, 0.33])
<class 'tuple'>
Gap: 0.158 eV
Transition (v -> c):
  (s=0, k=12, n=67, [-0.20, 0.40, 0.00]) -> (s=0, k=23, n=68, [0.40, 0.40, 0.33])
Gap: 0.060 eV
Transition (v -> c):
  (s=0, k=10, n=83, [0.40, 0.40, 0.00]) -> (s=0, k=23, n=84, [0.40, 0.40, 0.33])
<class 'tuple'>
Gap: 0.060 eV
Transition (v -> c):
  (s=0, k=10, n=83, [0.40, 0.40, 0.00]) -> (s=0, k=23, n=84, [0.40, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.046 eV
Transition (v -> c):
  (s=0, k=26, n=63, [0.42, 0.40, 0.33]) -> (s=0, k=17, n=64, [0.42, 0.00, 0.33])
<class 'tuple'>
Gap: 1.046 eV
Transition (v -> c):
  (s=0, k=26, n=63, [0.42, 0.40, 0.33]) -> (s=0, k=17, n=64, [0.42, 0.00, 0.33])
Gap: 1.538 eV
Transition (v -> c):
  (s=0, k=3, n=87, [-0.40, 0.12, 0.00]) -> (s=0, k=2, n=88, [0.40, 0.12, -0.00])
<class 'tuple'>
Gap: 1.538 eV
Transition (v -> c):
  (s=0, k=3, n=87, [-0.40, 0.12, 0.00]) -> (s=0, k=2, n=88, [0.40, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.671 eV
Transition (v -> c):
  (s=0, k=3, n=87, [-0.40, 0.12, -0.00]) -> (s=0, k=6, n=88, [0.20, 0.38, 0.00])
<class 'tuple'>
Gap: 0.671 eV
Transition (v -> c):
  (s=0, k=3, n=87, [-0.40, 0.12, -0.00]) -> (s=0, k=6, n=88, [0.20, 0.38, 0.00])
Gap: 0.774 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.12, 0.33]) -> (s=0, k=9, n=84, [-0.20, 0.38, 0.00])
<class 'tuple'>
Gap: 0.774 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.12, 0.33]) -> (s=0, k=9, n=84, [-0.20, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.317 eV
Transition (v -> c):
  (s=0, k=3, n=81, [-0.12, 0.12, 0.12]) -> (s=0, k=3, n=82, [-0.12, 0.12, 0.12])
<class 'tuple'>
Gap: 0.317 eV
Transition (v -> c):
  (s=0, k=3, n=81, [-0.12, 0.12, 0.12]) -> (s=0, k=3, n=82, [-0.12, 0.12, 0.12])
No gap
<class 'tuple'>
No gap
Gap: 0.775 eV
Transition (v -> c):
  (s=0, k=1, n=75, [0.20, 0.00, -0.00]) -> (s=0, k=7, n=76, [-0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 0.775 eV
Transition (v -> c):
  (s=0, k=1, n=75, [0.20, 0.00, -0.00]) -> (s=0, k=7, n=76, [-0.20, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.627 eV
Transition (v -> c):
  (s=0, k=3, n=75, [-0.00, 0.20, -0.00]) -> (s=0, k=3, n=76, [-0.00, 0.20, -0.00])
<class 'tuple'>
Gap: 0.627 eV
Transition (v -> c):
  (s=0, k=3, n=75, [-0.00, 0.20, -0.00]) -> (s=0, k=3, n=76, [-0.00, 0.20, -0.00])
No gap
<class 'tuple'>
No gap

Error in 501
No gap
<class 'tuple'>
No gap
Gap: 0.866 eV
Transition (v -> c):
  (s=0, k=4, n=83, [0.12, 0.38, 0.00]) -> (s=0, k=6, n=84, [-0.38, 0.38, -0.00])
<class 'tuple'>
Gap: 0.866 eV
Transition (v -> c):
  (s=0, k=4, n=83, [0.12, 0.38, 0.00]) -> (s=0, k=6, n=84, [-0.38, 0.38, -0.00])
Gap: 0.149 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.08, 0.00, 0.00]) -> (s=0, k=2, n=80, [0.42, 0.00, 0.00])
<class 'tuple'>
Gap: 0.149 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.08, 0.00, 0.00]) -> (s=0, k=2, n=80, [0.42, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.447 eV
Transition (v -> c):
  (s=0, k=9, n=67, [-0.20, 0.38, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.12, 0.00])
<class 'tuple'>
Gap: 0.447 eV
Transition (v -> c):
  (s=0, k=9, n=67, [-0.20, 0.38, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.022 eV
Transition (v -> c):
  (s=0, k=1, n=71, [0.38, -0.00, 0.12]) -> (s=0, k=0, n=72, [0.12, -0.00, 0.12])
<class 'tuple'>
Gap: 0.022 eV
Transition (v -> c):
  (s=0, k=1, n=71, [0.38, -0.00, 0.12]) -> (s=0, k=0, n=72, [0.12, -0.00, 0.12])
Gap: 0.786 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.20, 0.00, 0.00]) -> (s=0, k=6, n=68, [-0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.786 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.20, 0.00, 0.00]) -> (s=0, k=6, n=68, [-0.40, 0.20, 0.00])
Gap: 0.809 eV
Transition (v -> c):
  (s=0, k=2, n=51, [0.40, -0.00, 0.00]) -> (s=0, k=13, n=52, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.809 eV
Transition (v -> c):
  (s=0, k=2, n=51, [0.40, -0.00, 0.00]) -> (s=0, k=13, n=52, [0.00, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.239 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=6, n=80, [-0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.239 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=6, n=80, [-0.40, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.114 eV
Transition (v -> c):
  (s=0, k=12, n=75, [-0.42, 0.40, -0.00]) -> (s=0, k=2, n=76, [0.42, 0.00, -0.00])
<class 'tuple'>
Gap: 0.114 eV
Transition (v -> c):
  (s=0, k=12, n=75, [-0.42, 0.40, -0.00]) -> (s=0, k=2, n=76, [0.42, 0.00, -0.00])
Gap: 0.344 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.12, 0.00]) -> (s=0, k=1, n=72, [0.20, 0.12, 0.00])
<class 'tuple'>
Gap: 0.344 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.12, 0.00]) -> (s=0, k=1, n=72, [0.20, 0.12, 0.00])
Gap: 0.011 eV
Transition (v -> c):
  (s=0, k=14, n=71, [0.42, 0.12, 0.33]) -> (s=0, k=5, n=72, [-0.08, 0.12, -0.00])
<class 'tuple'>
Gap: 0.011 eV
Transition (v -> c):
  (s=0, k=14, n=71, [0.42, 0.12, 0.33]) -> (s=0, k=5, n=72, [-0.08, 0.12, -0.00])
Gap: 0.097 eV
Transition (v -> c):
  (s=0, k=29, n=83, [-0.08, 0.40, 0.33]) -> (s=0, k=25, n=84, [0.25, 0.40, 0.33])
<class 'tuple'>
Gap: 0.097 eV
Transition (v -> c):
  (s=0, k=29, n=83, [-0.08, 0.40, 0.33]) -> (s=0, k=25, n=84, [0.25, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 1.295 eV
Transition (v -> c):
  (s=0, k=2, n=75, [0.40, 0.00, 0.00]) -> (s=0, k=7, n=76, [-0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 1.295 eV
Transition (v -> c):
  (s=0, k=2, n=75, [0.40, 0.00, 0.00]) -> (s=0, k=7, n=76, [-0.20, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.001 eV
Transition (v -> c):
  (s=0, k=11, n=71, [-0.40, 0.40, -0.00]) -> (s=0, k=9, n=72, [0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.001 eV
Transition (v -> c):
  (s=0, k=11, n=71, [-0.40, 0.40, -0.00]) -> (s=0, k=9, n=72, [0.20, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.407 eV
Transition (v -> c):
  (s=0, k=1, n=63, [0.20, 0.00, 0.00]) -> (s=0, k=12, n=64, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.407 eV
Transition (v -> c):
  (s=0, k=1, n=63, [0.20, 0.00, 0.00]) -> (s=0, k=12, n=64, [-0.20, 0.40, -0.00])
Gap: 0.808 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, -0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, 0.00])
<class 'tuple'>
Gap: 0.808 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, -0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, 0.00])
Gap: 0.241 eV
Transition (v -> c):
  (s=0, k=5, n=87, [-0.00, 0.38, 0.00]) -> (s=0, k=3, n=88, [-0.40, 0.12, -0.00])
<class 'tuple'>
Gap: 0.241 eV
Transition (v -> c):
  (s=0, k=5, n=87, [-0.00, 0.38, 0.00]) -> (s=0, k=3, n=88, [-0.40, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.412 eV
Transition (v -> c):
  (s=0, k=3, n=75, [0.00, 0.20, 0.00]) -> (s=0, k=13, n=76, [-0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.412 eV
Transition (v -> c):
  (s=0, k=3, n=75, [0.00, 0.20, 0.00]) -> (s=0, k=13, n=76, [-0.00, 0.00, 0.33])
Gap: 1.145 eV
Transition (v -> c):
  (s=0, k=6, n=83, [-0.38, 0.38, 0.00]) -> (s=0, k=9, n=84, [0.38, 0.12, 0.33])
<class 'tuple'>
Gap: 1.145 eV
Transition (v -> c):
  (s=0, k=6, n=83, [-0.38, 0.38, 0.00]) -> (s=0, k=9, n=84, [0.38, 0.12, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.633 eV
Transition (v -> c):
  (s=0, k=16, n=91, [0.20, 0.38, 0.33]) -> (s=0, k=9, n=92, [-0.20, 0.38, 0.00])
<class 'tuple'>
Gap: 0.633 eV
Transition (v -> c):
  (s=0, k=16, n=91, [0.20, 0.38, 0.33]) -> (s=0, k=9, n=92, [-0.20, 0.38, 0.00])
Gap: 0.940 eV
Transition (v -> c):
  (s=0, k=7, n=83, [0.40, 0.38, 0.12]) -> (s=0, k=5, n=84, [-0.00, 0.38, 0.12])
<class 'tuple'>
Gap: 0.940 eV
Transition (v -> c):
  (s=0, k=7, n=83, [0.40, 0.38, 0.12]) -> (s=0, k=5, n=84, [-0.00, 0.38, 0.12])
No gap
<class 'tuple'>
No gap
Gap: 1.284 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, 0.00, 0.00]) -> (s=0, k=22, n=84, [0.20, 0.40, 0.33])
<class 'tuple'>
Gap: 1.284 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, 0.00, 0.00]) -> (s=0, k=22, n=84, [0.20, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.689 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.38, -0.00, 0.00]) -> (s=0, k=0, n=88, [0.12, 0.00, 0.00])
<class 'tuple'>
Gap: 0.689 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.38, -0.00, 0.00]) -> (s=0, k=0, n=88, [0.12, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.074 eV
Transition (v -> c):
  (s=0, k=4, n=77, [-0.38, 0.20, -0.00]) -> (s=0, k=1, n=78, [0.38, 0.00, 0.00])
<class 'tuple'>
Gap: 0.074 eV
Transition (v -> c):
  (s=0, k=4, n=77, [-0.38, 0.20, -0.00]) -> (s=0, k=1, n=78, [0.38, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.387 eV
Transition (v -> c):
  (s=0, k=15, n=87, [0.40, -0.00, 0.33]) -> (s=0, k=17, n=88, [0.20, 0.20, 0.33])
<class 'tuple'>
Gap: 0.387 eV
Transition (v -> c):
  (s=0, k=15, n=87, [0.40, -0.00, 0.33]) -> (s=0, k=17, n=88, [0.20, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 1.283 eV
Transition (v -> c):
  (s=0, k=1, n=79, [0.38, -0.00, 0.00]) -> (s=0, k=15, n=80, [-0.12, 0.20, 0.33])
<class 'tuple'>
Gap: 1.283 eV
Transition (v -> c):
  (s=0, k=1, n=79, [0.38, -0.00, 0.00]) -> (s=0, k=15, n=80, [-0.12, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.054 eV
Transition (v -> c):
  (s=0, k=0, n=65, [0.00, 0.00, 0.00]) -> (s=0, k=13, n=66, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.054 eV
Transition (v -> c):
  (s=0, k=0, n=65, [0.00, 0.00, 0.00]) -> (s=0, k=13, n=66, [0.00, 0.00, 0.33])
Gap: 0.069 eV
Transition (v -> c):
  (s=0, k=10, n=79, [0.40, 0.40, -0.00]) -> (s=0, k=5, n=80, [0.40, 0.20, -0.00])
<class 'tuple'>
Gap: 0.069 eV
Transition (v -> c):
  (s=0, k=10, n=79, [0.40, 0.40, -0.00]) -> (s=0, k=5, n=80, [0.40, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.405 eV
Transition (v -> c):
  (s=0, k=1, n=75, [0.38, 0.00, -0.00]) -> (s=0, k=15, n=76, [-0.12, 0.20, 0.33])
<class 'tuple'>
Gap: 1.405 eV
Transition (v -> c):
  (s=0, k=1, n=75, [0.38, 0.00, -0.00]) -> (s=0, k=15, n=76, [-0.12, 0.20, 0.33])
Gap: 0.240 eV
Transition (v -> c):
  (s=0, k=9, n=79, [0.20, 0.40, 0.00]) -> (s=0, k=6, n=80, [-0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.240 eV
Transition (v -> c):
  (s=0, k=9, n=79, [0.20, 0.40, 0.00]) -> (s=0, k=6, n=80, [-0.40, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.188 eV
Transition (v -> c):
  (s=0, k=0, n=91, [0.08, 0.00, 0.00]) -> (s=0, k=6, n=92, [-0.42, 0.20, 0.00])
<class 'tuple'>
Gap: 0.188 eV
Transition (v -> c):
  (s=0, k=0, n=91, [0.08, 0.00, 0.00]) -> (s=0, k=6, n=92, [-0.42, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.103 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.42, -0.00]) -> (s=0, k=0, n=84, [0.00, 0.08, 0.00])
<class 'tuple'>
Gap: 1.103 eV
Transition (v -> c):
  (s=0, k=12, n=83, [0.40, 0.42, -0.00]) -> (s=0, k=0, n=84, [0.00, 0.08, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.470 eV
Transition (v -> c):
  (s=0, k=8, n=83, [-0.38, 0.40, 0.12]) -> (s=0, k=4, n=84, [-0.38, 0.20, 0.12])
<class 'tuple'>
Gap: 0.470 eV
Transition (v -> c):
  (s=0, k=8, n=83, [-0.38, 0.40, 0.12]) -> (s=0, k=4, n=84, [-0.38, 0.20, 0.12])
Gap: 0.497 eV
Transition (v -> c):
  (s=0, k=13, n=79, [0.00, 0.00, 0.33]) -> (s=0, k=14, n=80, [0.20, -0.00, 0.33])
<class 'tuple'>
Gap: 0.497 eV
Transition (v -> c):
  (s=0, k=13, n=79, [0.00, 0.00, 0.33]) -> (s=0, k=14, n=80, [0.20, -0.00, 0.33])
Gap: 0.569 eV
Transition (v -> c):
  (s=0, k=7, n=95, [-0.20, 0.20, -0.00]) -> (s=0, k=2, n=96, [0.40, -0.00, 0.00])
<class 'tuple'>
Gap: 0.569 eV
Transition (v -> c):
  (s=0, k=7, n=95, [-0.20, 0.20, -0.00]) -> (s=0, k=2, n=96, [0.40, -0.00, 0.00])
Gap: 1.417 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=88, [0.40, -0.00, -0.00])
<class 'tuple'>
Gap: 1.417 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=88, [0.40, -0.00, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.981 eV
Transition (v -> c):
  (s=0, k=6, n=79, [0.20, 0.38, 0.12]) -> (s=0, k=0, n=80, [0.00, 0.12, 0.12])
<class 'tuple'>
Gap: 0.981 eV
Transition (v -> c):
  (s=0, k=6, n=79, [0.20, 0.38, 0.12]) -> (s=0, k=0, n=80, [0.00, 0.12, 0.12])
Gap: 1.515 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, -0.00, -0.00]) -> (s=0, k=1, n=88, [0.20, -0.00, -0.00])
<class 'tuple'>
Gap: 1.515 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, -0.00, -0.00]) -> (s=0, k=1, n=88, [0.20, -0.00, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.474 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.12, 0.00]) -> (s=0, k=10, n=80, [0.00, 0.12, 0.33])
<class 'tuple'>
Gap: 0.474 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.12, 0.00]) -> (s=0, k=10, n=80, [0.00, 0.12, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.493 eV
Transition (v -> c):
  (s=0, k=5, n=83, [0.40, 0.20, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.493 eV
Transition (v -> c):
  (s=0, k=5, n=83, [0.40, 0.20, 0.00]) -> (s=0, k=0, n=84, [0.00, 0.00, 0.00])
Gap: 0.494 eV
Transition (v -> c):
  (s=0, k=5, n=51, [0.00, 0.38, -0.00]) -> (s=0, k=0, n=52, [0.00, 0.12, -0.00])
<class 'tuple'>
Gap: 0.494 eV
Transition (v -> c):
  (s=0, k=5, n=51, [0.00, 0.38, -0.00]) -> (s=0, k=0, n=52, [0.00, 0.12, -0.00])
Gap: 0.345 eV
Transition (v -> c):
  (s=0, k=4, n=91, [-0.20, 0.12, 0.00]) -> (s=0, k=2, n=92, [0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 0.345 eV
Transition (v -> c):
  (s=0, k=4, n=91, [-0.20, 0.12, 0.00]) -> (s=0, k=2, n=92, [0.40, 0.12, 0.00])
Gap: 0.213 eV
Transition (v -> c):
  (s=0, k=17, n=85, [0.20, 0.20, 0.33]) -> (s=0, k=0, n=86, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.213 eV
Transition (v -> c):
  (s=0, k=17, n=85, [0.20, 0.20, 0.33]) -> (s=0, k=0, n=86, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.230 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.12, -0.00, 0.12]) -> (s=0, k=2, n=88, [0.12, 0.20, 0.12])
<class 'tuple'>
Gap: 0.230 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.12, -0.00, 0.12]) -> (s=0, k=2, n=88, [0.12, 0.20, 0.12])
Gap: 0.159 eV
Transition (v -> c):
  (s=0, k=15, n=87, [0.40, 0.00, 0.33]) -> (s=0, k=0, n=88, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.159 eV
Transition (v -> c):
  (s=0, k=15, n=87, [0.40, 0.00, 0.33]) -> (s=0, k=0, n=88, [0.00, 0.00, 0.00])
Gap: 0.085 eV
Transition (v -> c):
  (s=0, k=14, n=81, [0.20, 0.00, 0.33]) -> (s=0, k=10, n=82, [0.40, 0.40, -0.00])
<class 'tuple'>
Gap: 0.085 eV
Transition (v -> c):
  (s=0, k=14, n=81, [0.20, 0.00, 0.33]) -> (s=0, k=10, n=82, [0.40, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.088 eV
Transition (v -> c):
  (s=0, k=1, n=53, [0.20, 0.12, 0.12]) -> (s=0, k=13, n=54, [-0.40, 0.12, 0.38])
<class 'tuple'>
Gap: 0.088 eV
Transition (v -> c):
  (s=0, k=1, n=53, [0.20, 0.12, 0.12]) -> (s=0, k=13, n=54, [-0.40, 0.12, 0.38])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.909 eV
Transition (v -> c):
  (s=0, k=12, n=87, [-0.42, 0.40, 0.00]) -> (s=0, k=0, n=88, [0.08, 0.00, -0.00])
<class 'tuple'>
Gap: 0.909 eV
Transition (v -> c):
  (s=0, k=12, n=87, [-0.42, 0.40, 0.00]) -> (s=0, k=0, n=88, [0.08, 0.00, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.515 eV
Transition (v -> c):
  (s=0, k=2, n=75, [0.40, 0.00, 0.00]) -> (s=0, k=4, n=76, [0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 0.515 eV
Transition (v -> c):
  (s=0, k=2, n=75, [0.40, 0.00, 0.00]) -> (s=0, k=4, n=76, [0.20, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.358 eV
Transition (v -> c):
  (s=0, k=4, n=87, [-0.20, 0.12, 0.12]) -> (s=0, k=2, n=88, [0.40, 0.12, 0.12])
<class 'tuple'>
Gap: 0.358 eV
Transition (v -> c):
  (s=0, k=4, n=87, [-0.20, 0.12, 0.12]) -> (s=0, k=2, n=88, [0.40, 0.12, 0.12])
Gap: 1.269 eV
Transition (v -> c):
  (s=0, k=16, n=87, [0.00, 0.20, 0.38]) -> (s=0, k=13, n=88, [0.00, 0.00, 0.38])
<class 'tuple'>
Gap: 1.269 eV
Transition (v -> c):
  (s=0, k=16, n=87, [0.00, 0.20, 0.38]) -> (s=0, k=13, n=88, [0.00, 0.00, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.025 eV
Transition (v -> c):
  (s=0, k=1, n=85, [0.38, 0.00, 0.12]) -> (s=0, k=19, n=86, [-0.12, 0.40, 0.38])
<class 'tuple'>
Gap: 0.025 eV
Transition (v -> c):
  (s=0, k=1, n=85, [0.38, 0.00, 0.12]) -> (s=0, k=19, n=86, [-0.12, 0.40, 0.38])
Gap: 0.022 eV
Transition (v -> c):
  (s=0, k=1, n=73, [0.20, 0.12, 0.00]) -> (s=0, k=0, n=74, [0.00, 0.12, -0.00])
<class 'tuple'>
Gap: 0.022 eV
Transition (v -> c):
  (s=0, k=1, n=73, [0.20, 0.12, 0.00]) -> (s=0, k=0, n=74, [0.00, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.041 eV
Transition (v -> c):
  (s=0, k=6, n=67, [-0.40, 0.20, 0.00]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 1.041 eV
Transition (v -> c):
  (s=0, k=6, n=67, [-0.40, 0.20, 0.00]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.196 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, 0.00, 0.00]) -> (s=0, k=7, n=84, [-0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 0.196 eV
Transition (v -> c):
  (s=0, k=2, n=83, [0.40, 0.00, 0.00]) -> (s=0, k=7, n=84, [-0.20, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.062 eV
Transition (v -> c):
  (s=0, k=0, n=91, [0.08, -0.00, 0.00]) -> (s=0, k=12, n=92, [-0.42, 0.40, -0.00])
<class 'tuple'>
Gap: 0.062 eV
Transition (v -> c):
  (s=0, k=0, n=91, [0.08, -0.00, 0.00]) -> (s=0, k=12, n=92, [-0.42, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.386 eV
Transition (v -> c):
  (s=0, k=1, n=59, [0.25, 0.00, 0.00]) -> (s=0, k=14, n=60, [-0.08, 0.40, 0.00])
<class 'tuple'>
Gap: 0.386 eV
Transition (v -> c):
  (s=0, k=1, n=59, [0.25, 0.00, 0.00]) -> (s=0, k=14, n=60, [-0.08, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.249 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, 0.00, 0.00]) -> (s=0, k=0, n=88, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.249 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, 0.00, 0.00]) -> (s=0, k=0, n=88, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.851 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.38, 0.00, 0.00]) -> (s=0, k=19, n=88, [-0.12, 0.40, 0.33])
<class 'tuple'>
Gap: 0.851 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.38, 0.00, 0.00]) -> (s=0, k=19, n=88, [-0.12, 0.40, 0.33])
Gap: 0.423 eV
Transition (v -> c):
  (s=0, k=13, n=81, [-0.00, 0.00, 0.38]) -> (s=0, k=21, n=82, [-0.00, 0.40, 0.38])
<class 'tuple'>
Gap: 0.423 eV
Transition (v -> c):
  (s=0, k=13, n=81, [-0.00, 0.00, 0.38]) -> (s=0, k=21, n=82, [-0.00, 0.40, 0.38])
Gap: 0.598 eV
Transition (v -> c):
  (s=0, k=3, n=87, [-0.40, 0.12, -0.00]) -> (s=0, k=10, n=88, [0.00, 0.12, 0.33])
<class 'tuple'>
Gap: 0.598 eV
Transition (v -> c):
  (s=0, k=3, n=87, [-0.40, 0.12, -0.00]) -> (s=0, k=10, n=88, [0.00, 0.12, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.079 eV
Transition (v -> c):
  (s=0, k=8, n=81, [-0.00, 0.00, 0.38]) -> (s=0, k=12, n=82, [-0.33, 0.20, 0.38])
<class 'tuple'>
Gap: 0.079 eV
Transition (v -> c):
  (s=0, k=8, n=81, [-0.00, 0.00, 0.38]) -> (s=0, k=12, n=82, [-0.33, 0.20, 0.38])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.125 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.00, 0.00]) -> (s=0, k=5, n=76, [0.40, 0.20, -0.00])
<class 'tuple'>
Gap: 0.125 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.00, 0.00]) -> (s=0, k=5, n=76, [0.40, 0.20, -0.00])
Gap: 1.254 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.40, 0.00, -0.00]) -> (s=0, k=7, n=80, [-0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 1.254 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.40, 0.00, -0.00]) -> (s=0, k=7, n=80, [-0.20, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.533 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=7, n=72, [-0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 0.533 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=7, n=72, [-0.20, 0.20, -0.00])
Gap: 0.434 eV
Transition (v -> c):
  (s=0, k=24, n=83, [-0.20, 0.25, 0.33]) -> (s=0, k=17, n=84, [0.40, 0.08, 0.33])
<class 'tuple'>
Gap: 0.434 eV
Transition (v -> c):
  (s=0, k=24, n=83, [-0.20, 0.25, 0.33]) -> (s=0, k=17, n=84, [0.40, 0.08, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.099 eV
Transition (v -> c):
  (s=0, k=3, n=87, [0.08, 0.20, 0.00]) -> (s=0, k=9, n=88, [0.08, 0.40, 0.00])
<class 'tuple'>
Gap: 0.099 eV
Transition (v -> c):
  (s=0, k=3, n=87, [0.08, 0.20, 0.00]) -> (s=0, k=9, n=88, [0.08, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.110 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.08, 0.12, 0.00]) -> (s=0, k=2, n=68, [0.42, 0.12, 0.00])
<class 'tuple'>
Gap: 1.110 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.08, 0.12, 0.00]) -> (s=0, k=2, n=68, [0.42, 0.12, 0.00])
Gap: 1.876 eV
Transition (v -> c):
  (s=0, k=13, n=63, [0.00, 0.00, 0.33]) -> (s=0, k=11, n=64, [-0.40, 0.40, -0.00])
<class 'tuple'>
Gap: 1.876 eV
Transition (v -> c):
  (s=0, k=13, n=63, [0.00, 0.00, 0.33]) -> (s=0, k=11, n=64, [-0.40, 0.40, -0.00])
Gap: 0.523 eV
Transition (v -> c):
  (s=0, k=7, n=79, [0.38, 0.40, 0.00]) -> (s=0, k=0, n=80, [0.12, -0.00, 0.00])
<class 'tuple'>
Gap: 0.523 eV
Transition (v -> c):
  (s=0, k=7, n=79, [0.38, 0.40, 0.00]) -> (s=0, k=0, n=80, [0.12, -0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.062 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, 0.00, 0.33]) -> (s=0, k=7, n=84, [-0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 0.062 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, 0.00, 0.33]) -> (s=0, k=7, n=84, [-0.20, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.626 eV
Transition (v -> c):
  (s=0, k=10, n=91, [0.00, 0.12, 0.33]) -> (s=0, k=0, n=92, [0.00, 0.12, 0.00])
<class 'tuple'>
Gap: 0.626 eV
Transition (v -> c):
  (s=0, k=10, n=91, [0.00, 0.12, 0.33]) -> (s=0, k=0, n=92, [0.00, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.132 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.12, 0.12, -0.00]) -> (s=0, k=8, n=68, [0.12, 0.12, 0.33])
<class 'tuple'>
Gap: 1.132 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.12, 0.12, -0.00]) -> (s=0, k=8, n=68, [0.12, 0.12, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.849 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=8, n=88, [0.00, 0.40, -0.00])
<class 'tuple'>
Gap: 1.849 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=8, n=88, [0.00, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.059 eV
Transition (v -> c):
  (s=0, k=5, n=67, [0.00, 0.38, 0.00]) -> (s=0, k=15, n=68, [-0.00, 0.38, 0.33])
<class 'tuple'>
Gap: 1.059 eV
Transition (v -> c):
  (s=0, k=5, n=67, [0.00, 0.38, 0.00]) -> (s=0, k=15, n=68, [-0.00, 0.38, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 2.029 eV
Transition (v -> c):
  (s=0, k=5, n=79, [-0.00, 0.38, 0.00]) -> (s=0, k=13, n=80, [-0.40, 0.12, 0.33])
<class 'tuple'>
Gap: 2.029 eV
Transition (v -> c):
  (s=0, k=5, n=79, [-0.00, 0.38, 0.00]) -> (s=0, k=13, n=80, [-0.40, 0.12, 0.33])
Gap: 0.204 eV
Transition (v -> c):
  (s=0, k=5, n=71, [0.00, 0.38, -0.00]) -> (s=0, k=2, n=72, [0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 0.204 eV
Transition (v -> c):
  (s=0, k=5, n=71, [0.00, 0.38, -0.00]) -> (s=0, k=2, n=72, [0.40, 0.12, 0.00])
Gap: 0.004 eV
Transition (v -> c):
  (s=0, k=5, n=85, [0.40, 0.20, 0.00]) -> (s=0, k=13, n=86, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.004 eV
Transition (v -> c):
  (s=0, k=5, n=85, [0.40, 0.20, 0.00]) -> (s=0, k=13, n=86, [0.00, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.975 eV
Transition (v -> c):
  (s=0, k=6, n=87, [-0.38, 0.38, 0.00]) -> (s=0, k=8, n=88, [0.12, 0.12, 0.33])
<class 'tuple'>
Gap: 0.975 eV
Transition (v -> c):
  (s=0, k=6, n=87, [-0.38, 0.38, 0.00]) -> (s=0, k=8, n=88, [0.12, 0.12, 0.33])
Gap: 0.050 eV
Transition (v -> c):
  (s=0, k=3, n=67, [-0.40, 0.08, 0.00]) -> (s=0, k=5, n=68, [0.00, 0.25, 0.00])
<class 'tuple'>
Gap: 0.050 eV
Transition (v -> c):
  (s=0, k=3, n=67, [-0.40, 0.08, 0.00]) -> (s=0, k=5, n=68, [0.00, 0.25, 0.00])
Gap: 0.151 eV
Transition (v -> c):
  (s=0, k=17, n=89, [0.20, 0.20, 0.33]) -> (s=0, k=11, n=90, [-0.40, 0.40, -0.00])
<class 'tuple'>
Gap: 0.151 eV
Transition (v -> c):
  (s=0, k=17, n=89, [0.20, 0.20, 0.33]) -> (s=0, k=11, n=90, [-0.40, 0.40, -0.00])
Gap: 0.012 eV
Transition (v -> c):
  (s=0, k=3, n=65, [-0.00, 0.20, -0.00]) -> (s=0, k=6, n=66, [-0.40, 0.20, -0.00])
<class 'tuple'>
Gap: 0.012 eV
Transition (v -> c):
  (s=0, k=3, n=65, [-0.00, 0.20, -0.00]) -> (s=0, k=6, n=66, [-0.40, 0.20, -0.00])
Gap: 0.383 eV
Transition (v -> c):
  (s=0, k=7, n=75, [-0.20, 0.20, -0.00]) -> (s=0, k=5, n=76, [0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.383 eV
Transition (v -> c):
  (s=0, k=7, n=75, [-0.20, 0.20, -0.00]) -> (s=0, k=5, n=76, [0.40, 0.20, 0.00])
Gap: 1.437 eV
Transition (v -> c):
  (s=0, k=11, n=83, [-0.40, 0.40, -0.00]) -> (s=0, k=24, n=84, [-0.40, 0.40, 0.33])
<class 'tuple'>
Gap: 1.437 eV
Transition (v -> c):
  (s=0, k=11, n=83, [-0.40, 0.40, -0.00]) -> (s=0, k=24, n=84, [-0.40, 0.40, 0.33])
Gap: 1.008 eV
Transition (v -> c):
  (s=0, k=3, n=71, [-0.00, 0.20, 0.00]) -> (s=0, k=15, n=72, [0.40, 0.00, 0.33])
<class 'tuple'>
Gap: 1.008 eV
Transition (v -> c):
  (s=0, k=3, n=71, [-0.00, 0.20, 0.00]) -> (s=0, k=15, n=72, [0.40, 0.00, 0.33])
Gap: 0.216 eV
Transition (v -> c):
  (s=0, k=16, n=67, [0.00, 0.20, 0.33]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.216 eV
Transition (v -> c):
  (s=0, k=16, n=67, [0.00, 0.20, 0.33]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 1.270 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 1.270 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
Gap: 0.434 eV
Transition (v -> c):
  (s=0, k=15, n=75, [0.00, 0.38, 0.33]) -> (s=0, k=6, n=76, [0.20, 0.38, 0.00])
<class 'tuple'>
Gap: 0.434 eV
Transition (v -> c):
  (s=0, k=15, n=75, [0.00, 0.38, 0.33]) -> (s=0, k=6, n=76, [0.20, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.139 eV
Transition (v -> c):
  (s=0, k=2, n=83, [-0.38, 0.12, 0.00]) -> (s=0, k=11, n=84, [-0.12, 0.12, 0.33])
<class 'tuple'>
Gap: 1.139 eV
Transition (v -> c):
  (s=0, k=2, n=83, [-0.38, 0.12, 0.00]) -> (s=0, k=11, n=84, [-0.12, 0.12, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.754 eV
Transition (v -> c):
  (s=0, k=19, n=83, [-0.20, 0.38, 0.33]) -> (s=0, k=4, n=84, [-0.20, 0.12, 0.00])
<class 'tuple'>
Gap: 0.754 eV
Transition (v -> c):
  (s=0, k=19, n=83, [-0.20, 0.38, 0.33]) -> (s=0, k=4, n=84, [-0.20, 0.12, 0.00])
Gap: 0.144 eV
Transition (v -> c):
  (s=0, k=18, n=87, [0.40, 0.20, 0.33]) -> (s=0, k=13, n=88, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.144 eV
Transition (v -> c):
  (s=0, k=18, n=87, [0.40, 0.20, 0.33]) -> (s=0, k=13, n=88, [0.00, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.096 eV
Transition (v -> c):
  (s=0, k=0, n=91, [0.00, 0.00, 0.00]) -> (s=0, k=24, n=92, [-0.40, 0.40, 0.33])
<class 'tuple'>
Gap: 0.096 eV
Transition (v -> c):
  (s=0, k=0, n=91, [0.00, 0.00, 0.00]) -> (s=0, k=24, n=92, [-0.40, 0.40, 0.33])
Gap: 0.871 eV
Transition (v -> c):
  (s=0, k=8, n=59, [-0.00, 0.40, -0.00]) -> (s=0, k=2, n=60, [0.40, -0.00, -0.00])
<class 'tuple'>
Gap: 0.871 eV
Transition (v -> c):
  (s=0, k=8, n=59, [-0.00, 0.40, -0.00]) -> (s=0, k=2, n=60, [0.40, -0.00, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.332 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.20, 0.12, 0.12]) -> (s=0, k=10, n=88, [0.00, 0.12, 0.38])
<class 'tuple'>
Gap: 1.332 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.20, 0.12, 0.12]) -> (s=0, k=10, n=88, [0.00, 0.12, 0.38])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.660 eV
Transition (v -> c):
  (s=0, k=14, n=75, [-0.20, 0.12, 0.33]) -> (s=0, k=2, n=76, [0.40, 0.12, -0.00])
<class 'tuple'>
Gap: 0.660 eV
Transition (v -> c):
  (s=0, k=14, n=75, [-0.20, 0.12, 0.33]) -> (s=0, k=2, n=76, [0.40, 0.12, -0.00])
Gap: 0.875 eV
Transition (v -> c):
  (s=0, k=4, n=71, [-0.38, 0.20, 0.00]) -> (s=0, k=4, n=72, [-0.38, 0.20, 0.00])
<class 'tuple'>
Gap: 0.875 eV
Transition (v -> c):
  (s=0, k=4, n=71, [-0.38, 0.20, 0.00]) -> (s=0, k=4, n=72, [-0.38, 0.20, 0.00])
Gap: 0.052 eV
Transition (v -> c):
  (s=0, k=14, n=87, [0.20, 0.00, 0.33]) -> (s=0, k=2, n=88, [0.40, 0.00, 0.00])
<class 'tuple'>
Gap: 0.052 eV
Transition (v -> c):
  (s=0, k=14, n=87, [0.20, 0.00, 0.33]) -> (s=0, k=2, n=88, [0.40, 0.00, 0.00])
Gap: 0.193 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=9, n=88, [0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.193 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=9, n=88, [0.20, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.900 eV
Transition (v -> c):
  (s=0, k=0, n=83, [-0.00, 0.08, 0.00]) -> (s=0, k=5, n=84, [0.00, 0.25, 0.00])
<class 'tuple'>
Gap: 0.900 eV
Transition (v -> c):
  (s=0, k=0, n=83, [-0.00, 0.08, 0.00]) -> (s=0, k=5, n=84, [0.00, 0.25, 0.00])
Gap: 0.698 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.698 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
Gap: 0.661 eV
Transition (v -> c):
  (s=0, k=0, n=51, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=52, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.661 eV
Transition (v -> c):
  (s=0, k=0, n=51, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=52, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.901 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=76, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 1.901 eV
Transition (v -> c):
  (s=0, k=0, n=75, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=76, [0.00, 0.00, 0.00])
Gap: 0.991 eV
Transition (v -> c):
  (s=0, k=11, n=67, [0.38, 0.00, 0.33]) -> (s=0, k=9, n=68, [-0.12, 0.40, 0.00])
<class 'tuple'>
Gap: 0.991 eV
Transition (v -> c):
  (s=0, k=11, n=67, [0.38, 0.00, 0.33]) -> (s=0, k=9, n=68, [-0.12, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.460 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.12, 0.12, 0.12]) -> (s=0, k=11, n=80, [-0.12, 0.12, 0.38])
<class 'tuple'>
Gap: 1.460 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.12, 0.12, 0.12]) -> (s=0, k=11, n=80, [-0.12, 0.12, 0.38])
Gap: 0.603 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, -0.00, 0.00]) -> (s=0, k=15, n=72, [0.40, -0.00, 0.33])
<class 'tuple'>
Gap: 0.603 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, -0.00, 0.00]) -> (s=0, k=15, n=72, [0.40, -0.00, 0.33])
Gap: 0.057 eV
Transition (v -> c):
  (s=0, k=12, n=83, [-0.20, 0.40, 0.12]) -> (s=0, k=2, n=84, [0.40, 0.00, 0.12])
<class 'tuple'>
Gap: 0.057 eV
Transition (v -> c):
  (s=0, k=12, n=83, [-0.20, 0.40, 0.12]) -> (s=0, k=2, n=84, [0.40, 0.00, 0.12])
Gap: 1.101 eV
Transition (v -> c):
  (s=0, k=10, n=83, [0.12, 0.00, 0.33]) -> (s=0, k=0, n=84, [0.12, 0.00, 0.00])
<class 'tuple'>
Gap: 1.101 eV
Transition (v -> c):
  (s=0, k=10, n=83, [0.12, 0.00, 0.33]) -> (s=0, k=0, n=84, [0.12, 0.00, 0.00])
Gap: 0.243 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=6, n=88, [-0.40, 0.20, -0.00])
<class 'tuple'>
Gap: 0.243 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=6, n=88, [-0.40, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.881 eV
Transition (v -> c):
  (s=0, k=2, n=63, [-0.38, 0.12, 0.00]) -> (s=0, k=1, n=64, [0.38, 0.12, -0.00])
<class 'tuple'>
Gap: 0.881 eV
Transition (v -> c):
  (s=0, k=2, n=63, [-0.38, 0.12, 0.00]) -> (s=0, k=1, n=64, [0.38, 0.12, -0.00])
Gap: 1.012 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.12, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.12, 0.00, 0.00])
<class 'tuple'>
Gap: 1.012 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.12, 0.00, 0.00]) -> (s=0, k=0, n=84, [0.12, 0.00, 0.00])
Gap: 1.261 eV
Transition (v -> c):
  (s=0, k=2, n=75, [0.40, 0.00, -0.00]) -> (s=0, k=7, n=76, [-0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 1.261 eV
Transition (v -> c):
  (s=0, k=2, n=75, [0.40, 0.00, -0.00]) -> (s=0, k=7, n=76, [-0.20, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.063 eV
Transition (v -> c):
  (s=0, k=3, n=85, [-0.40, 0.12, 0.12]) -> (s=0, k=15, n=86, [-0.00, 0.38, 0.38])
<class 'tuple'>
Gap: 0.063 eV
Transition (v -> c):
  (s=0, k=3, n=85, [-0.40, 0.12, 0.12]) -> (s=0, k=15, n=86, [-0.00, 0.38, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.229 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=88, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.229 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=88, [0.00, 0.00, 0.00])
Gap: 0.243 eV
Transition (v -> c):
  (s=0, k=24, n=77, [-0.40, 0.40, 0.38]) -> (s=0, k=0, n=78, [0.00, 0.00, 0.12])
<class 'tuple'>
Gap: 0.243 eV
Transition (v -> c):
  (s=0, k=24, n=77, [-0.40, 0.40, 0.38]) -> (s=0, k=0, n=78, [0.00, 0.00, 0.12])
No gap
<class 'tuple'>
No gap
Gap: 0.512 eV
Transition (v -> c):
  (s=0, k=13, n=71, [0.00, 0.00, 0.33]) -> (s=0, k=0, n=72, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.512 eV
Transition (v -> c):
  (s=0, k=13, n=71, [0.00, 0.00, 0.33]) -> (s=0, k=0, n=72, [0.00, 0.00, 0.00])
Gap: 1.060 eV
Transition (v -> c):
  (s=0, k=5, n=83, [-0.08, 0.12, -0.00]) -> (s=0, k=5, n=84, [-0.08, 0.12, -0.00])
<class 'tuple'>
Gap: 1.060 eV
Transition (v -> c):
  (s=0, k=5, n=83, [-0.08, 0.12, -0.00]) -> (s=0, k=5, n=84, [-0.08, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.026 eV
Transition (v -> c):
  (s=0, k=1, n=73, [0.38, 0.12, 0.12]) -> (s=0, k=14, n=74, [-0.38, 0.38, 0.38])
<class 'tuple'>
Gap: 0.026 eV
Transition (v -> c):
  (s=0, k=1, n=73, [0.38, 0.12, 0.12]) -> (s=0, k=14, n=74, [-0.38, 0.38, 0.38])
Gap: 1.619 eV
Transition (v -> c):
  (s=0, k=6, n=83, [0.20, 0.38, -0.00]) -> (s=0, k=6, n=84, [0.20, 0.38, -0.00])
<class 'tuple'>
Gap: 1.619 eV
Transition (v -> c):
  (s=0, k=6, n=83, [0.20, 0.38, -0.00]) -> (s=0, k=6, n=84, [0.20, 0.38, -0.00])
Gap: 0.854 eV
Transition (v -> c):
  (s=0, k=13, n=67, [0.00, 0.00, 0.33]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.854 eV
Transition (v -> c):
  (s=0, k=13, n=67, [0.00, 0.00, 0.33]) -> (s=0, k=13, n=68, [0.00, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.362 eV
Transition (v -> c):
  (s=0, k=1, n=79, [0.38, 0.00, -0.00]) -> (s=0, k=11, n=80, [0.38, -0.00, 0.33])
<class 'tuple'>
Gap: 0.362 eV
Transition (v -> c):
  (s=0, k=1, n=79, [0.38, 0.00, -0.00]) -> (s=0, k=11, n=80, [0.38, -0.00, 0.33])
Gap: 0.608 eV
Transition (v -> c):
  (s=0, k=24, n=87, [-0.40, 0.40, 0.33]) -> (s=0, k=1, n=88, [0.20, 0.00, -0.00])
<class 'tuple'>
Gap: 0.608 eV
Transition (v -> c):
  (s=0, k=24, n=87, [-0.40, 0.40, 0.33]) -> (s=0, k=1, n=88, [0.20, 0.00, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.412 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.412 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.306 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.12, 0.20, 0.12]) -> (s=0, k=4, n=80, [-0.38, 0.20, 0.12])
<class 'tuple'>
Gap: 0.306 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.12, 0.20, 0.12]) -> (s=0, k=4, n=80, [-0.38, 0.20, 0.12])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.093 eV
Transition (v -> c):
  (s=0, k=1, n=79, [0.20, -0.00, -0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.093 eV
Transition (v -> c):
  (s=0, k=1, n=79, [0.20, -0.00, -0.00]) -> (s=0, k=0, n=80, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.654 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.20, 0.12, 0.00]) -> (s=0, k=3, n=68, [-0.40, 0.12, -0.00])
<class 'tuple'>
Gap: 0.654 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.20, 0.12, 0.00]) -> (s=0, k=3, n=68, [-0.40, 0.12, -0.00])
Gap: 1.117 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.40, 0.12, -0.00]) -> (s=0, k=12, n=80, [0.40, 0.12, 0.33])
<class 'tuple'>
Gap: 1.117 eV
Transition (v -> c):
  (s=0, k=2, n=79, [0.40, 0.12, -0.00]) -> (s=0, k=12, n=80, [0.40, 0.12, 0.33])
Gap: 0.996 eV
Transition (v -> c):
  (s=0, k=3, n=83, [-0.12, 0.12, -0.00]) -> (s=0, k=15, n=84, [-0.12, 0.38, 0.33])
<class 'tuple'>
Gap: 0.996 eV
Transition (v -> c):
  (s=0, k=3, n=83, [-0.12, 0.12, -0.00]) -> (s=0, k=15, n=84, [-0.12, 0.38, 0.33])
Gap: 0.502 eV
Transition (v -> c):
  (s=0, k=0, n=81, [0.00, 0.00, 0.00]) -> (s=0, k=5, n=82, [0.40, 0.20, 0.00])
<class 'tuple'>
Gap: 0.502 eV
Transition (v -> c):
  (s=0, k=0, n=81, [0.00, 0.00, 0.00]) -> (s=0, k=5, n=82, [0.40, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.576 eV
Transition (v -> c):
  (s=0, k=7, n=67, [0.40, 0.38, 0.00]) -> (s=0, k=3, n=68, [-0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 1.576 eV
Transition (v -> c):
  (s=0, k=7, n=67, [0.40, 0.38, 0.00]) -> (s=0, k=3, n=68, [-0.40, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.504 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.42, -0.00, -0.00]) -> (s=0, k=2, n=88, [0.42, -0.00, -0.00])
<class 'tuple'>
Gap: 0.504 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.42, -0.00, -0.00]) -> (s=0, k=2, n=88, [0.42, -0.00, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.031 eV
Transition (v -> c):
  (s=0, k=0, n=79, [-0.00, 0.12, -0.00]) -> (s=0, k=15, n=80, [0.00, 0.38, 0.33])
<class 'tuple'>
Gap: 0.031 eV
Transition (v -> c):
  (s=0, k=0, n=79, [-0.00, 0.12, -0.00]) -> (s=0, k=15, n=80, [0.00, 0.38, 0.33])

Error in 789
Gap: 1.647 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, 0.12, -0.00]) -> (s=0, k=4, n=88, [-0.20, 0.12, 0.00])
<class 'tuple'>
Gap: 1.647 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, 0.12, -0.00]) -> (s=0, k=4, n=88, [-0.20, 0.12, 0.00])
Gap: 0.021 eV
Transition (v -> c):
  (s=0, k=24, n=85, [0.08, 0.40, 0.33]) -> (s=0, k=21, n=86, [-0.42, 0.20, 0.33])
<class 'tuple'>
Gap: 0.021 eV
Transition (v -> c):
  (s=0, k=24, n=85, [0.08, 0.40, 0.33]) -> (s=0, k=21, n=86, [-0.42, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.057 eV
Transition (v -> c):
  (s=0, k=17, n=83, [0.40, 0.38, 0.38]) -> (s=0, k=4, n=84, [-0.20, 0.12, 0.12])
<class 'tuple'>
Gap: 0.057 eV
Transition (v -> c):
  (s=0, k=17, n=83, [0.40, 0.38, 0.38]) -> (s=0, k=4, n=84, [-0.20, 0.12, 0.12])
Gap: 0.124 eV
Transition (v -> c):
  (s=0, k=12, n=75, [-0.20, 0.40, -0.00]) -> (s=0, k=8, n=76, [-0.00, 0.40, 0.00])
<class 'tuple'>
Gap: 0.124 eV
Transition (v -> c):
  (s=0, k=12, n=75, [-0.20, 0.40, -0.00]) -> (s=0, k=8, n=76, [-0.00, 0.40, 0.00])
Gap: 0.135 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.25, 0.00, 0.25]) -> (s=0, k=14, n=88, [-0.08, 0.40, 0.25])
<class 'tuple'>
Gap: 0.135 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.25, 0.00, 0.25]) -> (s=0, k=14, n=88, [-0.08, 0.40, 0.25])
No gap
<class 'tuple'>
No gap
Gap: 0.574 eV
Transition (v -> c):
  (s=0, k=5, n=63, [-0.08, 0.12, 0.00]) -> (s=0, k=0, n=64, [0.08, 0.12, 0.00])
<class 'tuple'>
Gap: 0.574 eV
Transition (v -> c):
  (s=0, k=5, n=63, [-0.08, 0.12, 0.00]) -> (s=0, k=0, n=64, [0.08, 0.12, 0.00])
Gap: 0.020 eV
Transition (v -> c):
  (s=0, k=3, n=67, [-0.00, 0.20, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.020 eV
Transition (v -> c):
  (s=0, k=3, n=67, [-0.00, 0.20, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
Gap: 0.175 eV
Transition (v -> c):
  (s=0, k=3, n=61, [-0.40, 0.08, -0.00]) -> (s=0, k=14, n=62, [-0.20, 0.42, 0.00])
<class 'tuple'>
Gap: 0.175 eV
Transition (v -> c):
  (s=0, k=3, n=61, [-0.40, 0.08, -0.00]) -> (s=0, k=14, n=62, [-0.20, 0.42, 0.00])
Gap: 0.101 eV
Transition (v -> c):
  (s=0, k=1, n=65, [0.25, 0.08, 0.00]) -> (s=0, k=21, n=66, [-0.42, 0.08, 0.33])
<class 'tuple'>
Gap: 0.101 eV
Transition (v -> c):
  (s=0, k=1, n=65, [0.25, 0.08, 0.00]) -> (s=0, k=21, n=66, [-0.42, 0.08, 0.33])
Gap: 0.703 eV
Transition (v -> c):
  (s=0, k=14, n=71, [0.20, -0.00, 0.33]) -> (s=0, k=21, n=72, [0.00, 0.40, 0.33])
<class 'tuple'>
Gap: 0.703 eV
Transition (v -> c):
  (s=0, k=14, n=71, [0.20, -0.00, 0.33]) -> (s=0, k=21, n=72, [0.00, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 1.464 eV
Transition (v -> c):
  (s=0, k=23, n=63, [-0.08, 0.08, 0.33]) -> (s=0, k=24, n=64, [0.08, 0.25, 0.33])
<class 'tuple'>
Gap: 1.464 eV
Transition (v -> c):
  (s=0, k=23, n=63, [-0.08, 0.08, 0.33]) -> (s=0, k=24, n=64, [0.08, 0.25, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.758 eV
Transition (v -> c):
  (s=0, k=8, n=67, [-0.38, 0.40, 0.12]) -> (s=0, k=12, n=68, [0.12, 0.20, 0.38])
<class 'tuple'>
Gap: 0.758 eV
Transition (v -> c):
  (s=0, k=8, n=67, [-0.38, 0.40, 0.12]) -> (s=0, k=12, n=68, [0.12, 0.20, 0.38])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.352 eV
Transition (v -> c):
  (s=0, k=11, n=63, [0.42, 0.40, 0.00]) -> (s=0, k=13, n=64, [-0.25, 0.40, 0.00])
<class 'tuple'>
Gap: 0.352 eV
Transition (v -> c):
  (s=0, k=11, n=63, [0.42, 0.40, 0.00]) -> (s=0, k=13, n=64, [-0.25, 0.40, 0.00])
Gap: 1.173 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=64, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 1.173 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=64, [-0.40, 0.20, 0.33])
Gap: 0.592 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, 0.00, 0.00]) -> (s=0, k=13, n=88, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.592 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, 0.00, 0.00]) -> (s=0, k=13, n=88, [0.00, 0.00, 0.33])
Gap: 0.363 eV
Transition (v -> c):
  (s=0, k=10, n=71, [0.00, 0.12, 0.33]) -> (s=0, k=6, n=72, [0.20, 0.38, 0.00])
<class 'tuple'>
Gap: 0.363 eV
Transition (v -> c):
  (s=0, k=10, n=71, [0.00, 0.12, 0.33]) -> (s=0, k=6, n=72, [0.20, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.215 eV
Transition (v -> c):
  (s=0, k=17, n=63, [0.40, 0.38, 0.33]) -> (s=0, k=2, n=64, [0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 0.215 eV
Transition (v -> c):
  (s=0, k=17, n=63, [0.40, 0.38, 0.33]) -> (s=0, k=2, n=64, [0.40, 0.12, 0.00])
Gap: 0.247 eV
Transition (v -> c):
  (s=0, k=12, n=71, [-0.42, 0.40, 0.25]) -> (s=0, k=0, n=72, [0.08, 0.00, 0.25])
<class 'tuple'>
Gap: 0.247 eV
Transition (v -> c):
  (s=0, k=12, n=71, [-0.42, 0.40, 0.25]) -> (s=0, k=0, n=72, [0.08, 0.00, 0.25])
Gap: 0.525 eV
Transition (v -> c):
  (s=0, k=13, n=71, [0.00, 0.00, 0.33]) -> (s=0, k=12, n=72, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.525 eV
Transition (v -> c):
  (s=0, k=13, n=71, [0.00, 0.00, 0.33]) -> (s=0, k=12, n=72, [-0.20, 0.40, -0.00])
Gap: 0.025 eV
Transition (v -> c):
  (s=0, k=0, n=67, [-0.00, 0.12, 0.12]) -> (s=0, k=2, n=68, [0.40, 0.12, 0.12])
<class 'tuple'>
Gap: 0.025 eV
Transition (v -> c):
  (s=0, k=0, n=67, [-0.00, 0.12, 0.12]) -> (s=0, k=2, n=68, [0.40, 0.12, 0.12])
Gap: 0.519 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.12, 0.20, 0.12]) -> (s=0, k=1, n=68, [0.38, -0.00, 0.12])
<class 'tuple'>
Gap: 0.519 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.12, 0.20, 0.12]) -> (s=0, k=1, n=68, [0.38, -0.00, 0.12])
No gap
<class 'tuple'>
No gap
Gap: 0.963 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, 0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, -0.00])
<class 'tuple'>
Gap: 0.963 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, 0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, -0.00])
Gap: 0.339 eV
Transition (v -> c):
  (s=0, k=0, n=71, [-0.00, 0.12, 0.12]) -> (s=0, k=13, n=72, [-0.40, 0.12, 0.38])
<class 'tuple'>
Gap: 0.339 eV
Transition (v -> c):
  (s=0, k=0, n=71, [-0.00, 0.12, 0.12]) -> (s=0, k=13, n=72, [-0.40, 0.12, 0.38])
Gap: 2.346 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.12, 0.00]) -> (s=0, k=1, n=64, [0.20, 0.12, 0.00])
<class 'tuple'>
Gap: 2.346 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.12, 0.00]) -> (s=0, k=1, n=64, [0.20, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.525 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.42, 0.00, -0.00]) -> (s=0, k=2, n=72, [0.42, 0.00, -0.00])
<class 'tuple'>
Gap: 0.525 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.42, 0.00, -0.00]) -> (s=0, k=2, n=72, [0.42, 0.00, -0.00])
Gap: 0.486 eV
Transition (v -> c):
  (s=0, k=2, n=59, [0.42, 0.00, 0.00]) -> (s=0, k=11, n=60, [0.42, 0.40, 0.00])
<class 'tuple'>
Gap: 0.486 eV
Transition (v -> c):
  (s=0, k=2, n=59, [0.42, 0.00, 0.00]) -> (s=0, k=11, n=60, [0.42, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.360 eV
Transition (v -> c):
  (s=0, k=0, n=51, [0.08, 0.00, -0.00]) -> (s=0, k=0, n=52, [0.08, 0.00, -0.00])
<class 'tuple'>
Gap: 0.360 eV
Transition (v -> c):
  (s=0, k=0, n=51, [0.08, 0.00, -0.00]) -> (s=0, k=0, n=52, [0.08, 0.00, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.205 eV
Transition (v -> c):
  (s=0, k=13, n=47, [0.00, -0.00, 0.33]) -> (s=0, k=0, n=48, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.205 eV
Transition (v -> c):
  (s=0, k=13, n=47, [0.00, -0.00, 0.33]) -> (s=0, k=0, n=48, [0.00, 0.00, 0.00])
Gap: 0.265 eV
Transition (v -> c):
  (s=0, k=13, n=63, [0.00, -0.00, 0.33]) -> (s=0, k=13, n=64, [0.00, -0.00, 0.33])
<class 'tuple'>
Gap: 0.265 eV
Transition (v -> c):
  (s=0, k=13, n=63, [0.00, -0.00, 0.33]) -> (s=0, k=13, n=64, [0.00, -0.00, 0.33])
Gap: 0.611 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=15, n=68, [0.40, 0.00, 0.33])
<class 'tuple'>
Gap: 0.611 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=15, n=68, [0.40, 0.00, 0.33])
Gap: 0.297 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.297 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, -0.00])
Gap: 1.621 eV
Transition (v -> c):
  (s=0, k=1, n=71, [0.20, 0.12, -0.00]) -> (s=0, k=7, n=72, [0.40, 0.38, 0.00])
<class 'tuple'>
Gap: 1.621 eV
Transition (v -> c):
  (s=0, k=1, n=71, [0.20, 0.12, -0.00]) -> (s=0, k=7, n=72, [0.40, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.193 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, -0.00, 0.00]) -> (s=0, k=12, n=72, [-0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.193 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, -0.00, 0.00]) -> (s=0, k=12, n=72, [-0.20, 0.40, 0.00])
Gap: 0.664 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.00, 0.38, -0.00]) -> (s=0, k=6, n=68, [0.20, 0.38, -0.00])
<class 'tuple'>
Gap: 0.664 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.00, 0.38, -0.00]) -> (s=0, k=6, n=68, [0.20, 0.38, -0.00])
Gap: 0.309 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.42, 0.00, 0.00]) -> (s=0, k=17, n=68, [0.42, 0.00, 0.33])
<class 'tuple'>
Gap: 0.309 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.42, 0.00, 0.00]) -> (s=0, k=17, n=68, [0.42, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.564 eV
Transition (v -> c):
  (s=0, k=0, n=61, [0.08, 0.00, 0.00]) -> (s=0, k=25, n=62, [0.25, 0.40, 0.33])
<class 'tuple'>
Gap: 0.564 eV
Transition (v -> c):
  (s=0, k=0, n=61, [0.08, 0.00, 0.00]) -> (s=0, k=25, n=62, [0.25, 0.40, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.026 eV
Transition (v -> c):
  (s=0, k=0, n=87, [-0.00, 0.12, 0.00]) -> (s=0, k=2, n=88, [0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 0.026 eV
Transition (v -> c):
  (s=0, k=0, n=87, [-0.00, 0.12, 0.00]) -> (s=0, k=2, n=88, [0.40, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.003 eV
Transition (v -> c):
  (s=0, k=3, n=87, [-0.40, 0.12, -0.00]) -> (s=0, k=4, n=88, [-0.20, 0.12, -0.00])
<class 'tuple'>
Gap: 0.003 eV
Transition (v -> c):
  (s=0, k=3, n=87, [-0.40, 0.12, -0.00]) -> (s=0, k=4, n=88, [-0.20, 0.12, -0.00])
Gap: 0.333 eV
Transition (v -> c):
  (s=0, k=12, n=67, [-0.20, 0.40, -0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, -0.00])
<class 'tuple'>
Gap: 0.333 eV
Transition (v -> c):
  (s=0, k=12, n=67, [-0.20, 0.40, -0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, -0.00])
Gap: 0.168 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=72, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.168 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=72, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.452 eV
Transition (v -> c):
  (s=0, k=10, n=55, [0.40, 0.40, -0.00]) -> (s=0, k=0, n=56, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.452 eV
Transition (v -> c):
  (s=0, k=10, n=55, [0.40, 0.40, -0.00]) -> (s=0, k=0, n=56, [0.00, 0.00, 0.00])
Gap: 0.044 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, -0.00, 0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.044 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, -0.00, 0.00]) -> (s=0, k=12, n=68, [-0.20, 0.40, 0.00])
Gap: 0.090 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, 0.00, -0.00]) -> (s=0, k=12, n=72, [-0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.090 eV
Transition (v -> c):
  (s=0, k=2, n=71, [0.40, 0.00, -0.00]) -> (s=0, k=12, n=72, [-0.20, 0.40, 0.00])
Gap: 1.280 eV
Transition (v -> c):
  (s=0, k=13, n=71, [-0.00, 0.00, 0.33]) -> (s=0, k=13, n=72, [-0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 1.280 eV
Transition (v -> c):
  (s=0, k=13, n=71, [-0.00, 0.00, 0.33]) -> (s=0, k=13, n=72, [-0.00, 0.00, 0.33])
Gap: 0.050 eV
Transition (v -> c):
  (s=0, k=12, n=59, [-0.42, 0.40, 0.12]) -> (s=0, k=17, n=60, [0.42, -0.00, 0.38])
<class 'tuple'>
Gap: 0.050 eV
Transition (v -> c):
  (s=0, k=12, n=59, [-0.42, 0.40, 0.12]) -> (s=0, k=17, n=60, [0.42, -0.00, 0.38])
Gap: 0.765 eV
Transition (v -> c):
  (s=0, k=0, n=47, [0.00, 0.00, 0.00]) -> (s=0, k=11, n=48, [-0.40, 0.40, -0.00])
<class 'tuple'>
Gap: 0.765 eV
Transition (v -> c):
  (s=0, k=0, n=47, [0.00, 0.00, 0.00]) -> (s=0, k=11, n=48, [-0.40, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.697 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=64, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 0.697 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=64, [-0.40, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.635 eV
Transition (v -> c):
  (s=0, k=4, n=67, [-0.20, 0.12, 0.12]) -> (s=0, k=10, n=68, [0.00, 0.12, 0.38])
<class 'tuple'>
Gap: 0.635 eV
Transition (v -> c):
  (s=0, k=4, n=67, [-0.20, 0.12, 0.12]) -> (s=0, k=10, n=68, [0.00, 0.12, 0.38])
Gap: 0.757 eV
Transition (v -> c):
  (s=0, k=13, n=51, [-0.00, 0.00, 0.33]) -> (s=0, k=4, n=52, [0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 0.757 eV
Transition (v -> c):
  (s=0, k=13, n=51, [-0.00, 0.00, 0.33]) -> (s=0, k=4, n=52, [0.20, 0.20, 0.00])
Gap: 0.861 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.12, 0.12]) -> (s=0, k=10, n=68, [0.00, 0.12, 0.38])
<class 'tuple'>
Gap: 0.861 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.12, 0.12]) -> (s=0, k=10, n=68, [0.00, 0.12, 0.38])
Gap: 0.981 eV
Transition (v -> c):
  (s=0, k=13, n=71, [0.00, 0.00, 0.33]) -> (s=0, k=13, n=72, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.981 eV
Transition (v -> c):
  (s=0, k=13, n=71, [0.00, 0.00, 0.33]) -> (s=0, k=13, n=72, [0.00, 0.00, 0.33])
Gap: 1.181 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, -0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, -0.00])
<class 'tuple'>
Gap: 1.181 eV
Transition (v -> c):
  (s=0, k=9, n=63, [-0.12, 0.40, -0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.871 eV
Transition (v -> c):
  (s=0, k=15, n=51, [-0.00, 0.38, 0.38]) -> (s=0, k=10, n=52, [0.00, 0.12, 0.38])
<class 'tuple'>
Gap: 0.871 eV
Transition (v -> c):
  (s=0, k=15, n=51, [-0.00, 0.38, 0.38]) -> (s=0, k=10, n=52, [0.00, 0.12, 0.38])
Gap: 0.461 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.00, 0.00]) -> (s=0, k=2, n=68, [0.40, 0.00, 0.00])
<class 'tuple'>
Gap: 0.461 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.40, 0.00, 0.00]) -> (s=0, k=2, n=68, [0.40, 0.00, 0.00])
Gap: 0.191 eV
Transition (v -> c):
  (s=0, k=13, n=79, [0.00, -0.00, 0.33]) -> (s=0, k=3, n=80, [0.00, 0.20, 0.00])
<class 'tuple'>
Gap: 0.191 eV
Transition (v -> c):
  (s=0, k=13, n=79, [0.00, -0.00, 0.33]) -> (s=0, k=3, n=80, [0.00, 0.20, 0.00])
Gap: 0.361 eV
Transition (v -> c):
  (s=0, k=1, n=71, [0.20, 0.12, 0.00]) -> (s=0, k=17, n=72, [0.40, 0.38, 0.33])
<class 'tuple'>
Gap: 0.361 eV
Transition (v -> c):
  (s=0, k=1, n=71, [0.20, 0.12, 0.00]) -> (s=0, k=17, n=72, [0.40, 0.38, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.252 eV
Transition (v -> c):
  (s=0, k=3, n=53, [-0.40, 0.12, 0.12]) -> (s=0, k=10, n=54, [-0.00, 0.12, 0.38])
<class 'tuple'>
Gap: 0.252 eV
Transition (v -> c):
  (s=0, k=3, n=53, [-0.40, 0.12, 0.12]) -> (s=0, k=10, n=54, [-0.00, 0.12, 0.38])
Gap: 0.757 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, 0.12, 0.00]) -> (s=0, k=14, n=88, [-0.20, 0.12, 0.33])
<class 'tuple'>
Gap: 0.757 eV
Transition (v -> c):
  (s=0, k=2, n=87, [0.40, 0.12, 0.00]) -> (s=0, k=14, n=88, [-0.20, 0.12, 0.33])
Gap: 0.601 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.601 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
Gap: 0.477 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=72, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 0.477 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=72, [-0.40, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 1.128 eV
Transition (v -> c):
  (s=0, k=8, n=59, [0.42, 0.38, 0.00]) -> (s=0, k=6, n=60, [0.08, 0.38, 0.00])
<class 'tuple'>
Gap: 1.128 eV
Transition (v -> c):
  (s=0, k=8, n=59, [0.42, 0.38, 0.00]) -> (s=0, k=6, n=60, [0.08, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.610 eV
Transition (v -> c):
  (s=0, k=0, n=55, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=56, [0.40, -0.00, 0.00])
<class 'tuple'>
Gap: 0.610 eV
Transition (v -> c):
  (s=0, k=0, n=55, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=56, [0.40, -0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.494 eV
Transition (v -> c):
  (s=0, k=0, n=47, [0.00, 0.00, 0.00]) -> (s=0, k=13, n=48, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.494 eV
Transition (v -> c):
  (s=0, k=0, n=47, [0.00, 0.00, 0.00]) -> (s=0, k=13, n=48, [0.00, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.003 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.08, 0.12, 0.00]) -> (s=0, k=5, n=68, [-0.08, 0.12, 0.00])
<class 'tuple'>
Gap: 0.003 eV
Transition (v -> c):
  (s=0, k=5, n=67, [-0.08, 0.12, 0.00]) -> (s=0, k=5, n=68, [-0.08, 0.12, 0.00])
Gap: 1.212 eV
Transition (v -> c):
  (s=0, k=0, n=51, [0.00, 0.00, 0.00]) -> (s=0, k=13, n=52, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 1.212 eV
Transition (v -> c):
  (s=0, k=0, n=51, [0.00, 0.00, 0.00]) -> (s=0, k=13, n=52, [0.00, 0.00, 0.33])
Gap: 0.038 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.038 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.990 eV
Transition (v -> c):
  (s=0, k=8, n=63, [-0.38, 0.40, -0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, -0.00])
<class 'tuple'>
Gap: 0.990 eV
Transition (v -> c):
  (s=0, k=8, n=63, [-0.38, 0.40, -0.00]) -> (s=0, k=8, n=64, [-0.38, 0.40, -0.00])
Gap: 0.173 eV
Transition (v -> c):
  (s=0, k=13, n=65, [-0.00, -0.00, 0.33]) -> (s=0, k=16, n=66, [0.00, 0.20, 0.33])
<class 'tuple'>
Gap: 0.173 eV
Transition (v -> c):
  (s=0, k=13, n=65, [-0.00, -0.00, 0.33]) -> (s=0, k=16, n=66, [0.00, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.224 eV
Transition (v -> c):
  (s=0, k=0, n=47, [-0.00, 0.12, 0.00]) -> (s=0, k=2, n=48, [0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 1.224 eV
Transition (v -> c):
  (s=0, k=0, n=47, [-0.00, 0.12, 0.00]) -> (s=0, k=2, n=48, [0.40, 0.12, 0.00])
Gap: 0.076 eV
Transition (v -> c):
  (s=0, k=23, n=69, [-0.08, 0.38, 0.38]) -> (s=0, k=2, n=70, [0.42, 0.12, 0.12])
<class 'tuple'>
Gap: 0.076 eV
Transition (v -> c):
  (s=0, k=23, n=69, [-0.08, 0.38, 0.38]) -> (s=0, k=2, n=70, [0.42, 0.12, 0.12])
Gap: 0.284 eV
Transition (v -> c):
  (s=0, k=12, n=63, [-0.20, 0.40, 0.12]) -> (s=0, k=19, n=64, [-0.40, 0.20, 0.38])
<class 'tuple'>
Gap: 0.284 eV
Transition (v -> c):
  (s=0, k=12, n=63, [-0.20, 0.40, 0.12]) -> (s=0, k=19, n=64, [-0.40, 0.20, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.466 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.42, 0.12, 0.00]) -> (s=0, k=7, n=68, [0.25, 0.38, 0.00])
<class 'tuple'>
Gap: 0.466 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.42, 0.12, 0.00]) -> (s=0, k=7, n=68, [0.25, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.030 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=72, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.030 eV
Transition (v -> c):
  (s=0, k=0, n=71, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=72, [0.00, 0.00, 0.00])
Gap: 0.964 eV
Transition (v -> c):
  (s=0, k=15, n=71, [0.00, 0.38, 0.33]) -> (s=0, k=3, n=72, [-0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 0.964 eV
Transition (v -> c):
  (s=0, k=15, n=71, [0.00, 0.38, 0.33]) -> (s=0, k=3, n=72, [-0.40, 0.12, 0.00])
Gap: 0.425 eV
Transition (v -> c):
  (s=0, k=15, n=75, [0.00, 0.38, 0.33]) -> (s=0, k=3, n=76, [-0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 0.425 eV
Transition (v -> c):
  (s=0, k=15, n=75, [0.00, 0.38, 0.33]) -> (s=0, k=3, n=76, [-0.40, 0.12, 0.00])
Gap: 0.361 eV
Transition (v -> c):
  (s=0, k=9, n=67, [-0.42, 0.38, 0.00]) -> (s=0, k=10, n=68, [-0.25, 0.38, -0.00])
<class 'tuple'>
Gap: 0.361 eV
Transition (v -> c):
  (s=0, k=9, n=67, [-0.42, 0.38, 0.00]) -> (s=0, k=10, n=68, [-0.25, 0.38, -0.00])
Gap: 0.138 eV
Transition (v -> c):
  (s=0, k=10, n=65, [0.40, 0.40, 0.00]) -> (s=0, k=1, n=66, [0.20, 0.00, 0.00])
<class 'tuple'>
Gap: 0.138 eV
Transition (v -> c):
  (s=0, k=10, n=65, [0.40, 0.40, 0.00]) -> (s=0, k=1, n=66, [0.20, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.595 eV
Transition (v -> c):
  (s=0, k=32, n=63, [0.43, 0.40, 0.33]) -> (s=0, k=21, n=64, [0.43, 0.00, 0.33])
<class 'tuple'>
Gap: 0.595 eV
Transition (v -> c):
  (s=0, k=32, n=63, [0.43, 0.40, 0.33]) -> (s=0, k=21, n=64, [0.43, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.405 eV
Transition (v -> c):
  (s=0, k=1, n=69, [0.20, -0.00, -0.00]) -> (s=0, k=19, n=70, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 0.405 eV
Transition (v -> c):
  (s=0, k=1, n=69, [0.20, -0.00, -0.00]) -> (s=0, k=19, n=70, [-0.40, 0.20, 0.33])
Gap: 0.710 eV
Transition (v -> c):
  (s=0, k=15, n=47, [0.08, 0.00, 0.33]) -> (s=0, k=18, n=48, [0.08, 0.20, 0.33])
<class 'tuple'>
Gap: 0.710 eV
Transition (v -> c):
  (s=0, k=15, n=47, [0.08, 0.00, 0.33]) -> (s=0, k=18, n=48, [0.08, 0.20, 0.33])
Gap: 0.273 eV
Transition (v -> c):
  (s=0, k=2, n=69, [0.40, 0.00, 0.00]) -> (s=0, k=15, n=70, [0.40, 0.00, 0.33])
<class 'tuple'>
Gap: 0.273 eV
Transition (v -> c):
  (s=0, k=2, n=69, [0.40, 0.00, 0.00]) -> (s=0, k=15, n=70, [0.40, 0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.405 eV
Transition (v -> c):
  (s=0, k=10, n=75, [0.40, 0.40, -0.00]) -> (s=0, k=0, n=76, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.405 eV
Transition (v -> c):
  (s=0, k=10, n=75, [0.40, 0.40, -0.00]) -> (s=0, k=0, n=76, [0.00, 0.00, 0.00])
Gap: 0.815 eV
Transition (v -> c):
  (s=0, k=12, n=55, [-0.20, 0.40, 0.00]) -> (s=0, k=11, n=56, [-0.40, 0.40, -0.00])
<class 'tuple'>
Gap: 0.815 eV
Transition (v -> c):
  (s=0, k=12, n=55, [-0.20, 0.40, 0.00]) -> (s=0, k=11, n=56, [-0.40, 0.40, -0.00])
Gap: 1.145 eV
Transition (v -> c):
  (s=0, k=0, n=59, [0.00, 0.08, -0.00]) -> (s=0, k=13, n=60, [-0.40, 0.42, -0.00])
<class 'tuple'>
Gap: 1.145 eV
Transition (v -> c):
  (s=0, k=0, n=59, [0.00, 0.08, -0.00]) -> (s=0, k=13, n=60, [-0.40, 0.42, -0.00])
Gap: 0.156 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.08, 0.00, 0.00]) -> (s=0, k=0, n=64, [0.08, 0.00, 0.00])
<class 'tuple'>
Gap: 0.156 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.08, 0.00, 0.00]) -> (s=0, k=0, n=64, [0.08, 0.00, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.701 eV
Transition (v -> c):
  (s=0, k=6, n=51, [0.20, 0.38, 0.00]) -> (s=0, k=7, n=52, [0.40, 0.38, -0.00])
<class 'tuple'>
Gap: 0.701 eV
Transition (v -> c):
  (s=0, k=6, n=51, [0.20, 0.38, 0.00]) -> (s=0, k=7, n=52, [0.40, 0.38, -0.00])
Gap: 0.081 eV
Transition (v -> c):
  (s=0, k=23, n=73, [0.40, 0.40, 0.33]) -> (s=0, k=4, n=74, [0.20, 0.20, 0.00])
<class 'tuple'>
Gap: 0.081 eV
Transition (v -> c):
  (s=0, k=23, n=73, [0.40, 0.40, 0.33]) -> (s=0, k=4, n=74, [0.20, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.321 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.12, 0.00]) -> (s=0, k=3, n=64, [-0.40, 0.12, -0.00])
<class 'tuple'>
Gap: 1.321 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.12, 0.00]) -> (s=0, k=3, n=64, [-0.40, 0.12, -0.00])
Gap: 0.169 eV
Transition (v -> c):
  (s=0, k=15, n=65, [0.08, 0.00, 0.33]) -> (s=0, k=8, n=66, [-0.08, 0.20, 0.00])
<class 'tuple'>
Gap: 0.169 eV
Transition (v -> c):
  (s=0, k=15, n=65, [0.08, 0.00, 0.33]) -> (s=0, k=8, n=66, [-0.08, 0.20, 0.00])
Gap: 0.850 eV
Transition (v -> c):
  (s=0, k=5, n=79, [-0.08, 0.12, 0.00]) -> (s=0, k=5, n=80, [-0.08, 0.12, 0.00])
<class 'tuple'>
Gap: 0.850 eV
Transition (v -> c):
  (s=0, k=5, n=79, [-0.08, 0.12, 0.00]) -> (s=0, k=5, n=80, [-0.08, 0.12, 0.00])
Gap: 0.932 eV
Transition (v -> c):
  (s=0, k=25, n=71, [0.25, 0.40, 0.33]) -> (s=0, k=21, n=72, [-0.42, 0.20, 0.33])
<class 'tuple'>
Gap: 0.932 eV
Transition (v -> c):
  (s=0, k=25, n=71, [0.25, 0.40, 0.33]) -> (s=0, k=21, n=72, [-0.42, 0.20, 0.33])
Gap: 0.384 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=18, n=68, [-0.00, -0.00, 0.33])
<class 'tuple'>
Gap: 0.384 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=18, n=68, [-0.00, -0.00, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.194 eV
Transition (v -> c):
  (s=0, k=13, n=67, [0.00, -0.00, 0.33]) -> (s=0, k=13, n=68, [0.00, -0.00, 0.33])
<class 'tuple'>
Gap: 0.194 eV
Transition (v -> c):
  (s=0, k=13, n=67, [0.00, -0.00, 0.33]) -> (s=0, k=13, n=68, [0.00, -0.00, 0.33])
Gap: 0.411 eV
Transition (v -> c):
  (s=0, k=16, n=67, [0.00, 0.20, 0.33]) -> (s=0, k=1, n=68, [0.20, 0.00, 0.00])
<class 'tuple'>
Gap: 0.411 eV
Transition (v -> c):
  (s=0, k=16, n=67, [0.00, 0.20, 0.33]) -> (s=0, k=1, n=68, [0.20, 0.00, 0.00])
Gap: 0.775 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=16, n=68, [0.00, 0.20, 0.33])
<class 'tuple'>
Gap: 0.775 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=16, n=68, [0.00, 0.20, 0.33])
Gap: 1.688 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 1.688 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.00, 0.00])
Gap: 0.672 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.12, 0.00, 0.00]) -> (s=0, k=6, n=88, [0.12, 0.40, -0.00])
<class 'tuple'>
Gap: 0.672 eV
Transition (v -> c):
  (s=0, k=0, n=87, [0.12, 0.00, 0.00]) -> (s=0, k=6, n=88, [0.12, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.861 eV
Transition (v -> c):
  (s=0, k=0, n=63, [-0.00, 0.08, -0.00]) -> (s=0, k=8, n=64, [-0.40, 0.25, 0.00])
<class 'tuple'>
Gap: 0.861 eV
Transition (v -> c):
  (s=0, k=0, n=63, [-0.00, 0.08, -0.00]) -> (s=0, k=8, n=64, [-0.40, 0.25, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.100 eV
Transition (v -> c):
  (s=0, k=6, n=63, [0.20, 0.38, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.12, 0.00])
<class 'tuple'>
Gap: 1.100 eV
Transition (v -> c):
  (s=0, k=6, n=63, [0.20, 0.38, 0.00]) -> (s=0, k=0, n=64, [0.00, 0.12, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.419 eV
Transition (v -> c):
  (s=0, k=13, n=67, [-0.00, -0.00, 0.33]) -> (s=0, k=22, n=68, [0.20, 0.40, 0.33])
<class 'tuple'>
Gap: 0.419 eV
Transition (v -> c):
  (s=0, k=13, n=67, [-0.00, -0.00, 0.33]) -> (s=0, k=22, n=68, [0.20, 0.40, 0.33])
Gap: 0.800 eV
Transition (v -> c):
  (s=0, k=0, n=59, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=60, [-0.40, 0.20, 0.33])
<class 'tuple'>
Gap: 0.800 eV
Transition (v -> c):
  (s=0, k=0, n=59, [0.00, 0.00, 0.00]) -> (s=0, k=19, n=60, [-0.40, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 0.745 eV
Transition (v -> c):
  (s=0, k=2, n=63, [0.40, -0.00, -0.00]) -> (s=0, k=11, n=64, [-0.40, 0.40, 0.00])
<class 'tuple'>
Gap: 0.745 eV
Transition (v -> c):
  (s=0, k=2, n=63, [0.40, -0.00, -0.00]) -> (s=0, k=11, n=64, [-0.40, 0.40, 0.00])
Gap: 1.068 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.42, 0.00, 0.12]) -> (s=0, k=13, n=68, [0.25, 0.33, 0.38])
<class 'tuple'>
Gap: 1.068 eV
Transition (v -> c):
  (s=0, k=2, n=67, [0.42, 0.00, 0.12]) -> (s=0, k=13, n=68, [0.25, 0.33, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.808 eV
Transition (v -> c):
  (s=0, k=5, n=59, [-0.08, 0.12, 0.00]) -> (s=0, k=5, n=60, [-0.08, 0.12, 0.00])
<class 'tuple'>
Gap: 0.808 eV
Transition (v -> c):
  (s=0, k=5, n=59, [-0.08, 0.12, 0.00]) -> (s=0, k=5, n=60, [-0.08, 0.12, 0.00])
Gap: 0.753 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=5, n=68, [0.40, 0.20, -0.00])
<class 'tuple'>
Gap: 0.753 eV
Transition (v -> c):
  (s=0, k=0, n=67, [0.00, 0.00, 0.00]) -> (s=0, k=5, n=68, [0.40, 0.20, -0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.673 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.12, 0.00, -0.00]) -> (s=0, k=0, n=64, [0.12, 0.00, -0.00])
<class 'tuple'>
Gap: 1.673 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.12, 0.00, -0.00]) -> (s=0, k=0, n=64, [0.12, 0.00, -0.00])
Gap: 1.381 eV
Transition (v -> c):
  (s=0, k=5, n=67, [0.42, 0.20, -0.00]) -> (s=0, k=10, n=68, [0.25, 0.40, 0.00])
<class 'tuple'>
Gap: 1.381 eV
Transition (v -> c):
  (s=0, k=5, n=67, [0.42, 0.20, -0.00]) -> (s=0, k=10, n=68, [0.25, 0.40, 0.00])
Gap: 0.016 eV
Transition (v -> c):
  (s=0, k=6, n=55, [0.20, 0.38, 0.12]) -> (s=0, k=3, n=56, [-0.40, 0.12, 0.12])
<class 'tuple'>
Gap: 0.016 eV
Transition (v -> c):
  (s=0, k=6, n=55, [0.20, 0.38, 0.12]) -> (s=0, k=3, n=56, [-0.40, 0.12, 0.12])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.169 eV
Transition (v -> c):
  (s=0, k=1, n=71, [0.25, 0.12, -0.00]) -> (s=0, k=3, n=72, [-0.42, 0.12, -0.00])
<class 'tuple'>
Gap: 0.169 eV
Transition (v -> c):
  (s=0, k=1, n=71, [0.25, 0.12, -0.00]) -> (s=0, k=3, n=72, [-0.42, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 1.394 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=64, [0.40, -0.00, 0.00])
<class 'tuple'>
Gap: 1.394 eV
Transition (v -> c):
  (s=0, k=0, n=63, [0.00, 0.00, 0.00]) -> (s=0, k=2, n=64, [0.40, -0.00, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 0.750 eV
Transition (v -> c):
  (s=0, k=5, n=83, [-0.08, 0.12, 0.00]) -> (s=0, k=5, n=84, [-0.08, 0.12, 0.00])
<class 'tuple'>
Gap: 0.750 eV
Transition (v -> c):
  (s=0, k=5, n=83, [-0.08, 0.12, 0.00]) -> (s=0, k=5, n=84, [-0.08, 0.12, 0.00])
Gap: 0.808 eV
Transition (v -> c):
  (s=0, k=17, n=51, [0.20, 0.20, 0.33]) -> (s=0, k=20, n=52, [-0.20, 0.20, 0.33])
<class 'tuple'>
Gap: 0.808 eV
Transition (v -> c):
  (s=0, k=17, n=51, [0.20, 0.20, 0.33]) -> (s=0, k=20, n=52, [-0.20, 0.20, 0.33])
No gap
<class 'tuple'>
No gap
Gap: 1.271 eV
Transition (v -> c):
  (s=0, k=8, n=63, [-0.40, 0.38, -0.00]) -> (s=0, k=0, n=64, [0.00, 0.12, -0.00])
<class 'tuple'>
Gap: 1.271 eV
Transition (v -> c):
  (s=0, k=8, n=63, [-0.40, 0.38, -0.00]) -> (s=0, k=0, n=64, [0.00, 0.12, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.395 eV
Transition (v -> c):
  (s=0, k=1, n=69, [0.20, 0.00, 0.00]) -> (s=0, k=2, n=70, [0.40, 0.00, 0.00])
<class 'tuple'>
Gap: 0.395 eV
Transition (v -> c):
  (s=0, k=1, n=69, [0.20, 0.00, 0.00]) -> (s=0, k=2, n=70, [0.40, 0.00, 0.00])
Gap: 0.886 eV
Transition (v -> c):
  (s=0, k=7, n=71, [0.38, 0.40, 0.00]) -> (s=0, k=5, n=72, [-0.12, 0.20, 0.00])
<class 'tuple'>
Gap: 0.886 eV
Transition (v -> c):
  (s=0, k=7, n=71, [0.38, 0.40, 0.00]) -> (s=0, k=5, n=72, [-0.12, 0.20, 0.00])
No gap
<class 'tuple'>
No gap
No gap
<class 'tuple'>
No gap
Gap: 1.196 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.20, 0.00, 0.12]) -> (s=0, k=15, n=68, [0.40, 0.00, 0.38])
<class 'tuple'>
Gap: 1.196 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.20, 0.00, 0.12]) -> (s=0, k=15, n=68, [0.40, 0.00, 0.38])
No gap
<class 'tuple'>
No gap
Gap: 0.036 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=20, n=80, [-0.20, 0.20, 0.33])
<class 'tuple'>
Gap: 0.036 eV
Transition (v -> c):
  (s=0, k=0, n=79, [0.00, 0.00, 0.00]) -> (s=0, k=20, n=80, [-0.20, 0.20, 0.33])
Gap: 1.191 eV
Transition (v -> c):
  (s=0, k=3, n=59, [0.38, 0.20, 0.00]) -> (s=0, k=2, n=60, [0.12, 0.20, 0.00])
<class 'tuple'>
Gap: 1.191 eV
Transition (v -> c):
  (s=0, k=3, n=59, [0.38, 0.20, 0.00]) -> (s=0, k=2, n=60, [0.12, 0.20, 0.00])
Gap: 1.647 eV
Transition (v -> c):
  (s=0, k=12, n=63, [-0.20, 0.40, -0.00]) -> (s=0, k=8, n=64, [0.00, 0.40, 0.00])
<class 'tuple'>
Gap: 1.647 eV
Transition (v -> c):
  (s=0, k=12, n=63, [-0.20, 0.40, -0.00]) -> (s=0, k=8, n=64, [0.00, 0.40, 0.00])
Gap: 0.514 eV
Transition (v -> c):
  (s=0, k=13, n=63, [0.00, 0.00, 0.33]) -> (s=0, k=22, n=64, [0.20, 0.40, 0.33])
<class 'tuple'>
Gap: 0.514 eV
Transition (v -> c):
  (s=0, k=13, n=63, [0.00, 0.00, 0.33]) -> (s=0, k=22, n=64, [0.20, 0.40, 0.33])
Gap: 0.321 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.12, 0.00, 0.12]) -> (s=0, k=5, n=84, [-0.12, 0.20, 0.12])
<class 'tuple'>
Gap: 0.321 eV
Transition (v -> c):
  (s=0, k=0, n=83, [0.12, 0.00, 0.12]) -> (s=0, k=5, n=84, [-0.12, 0.20, 0.12])
Gap: 0.067 eV
Transition (v -> c):
  (s=0, k=10, n=53, [0.40, 0.40, 0.00]) -> (s=0, k=7, n=54, [-0.20, 0.20, -0.00])
<class 'tuple'>
Gap: 0.067 eV
Transition (v -> c):
  (s=0, k=10, n=53, [0.40, 0.40, 0.00]) -> (s=0, k=7, n=54, [-0.20, 0.20, -0.00])
Gap: 0.872 eV
Transition (v -> c):
  (s=0, k=15, n=51, [0.00, 0.38, 0.33]) -> (s=0, k=8, n=52, [-0.40, 0.38, 0.00])
<class 'tuple'>
Gap: 0.872 eV
Transition (v -> c):
  (s=0, k=15, n=51, [0.00, 0.38, 0.33]) -> (s=0, k=8, n=52, [-0.40, 0.38, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.281 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.20, -0.00, -0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
<class 'tuple'>
Gap: 0.281 eV
Transition (v -> c):
  (s=0, k=1, n=67, [0.20, -0.00, -0.00]) -> (s=0, k=0, n=68, [0.00, 0.00, 0.00])
Gap: 0.533 eV
Transition (v -> c):
  (s=0, k=12, n=55, [0.40, 0.12, 0.33]) -> (s=0, k=0, n=56, [0.00, 0.12, 0.00])
<class 'tuple'>
Gap: 0.533 eV
Transition (v -> c):
  (s=0, k=12, n=55, [0.40, 0.12, 0.33]) -> (s=0, k=0, n=56, [0.00, 0.12, 0.00])
Gap: 0.580 eV
Transition (v -> c):
  (s=0, k=9, n=87, [-0.20, 0.38, -0.00]) -> (s=0, k=11, n=88, [0.20, 0.12, 0.33])
<class 'tuple'>
Gap: 0.580 eV
Transition (v -> c):
  (s=0, k=9, n=87, [-0.20, 0.38, -0.00]) -> (s=0, k=11, n=88, [0.20, 0.12, 0.33])
Gap: 0.682 eV
Transition (v -> c):
  (s=0, k=0, n=47, [0.08, 0.00, 0.00]) -> (s=0, k=14, n=48, [-0.08, 0.40, -0.00])
<class 'tuple'>
Gap: 0.682 eV
Transition (v -> c):
  (s=0, k=0, n=47, [0.08, 0.00, 0.00]) -> (s=0, k=14, n=48, [-0.08, 0.40, -0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.355 eV
Transition (v -> c):
  (s=0, k=13, n=79, [0.00, 0.00, 0.33]) -> (s=0, k=13, n=80, [0.00, 0.00, 0.33])
<class 'tuple'>
Gap: 0.355 eV
Transition (v -> c):
  (s=0, k=13, n=79, [0.00, 0.00, 0.33]) -> (s=0, k=13, n=80, [0.00, 0.00, 0.33])
Gap: 0.065 eV
Transition (v -> c):
  (s=0, k=2, n=69, [0.40, 0.00, 0.00]) -> (s=0, k=3, n=70, [0.00, 0.20, -0.00])
<class 'tuple'>
Gap: 0.065 eV
Transition (v -> c):
  (s=0, k=2, n=69, [0.40, 0.00, 0.00]) -> (s=0, k=3, n=70, [0.00, 0.20, -0.00])
Gap: 0.035 eV
Transition (v -> c):
  (s=0, k=3, n=75, [0.43, -0.00, 0.25]) -> (s=0, k=0, n=76, [0.00, 0.00, 0.25])
<class 'tuple'>
Gap: 0.035 eV
Transition (v -> c):
  (s=0, k=3, n=75, [0.43, -0.00, 0.25]) -> (s=0, k=0, n=76, [0.00, 0.00, 0.25])
Gap: 0.116 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, -0.00, 0.33]) -> (s=0, k=3, n=84, [-0.00, 0.20, 0.00])
<class 'tuple'>
Gap: 0.116 eV
Transition (v -> c):
  (s=0, k=13, n=83, [0.00, -0.00, 0.33]) -> (s=0, k=3, n=84, [-0.00, 0.20, 0.00])
Gap: 0.968 eV
Transition (v -> c):
  (s=0, k=13, n=67, [0.00, 0.00, 0.33]) -> (s=0, k=12, n=68, [-0.20, 0.40, 0.00])
<class 'tuple'>
Gap: 0.968 eV
Transition (v -> c):
  (s=0, k=13, n=67, [0.00, 0.00, 0.33]) -> (s=0, k=12, n=68, [-0.20, 0.40, 0.00])
No gap
<class 'tuple'>
No gap
Gap: 0.513 eV
Transition (v -> c):
  (s=0, k=13, n=87, [-0.40, 0.42, -0.00]) -> (s=0, k=2, n=88, [0.40, 0.08, -0.00])
<class 'tuple'>
Gap: 0.513 eV
Transition (v -> c):
  (s=0, k=13, n=87, [-0.40, 0.42, -0.00]) -> (s=0, k=2, n=88, [0.40, 0.08, -0.00])
Gap: 0.825 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.20, 0.12, -0.00]) -> (s=0, k=0, n=88, [-0.00, 0.12, -0.00])
<class 'tuple'>
Gap: 0.825 eV
Transition (v -> c):
  (s=0, k=1, n=87, [0.20, 0.12, -0.00]) -> (s=0, k=0, n=88, [-0.00, 0.12, -0.00])
Gap: 1.351 eV
Transition (v -> c):
  (s=0, k=8, n=67, [-0.40, 0.38, 0.00]) -> (s=0, k=3, n=68, [-0.40, 0.12, 0.00])
<class 'tuple'>
Gap: 1.351 eV
Transition (v -> c):
  (s=0, k=8, n=67, [-0.40, 0.38, 0.00]) -> (s=0, k=3, n=68, [-0.40, 0.12, 0.00])
Gap: 0.266 eV
Transition (v -> c):
  (s=0, k=1, n=55, [0.20, 0.12, 0.00]) -> (s=0, k=12, n=56, [0.40, 0.12, 0.33])
<class 'tuple'>
Gap: 0.266 eV
Transition (v -> c):
  (s=0, k=1, n=55, [0.20, 0.12, 0.00]) -> (s=0, k=12, n=56, [0.40, 0.12, 0.33])
