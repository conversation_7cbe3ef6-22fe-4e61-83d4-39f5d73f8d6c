import numpy as np

def verify_matrix_inverse():
    """Verify the matrix inverse computation and solution from Question 9."""
    print("Matrix Inverse Verification (Problem 9)")
    print("---------------------------------------")
    
    # Original matrix A from Problem 6
    A = np.array([
        [10, 2, -1],
        [-3, -6, 2],
        [1, 1, 5]
    ])
    
    # Our computed inverse from Problem 9
    A_inv_computed = np.array([
        [0.110727, 0.038062, 0.006920],
        [-0.058824, -0.176471, 0.058824],
        [-0.010381, 0.027682, 0.186851]
    ])
    
    # Right-hand side vector b
    b = np.array([27, -61.5, -21.5])
    
    # Our solution computed using the inverse
    x_computed = np.array([0.5, 8.0, -6.0])
    
    print("\nVerification steps:")
    print("1. Check if AA⁻¹ = I")
    print("-" * 40)
    
    # Compute AA⁻¹
    AA_inv = np.dot(A, A_inv_computed)
    I = np.eye(3)
    
    print("AA⁻¹ = ")
    print(AA_inv)
    print("\nDifference from identity matrix:")
    print(np.abs(AA_inv - I))
    
    # Check if AA⁻¹ is close to identity matrix
    is_inverse_correct = np.allclose(AA_inv, I, rtol=1e-10, atol=1e-10)
    print(f"\nInverse is correct: {is_inverse_correct}")
    
    print("\n2. Verify solution x = A⁻¹b")
    print("-" * 40)
    
    # Compute solution using inverse
    x_using_inverse = np.dot(A_inv_computed, b)
    print("Solution using inverse:")
    for i, xi in enumerate(x_using_inverse):
        print(f"x{i+1} = {xi:10.6f}")
    
    print("\nDifference from our computed solution:")
    diff = np.abs(x_using_inverse - x_computed)
    for i, d in enumerate(diff):
        print(f"x{i+1}: {d:10.2e}")
    
    # Compare with numpy's direct solution
    x_numpy = np.linalg.solve(A, b)
    print("\n3. Compare with numpy's solution")
    print("-" * 40)
    print(f"{'Variable':10} {'Our Value':>12} {'Numpy':>12} {'Difference':>12}")
    print("-" * 46)
    for i, (our_x, np_x) in enumerate(zip(x_computed, x_numpy)):
        diff = abs(our_x - np_x)
        print(f"x{i+1}:{' ':9} {our_x:12.6f} {np_x:12.6f} {diff:12.2e}")
    
    # Verify original equations
    residuals = np.dot(A, x_computed) - b
    print("\n4. Verify original equations")
    print("-" * 40)
    print(f"{'Equation':10} {'Left Side':>12} {'Right Side':>12} {'Residual':>12}")
    print("-" * 46)
    for i in range(3):
        left_side = np.dot(A[i], x_computed)
        print(f"{i+1:d}:{' ':9} {left_side:12.6f} {b[i]:12.6f} {residuals[i]:12.2e}")
    
    return is_inverse_correct and np.allclose(x_computed, x_numpy, rtol=1e-10, atol=1e-10)

if __name__ == "__main__":
    verify_matrix_inverse()
