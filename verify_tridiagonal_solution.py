import numpy as np

def verify_tridiagonal_solution():
    """Verify the solution for the tridiagonal system in Question 4."""
    # Coefficient matrix
    A = np.array([
        [0.8, -0.4, 0],
        [-0.4, 0.8, -0.4],
        [0, -0.4, 0.8]
    ])
    
    # Right-hand side vector
    b = np.array([41, 25, 105])
    
    # Our solution from elimination method
    x_given = np.array([173.75, 245.00, 253.75])
    
    print("Tridiagonal System Solution Verification")
    print("---------------------------------------")
    print(f"\nOur solution:")
    print(f"x₁ = {x_given[0]:.2f}")
    print(f"x₂ = {x_given[1]:.2f}")
    print(f"x₃ = {x_given[2]:.2f}\n")
    
    # Calculate residuals (Ax - b)
    residuals = np.dot(A, x_given) - b
    
    print("Equation Verification:")
    print(f"{'Equation':10} {'Left Side':>12} {'Right Side':>12} {'Residual':>12}")
    print("-" * 46)
    
    # Verify each equation
    equations = [
        (0.8, -0.4, 0.0, 41),  # Equation 1
        (-0.4, 0.8, -0.4, 25), # Equation 2
        (0.0, -0.4, 0.8, 105)  # Equation 3
    ]
    
    for i, (a1, a2, a3, rhs) in enumerate(equations):
        left_side = a1*x_given[0] + a2*x_given[1] + a3*x_given[2]
        print(f"{i+1:d}:{' ':9} {left_side:12.6f} {rhs:12.6f} {residuals[i]:12.2e}")
    
    # Check solution accuracy
    is_accurate = np.allclose(np.dot(A, x_given), b, rtol=1e-10, atol=1e-10)
    print(f"\nSolution is accurate: {is_accurate}")
    
    # Compare with numpy's solution
    x_numpy = np.linalg.solve(A, b)
    print("\nComparison with numpy's solution:")
    print(f"{'Variable':10} {'Our Value':>12} {'Numpy':>12} {'Difference':>12}")
    print("-" * 46)
    for i, (our_x, np_x) in enumerate(zip(x_given, x_numpy)):
        diff = abs(our_x - np_x)
        print(f"x{i+1:d}:{' ':9} {our_x:12.6f} {np_x:12.6f} {diff:12.2e}")
    
    return is_accurate and np.allclose(x_given, x_numpy, rtol=1e-10, atol=1e-10)

if __name__ == "__main__":
    verify_tridiagonal_solution()
