from math import *
from matplotlib.pyplot import *
import pandas as pd
class system:
    timestep=0.001
    def __init__(self,mass,posx,posy,vel,angle):
        self.mass=mass
        self.posx=posx
        self.posy=posy
        self.velx=vel*cos(radians(angle))
        self.vely=vel*sin(radians(angle))
        self.angle=angle
        self.acceleration_due_to_gravity=-9.8
    def posxupdate(self):        
        self.posx=self.posx+self.velx*system.timestep
       
    def velyupdate(self):
        self.vely=self.vely+self.acceleration_due_to_gravity*system.timestep
    def posyupdate(self):
        self.posy=self.posy+self.vely*system.timestep
    

def runner(ball):
    time=0
    timeset=[]
    posset=[[],[]] 
    while True:
        time+=system.timestep
        ball.posxupdate()
        ball.posyupdate()
        ball.velyupdate()
        if ball.posy<0:
            print('da it on aayath aano')
            break
        timeset.append(time)
        posset[0].append(ball.posx)
        posset[1].append(ball.posy)
    return timeset,posset

data=pd.DataFrame()
def saver(t,y):
    global data
    new_data=pd.DataFrame({'time':t,
              'Height':y})
    data=pd.concat([data,new_data],axis=1)
    return data
ball=system(1,0,0,3.67,0)
print(runner(ball))