from numpy import*
from math import *
from matplotlib.pyplot import*

class system:
    timestep=0.001
    magnetic_field=array([3,-4,sqrt(11)])
    electric_field=array([0,0,0])
    def __init__(self,mass,charge,position,velocity):
        self.mass=mass
        self.charge=charge
        self.position=array(position)
        self.velocity=array(velocity)
    def coulomb_force(self):
        self.electric_force=self.charge*self.electric_field
    def lorentz_force(self):
        self.magnetic_force=self.charge*cross(self.velocity,self.magnetic_field)
    def velocity_update(self):
        self.velocity=self.velocity+(self.magnetic_force+self.electric_force)/self.mass*self.timestep
    def position_update(self):
        self.position=self.position+self.velocity*self.timestep

electron=system(1,1,[0,0,0],[0,0,1])
positions=[list(electron.position)]
for i in range(5*10**5):
    electron.coulomb_force()
    electron.lorentz_force()
    electron.velocity_update()
    electron.position_update()
    positions.append(list(electron.position))
positions=array(positions)
fig=figure(figsize=(10,10))
ax = axes( projection='3d')
ax.plot(positions[:,0],positions[:,1],positions[:,2], label='Charge moving inside an electromagnetic field')
legend()
show()
savefig('path.png')
print(positions)
