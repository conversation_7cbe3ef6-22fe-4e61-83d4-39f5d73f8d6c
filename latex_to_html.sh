#\!/bin/bash
LATEX_FILE="/home/<USER>/Coding/sample_latex_document.tex"
HTML_FILE="/home/<USER>/Coding/sample_latex_document.html"

echo '<\!DOCTYPE html>
<html>
<head>
<title>LaTeX Preview</title>
<style>
body { font-family: serif; line-height: 1.5; margin: 40px; }
pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
</head>
<body>
<h1>LaTeX Document Preview</h1>
<pre>' > "$HTML_FILE"

cat "$LATEX_FILE" | sed 's/&/\&amp;/g; s/</\&lt;/g; s/>/\&gt;/g' >> "$HTML_FILE"

echo '</pre>
</body>
</html>' >> "$HTML_FILE"

echo "Created HTML preview at $HTML_FILE"
