import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D

# Set up publication-style plotting
plt.rcParams.update({
    "font.family": "serif",
    "axes.labelsize": 11,
    "font.size": 11,
    "legend.fontsize": 10,
    "axes.grid": True,
    "grid.alpha": 0.3,
    "grid.linestyle": "--",
    "axes.axisbelow": True,
    "figure.dpi": 300,
})

# Calculate points for Line 1
line1_x1_point1 = -15
line1_x2_point1 = 0.5 * line1_x1_point1 + 9.5
line1_x1_point2 = 15
line1_x2_point2 = 0.5 * line1_x1_point2 + 9.5

# Calculate points for Line 2
line2_x1_point1 = -15
line2_x2_point1 = (1.02 * line2_x1_point1 + 18.8) / 2
line2_x1_point2 = 15
line2_x2_point2 = (1.02 * line2_x1_point2 + 18.8) / 2

# Compute intersection point
A = np.array([[0.5, -1], [1.02, -2]])
b = np.array([-9.5, -18.8])
sol = np.linalg.solve(A, b)

# Create plot with golden ratio proportions
w = 5  # width in inches
golden_ratio = (1 + np.sqrt(5)) / 2
h = w / golden_ratio
plt.figure(figsize=(w, h))

# Define colors (colorblind-friendly)
c1 = '#0077BB'  # blue
c2 = '#EE7733'  # orange
c3 = '#009988'  # teal

# Plot lines with improved styling
plt.axline((line1_x1_point1, line1_x2_point1), (line1_x1_point2, line1_x2_point2),
          color=c1, label='0.5x₁ - x₂ = -9.5', linewidth=1.5)
plt.axline((line2_x1_point1, line2_x2_point1), (line2_x1_point2, line2_x2_point2),
          color=c2, label='1.02x₁ - 2x₂ = -18.8', linewidth=1.5)

# Mark intersection point with subtle guidelines
plt.axvline(x=sol[0], color=c3, linestyle=':', alpha=0.3, linewidth=0.8)
plt.axhline(y=sol[1], color=c3, linestyle=':', alpha=0.3, linewidth=0.8)

# Plot intersection point
plt.plot(sol[0], sol[1], 'o', color=c3, markersize=5,
         label=f'Solution $(%.1f, %.1f)$' % (sol[0], sol[1]))

# Set axis limits centered on solution point
half_width = 5  # Distance from center to edge
x_center = sol[0]  # x = 0.5
y_center = sol[1]  # y = 8.0
plt.xlim(x_center - half_width, x_center + half_width)
plt.ylim(y_center - half_width, y_center + half_width)

# Add subtle axes
plt.axhline(0, color='black', linewidth=0.5, zorder=1)
plt.axvline(0, color='black', linewidth=0.5, zorder=1)

# Add legend at bottom right inside the plot
plt.legend(loc='lower right', framealpha=0.9, edgecolor='none', 
          fancybox=False)

# Adjust layout and save
plt.tight_layout()
plt.savefig('/home/<USER>/Coding/linear_system_plot.png', 
            dpi=300, bbox_inches='tight', pad_inches=0.1)
plt.close()