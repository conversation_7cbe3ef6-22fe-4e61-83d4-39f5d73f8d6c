import numpy as np

def compute_correct_solution():
    """Compute the correct solution for Problem 4."""
    A = np.array([
        [6, -2, 4],
        [-3, 8, -1],
        [1, 1, 5]
    ])
    b = np.array([27, -61.5, -21.5])
    
    # Solve using numpy with high precision
    x = np.linalg.solve(A, b)
    
    print("Correct solution for Problem 4:")
    print(f"x1 = {x[0]:.6f}")
    print(f"x2 = {x[1]:.6f}")
    print(f"x3 = {x[2]:.6f}")
    
    # Verify solution
    print("\nVerification:")
    result = np.dot(A, x)
    print(f"Equation 1: {result[0]:.10f} = 27")
    print(f"Equation 2: {result[1]:.10f} = -61.5")
    print(f"Equation 3: {result[2]:.10f} = -21.5")
    
    return x

if __name__ == "__main__":
    x = compute_correct_solution()
