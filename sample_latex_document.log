This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023/Debian) (preloaded format=pdflatex 2025.6.4)  5 JUN 2025 07:06
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/home/<USER>/Coding/sample_latex_document
(/home/<USER>/Coding/sample_latex_document.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count187
\c@section=\count188
\c@subsection=\count189
\c@subsubsection=\count190
\c@paragraph=\count191
\c@subparagraph=\count192
\c@figure=\count193
\c@table=\count194
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen141
)) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen142
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count195
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count196
\leftroot@=\count197
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count198
\DOTSCASE@=\count199
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen143
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count266
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count267
\dotsspace@=\muskip16
\c@parentequation=\count268
\dspbrk@lvl=\count269
\tag@help=\toks18
\row@=\count270
\column@=\count271
\maxfields@=\count272
\andhelp@=\toks19
\eqnshift@=\dimen144
\alignsep@=\dimen145
\tagshift@=\dimen146
\tagwidth@=\dimen147
\totwidth@=\dimen148
\lineht@=\dimen149
\@envbody=\toks20
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen150
\lightrulewidth=\dimen151
\cmidrulewidth=\dimen152
\belowrulesep=\dimen153
\belowbottomsep=\dimen154
\aboverulesep=\dimen155
\abovetopsep=\dimen156
\cmidrulesep=\dimen157
\cmidrulekern=\dimen158
\defaultaddspace=\dimen159
\@cmidla=\count273
\@cmidlb=\count274
\@aboverulesep=\dimen160
\@belowrulesep=\dimen161
\@thisruleclass=\count275
\@lastruleclass=\count276
\@thisrulewidth=\dimen162
) (/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
) (/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count277
\Gm@cntv=\count278
\c@Gm@tempcnt=\count279
\Gm@bindingoffset=\dimen163
\Gm@wd@mp=\dimen164
\Gm@odd@mp=\dimen165
\Gm@even@mp=\dimen166
\Gm@layoutwidth=\dimen167
\Gm@layoutheight=\dimen168
\Gm@layouthoffset=\dimen169
\Gm@layoutvoffset=\dimen170
\Gm@dimlist=\toks23
) (/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen171
\ar@mcellbox=\box53
\extrarowheight=\dimen172
\NC@list=\toks24
\extratabsurround=\skip53
\backup@length=\skip54
\ar@cellbox=\box54
) (/usr/share/texlive/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2022/06/29 v1.29 mathematical typesetting tools
 (/usr/share/texlive/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count280
\calc@Bcount=\count281
\calc@Adimen=\dimen173
\calc@Bdimen=\dimen174
\calc@Askip=\skip55
\calc@Bskip=\skip56
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count282
\calc@Cskip=\skip57
) (/usr/share/texlive/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count283
\l_MT_multwidth_dim=\dimen175
\origjot=\skip58
\l_MT_shortvdotswithinadjustabove_dim=\dimen176
\l_MT_shortvdotswithinadjustbelow_dim=\dimen177
\l_MT_above_intertext_sep=\dimen178
\l_MT_below_intertext_sep=\dimen179
\l_MT_above_shortintertext_sep=\dimen180
\l_MT_below_shortintertext_sep=\dimen181
\xmathstrut@box=\box55
\xmathstrut@dim=\dimen182
) (/usr/share/texlive/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count284
\float@exts=\toks25
\float@box=\box56
\@float@everytoks=\toks26
\@floatcapt=\box57
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen183
\Gin@req@width=\dimen184
) (/usr/share/texlive/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count285
\lst@gtempboxa=\box58
\lst@token=\toks27
\lst@length=\count286
\lst@currlwidth=\dimen185
\lst@column=\count287
\lst@pos=\count288
\lst@lostspace=\dimen186
\lst@width=\dimen187
\lst@newlines=\count289
\lst@lineno=\count290
\lst@maxwidth=\dimen188
 (/usr/share/texlive/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2023/02/27 1.9 (Carsten Heinz)
\c@lstnumber=\count291
\lst@skipnumbers=\count292
\lst@framebox=\box59
) (/usr/share/texlive/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2023/02/27 1.9 listings configuration
))
Package: listings 2023/02/27 1.9 (Carsten Heinz)
 (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count293
\l__pdf_internal_box=\box60
) (./sample_latex_document.aux)
\openout1 = `sample_latex_document.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 38.
LaTeX Font Info:    ... okay on input line 38.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count294
\scratchdimen=\dimen189
\scratchbox=\box61
\nofMPsegments=\count295
\nofMParguments=\count296
\everyMPshowfont=\toks28
\MPscratchCnt=\count297
\MPscratchDim=\dimen190
\MPnumerator=\count298
\makeMPintoPDFobject=\count299
\everyMPtoPDFconversion=\toks29
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
\c@lstlisting=\count300
LaTeX Font Info:    Trying to load font information for U+msa on input line 40.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 40.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) [1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}] [2] [3] [4]
<linear_system_plot.png, id=29, 351.714pt x 213.9192pt>
File: linear_system_plot.png Graphic file (type png)
<use linear_system_plot.png>
Package pdftex.def Info: linear_system_plot.png  used on input line 248.
(pdftex.def)             Requested size: 375.80544pt x 228.5877pt.
 [5 <./linear_system_plot.png>] [6] [7] [8] [9] [10] [11] [12] [13] [14] [15] [16] [17] [18] [19] [20] [21] [22] [23] [24] [25] [26] [27] [28] [29] (./sample_latex_document.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
 ***********
 ) 
Here is how much of TeX's memory you used:
 5828 strings out of 476182
 87023 string characters out of 5795596
 1944975 words of memory out of 5000000
 27775 multiletter control sequences out of 15000+600000
 568180 words of font info for 75 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 57i,13n,65p,452b,265s stack positions out of 10000i,1000n,20000p,200000b,200000s
 </home/<USER>/.texlive2023/texmf-var/fonts/pk/ljfour/jknappen/ec/tcrm1000.600pk></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi12.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmti10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/symbols/msam10.pfb>
Output written on sample_latex_document.pdf (29 pages, 303773 bytes).
PDF statistics:
 181 PDF objects out of 1000 (max. 8388607)
 115 compressed objects within 2 object streams
 0 named destinations out of 1000 (max. 500000)
 6 words of extra memory for PDF output out of 10000 (max. 10000000)

