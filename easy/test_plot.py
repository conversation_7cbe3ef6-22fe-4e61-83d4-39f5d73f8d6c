#!/usr/bin/env python3
"""
Test script to verify that matplotlib plotting works correctly.
"""

import matplotlib.pyplot as plt
import numpy as np

def main():
    # Create some data
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # Create a plot
    plt.figure(figsize=(10, 6))
    plt.plot(x, y1, 'b-', label='sin(x)')
    plt.plot(x, y2, 'r--', label='cos(x)')
    plt.title('Matplotlib Test Plot')
    plt.xlabel('x')
    plt.ylabel('y')
    plt.legend()
    plt.grid(True)
    
    # Add some annotations
    plt.annotate('sin(π/2) = 1', xy=(np.pi/2, 1), xytext=(np.pi/2 + 0.5, 0.8),
                arrowprops=dict(facecolor='black', shrink=0.05))
    
    # Display the plot
    print("Displaying plot window. Close the window to exit.")
    plt.show()
    
    print("Plot window closed. Test completed successfully!")

if __name__ == "__main__":
    main()
