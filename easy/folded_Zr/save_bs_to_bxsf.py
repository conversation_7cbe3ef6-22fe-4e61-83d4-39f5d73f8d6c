import numpy as np
from pymatgen.electronic_structure.core import Spin

def write_bxsf(filename, eigenvalues, kgrid, fermi_energy, reciprocal_vectors):
    """
    Write eigenvalues to a .bxsf file for visualization in XCrySDen.
    
    Args:
        filename: Output filename
        eigenvalues: List of 3D numpy arrays with shape (kx, ky, kz) containing eigenvalues for each band
        kgrid: Tuple of (nx, ny, nz) k-point grid dimensions
        fermi_energy: Fermi energy in eV
        reciprocal_vectors: 3x3 array of reciprocal lattice vectors
    """
    with open(filename, 'w') as f:
        f.write(" BEGIN_INFO\n")
        f.write("   #\n")
        f.write("   # this is a Band-XCRYSDEN-Structure-File\n")
        f.write("   # aimed at Visualization of Fermi Surface\n")
        f.write("   #\n")
        f.write(f"   # Case:   {filename}\n")
        f.write("   #\n")
        f.write(f" Fermi Energy:         {fermi_energy:.4f}\n")
        f.write(" END_INFO\n")
        
        f.write(" BEGIN_BLOCK_BANDGRID_3D\n")
        f.write(" band_energies\n")
        f.write(" BANDGRID_3D_BANDS\n")
        f.write(f"   {len(eigenvalues)}\n")  # Number of bands
        f.write(f"   {kgrid[0]}   {kgrid[1]}   {kgrid[2]}\n")  # k-point grid
        f.write("  0.000000  0.000000  0.000000\n")  # Origin (Gamma point)
        
        # Write reciprocal lattice vectors
        for vector in reciprocal_vectors:
            f.write(f"  {vector[0]:.6f}  {vector[1]:.6f}  {vector[2]:.6f}\n")
        
        # Write eigenvalues for each band
        for i, band in enumerate(eigenvalues):
            f.write(f"BAND: {i+1}\n")
            for kz in range(kgrid[2]):
                for ky in range(kgrid[1]):
                    line = ""
                    for kx in range(kgrid[0]):
                        line += f"   {band[kx, ky, kz]:.6f}"
                        # Add a newline every 6 values for readability
                        if (kx + 1) % 6 == 0 and kx < kgrid[0] - 1:
                            line += "\n"
                    f.write(line + "\n")
        
        f.write(" END_BLOCK_BANDGRID_3D\n")
        f.write("END_BLOCK_BANDGRID_3D\n")

# Code to use with a band structure object (bs)
"""
# After creating your band structure object 'bs':

# Get the reciprocal lattice vectors
reciprocal_vectors = bs.structure.lattice.reciprocal_lattice.matrix

# Get the Fermi energy
fermi_energy = bs.efermi

# Get the bands near the Fermi level
# You can adjust this range to include more or fewer bands
energy_range = 2.0  # eV above and below Fermi level
band_indices = []

for i in range(bs.nb_bands):
    band_energies = bs.bands[Spin.up][i]
    min_energy = min(band_energies)
    max_energy = max(band_energies)
    
    # Check if this band crosses or is near the Fermi level
    if min_energy - energy_range <= fermi_energy <= max_energy + energy_range:
        band_indices.append(i)

# Get the k-point grid dimensions
kpoints = bs.kpoints
kpoint_coords = np.array([k.frac_coords for k in kpoints])

# Determine the grid dimensions
unique_kx = np.unique(kpoint_coords[:, 0])
unique_ky = np.unique(kpoint_coords[:, 1])
unique_kz = np.unique(kpoint_coords[:, 2])
kgrid = (len(unique_kx), len(unique_ky), len(unique_kz))

# Extract eigenvalues for each band
eigenvalues = []
spin = Spin.up  # Use up spin if spin-polarized, otherwise use the only spin channel

for band_idx in band_indices:
    # Create a 3D grid for this band's eigenvalues
    band_eigenvalues = np.zeros(kgrid)
    
    # Fill the grid with eigenvalues
    for i, k in enumerate(kpoints):
        # Find the indices in the grid
        kx_idx = np.where(unique_kx == kpoint_coords[i, 0])[0][0]
        ky_idx = np.where(unique_ky == kpoint_coords[i, 1])[0][0]
        kz_idx = np.where(unique_kz == kpoint_coords[i, 2])[0][0]
        
        # Get the eigenvalue for this k-point and band
        eigenvalue = bs.bands[spin][band_idx, i]
        
        # Store in the grid
        band_eigenvalues[kx_idx, ky_idx, kz_idx] = eigenvalue
    
    eigenvalues.append(band_eigenvalues)

# Write to .bxsf file
output_file = "NbFeSb_Zr_bands.bxsf"
write_bxsf(output_file, eigenvalues, kgrid, fermi_energy, reciprocal_vectors)
print(f"Band structure saved to {output_file} with {len(band_indices)} bands")
"""
