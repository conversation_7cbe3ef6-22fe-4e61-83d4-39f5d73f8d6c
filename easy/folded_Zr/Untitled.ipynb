{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4c01b54d", "metadata": {}, "outputs": [], "source": ["from vasprun import vasprun\n", "\n", "vasp = vasprun('vasprun.xml')\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e579f664", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "def plot_band(self, filename=None, styles='normal', ylim=[-20, 3], plim=[0.0,0.5], saveBands=False, dpi=300,\n", "              use_mask=True):\n", "        \"\"\"\n", "        plot the bandstructure with optional masking\n", "\n", "        Args:\n", "            filename: string\n", "            styles: string (`normal` or `projected`)\n", "            ylim: list, the range of energy values on the y-axis, e.g. [-5, 3]\n", "            plim: list, the range of projection values for color scale, e.g. [0.0, 0.5]\n", "            saveBands: bool, whether to save band data to files\n", "            dpi: int, resolution for saved figure\n", "            use_mask: bool, whether to use the mask column to filter points\n", "\n", "        Returns:\n", "            A figure with band structure\n", "        \"\"\"\n", "        #self.parse_bandpath()\n", "        efermi = self.values[\"calculation\"][\"efermi\"]\n", "        eigens = np.array(self.values['calculation']['eband_eigenvalues'])\n", "        paths = self.values['band_paths']\n", "        proj = np.array(self.values[\"calculation\"][\"projected\"]) #[N_kpts, N_band, Ions, 9]\n", "        cm = plt.cm.get_cmap('RdYlBu')\n", "        \n", "        # Check if eigens has a mask column (3rd column)\n", "        has_mask = eigens.shape[2] >= 3 and use_mask\n", "        \n", "        nkpt, nband, ncols = np.shape(eigens)\n", "        \n", "        for i in range(nband):\n", "            # Extract band energy values (first column)\n", "            band = eigens[:, i, 0] #- efermi\n", "            \n", "            # Skip bands outside the y-limits\n", "            if np.all(band < ylim[0]) or np.all(band > ylim[1]):\n", "                continue\n", "            \n", "            # Get mask values if available (third column)\n", "            if has_mask:\n", "                mask = eigens[:, i, 2]\n", "                valid_indices = np.where(mask > 0)[0]\n", "                \n", "                if len(valid_indices) == 0:\n", "                    continue  # Skip this band if all points are masked out\n", "                \n", "                # Create arrays with only unmasked points\n", "                masked_paths = np.array([paths[idx] for idx in valid_indices])\n", "                masked_band = np.array([band[idx] for idx in valid_indices])\n", "            else:\n", "                # No masking - use all points\n", "                valid_indices = range(len(paths))\n", "                masked_paths = paths\n", "                masked_band = band\n", "            \n", "            # Calculate projections for each k-point\n", "            p = np.empty([len(valid_indices)])\n", "            for k, kpt_idx in enumerate(valid_indices):\n", "                p[k] = np.sum(proj[kpt_idx, i, :, :])\n", "            \n", "            # Plot the band\n", "            if len(band)/len(paths) == 2 and not has_mask:\n", "                # Handle spin-polarized case without masking\n", "                plt.scatter(paths, band[len(paths):], c='red', s=2)\n", "                plt.scatter(paths, band[:len(paths)], c='black', s=2)\n", "            else:\n", "                # Non-spin-polarized or masked case\n", "                plt.scatter(masked_paths, masked_band, c='black',s=2)\n", "            \n", "            # Handle projected style\n", "            if styles == 'projected':\n", "                p_plot = p.copy()\n", "                p_plot[p_plot<plim[0]] = plim[0]\n", "                p_plot[p_plot>plim[1]] = plim[1]\n", "                plt.scatter(masked_paths, masked_band, c=p_plot, vmin=plim[0], vmax=plim[1], cmap=cm, s=10)\n", "                if saveBands:\n", "                    np.savetxt(f'band{i:04d}.dat', np.transpose([masked_band, p]))\n", "            else:\n", "                if saveBands:\n", "                    np.savetxt(f'band{i:04d}.dat', masked_band)\n", "\n", "        # Add colorbar for projected style\n", "        if styles == 'projected': \n", "            cbar = plt.colorbar(orientation='horizontal', \n", "                                ticks=[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0])\n", "            cbar.set_label('ratio of projected DOS')\n", "        \n", "        # Set plot labels and limits\n", "        plt.ylabel(\"Energy (eV)\")\n", "        plt.ylim(ylim)\n", "        plt.xlim([0, paths[-1]])\n", "        plt.xticks([])\n", "        \n", "        # Show or save the plot\n", "        if filename is None:\n", "            plt.show()\n", "        else:\n", "            plt.savefig(filename, dpi=dpi)\n", "            plt.close()"]}, {"cell_type": "code", "execution_count": 3, "id": "e50983ee", "metadata": {}, "outputs": [], "source": ["calc = vasp.values['calculation']"]}, {"cell_type": "code", "execution_count": 4, "id": "7d331471", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.12/site-packages/pymatgen/io/vasp/outputs.py:312: EncodingWarning: We strongly encourage explicit `encoding`, and we would use UTF-8 by default as per PEP 686\n", "  with zopen(filename, mode=\"rt\") as file:\n", "/home/<USER>/.local/lib/python3.12/site-packages/pymatgen/io/vasp/outputs.py:1219: UserWarning: No POTCAR file with matching TITEL fields was found in\n", "\n", "  warnings.warn(\"No POTCAR file with matching TITEL fields was found in\\n\" + \"\\n  \".join(potcar_paths))\n"]}], "source": ["     \n", "\n", "from pymatgen.io.vasp.outputs import Vasprun\n", "vp = Vasprun('/home/<USER>/Downloads/easy/folded_Zr/vasprun copy.xml')"]}, {"cell_type": "code", "execution_count": 5, "id": "dff67888", "metadata": {}, "outputs": [{"data": {"text/plain": ["(330, 192, 2)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "eigenvalues = np.array(calc['eband_eigenvalues'])\n", "eigenvalues.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "f33ba127", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "lv=0.05\n", "uv=1.99\n", "data=pd.read_csv(r'/home/<USER>/Downloads/easy/without_mask.csv')\n", "data['weights']=data['energy'].apply(lambda x: 1 if x <0 else 0)\n", "data['mask']=data['size'].apply(lambda x: 1 if lv<x< uv else 0)\n", "for_vasp=data[['energy','weights','mask']].to_numpy().reshape(197,192,3)\n", "bands=data['kdist'].unique()"]}, {"cell_type": "code", "execution_count": 7, "id": "3d4d211a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(37824, 6)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "da67b67c", "metadata": {}, "outputs": [], "source": ["vasp.values['band_paths'] = bands\n", "vasp.values['calculation']['eband_eigenvalues'] = for_vasp"]}, {"cell_type": "code", "execution_count": 9, "id": "1cab989d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(197,)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["vasp.values['band_paths'].shape"]}, {"cell_type": "code", "execution_count": null, "id": "b372adf0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "c4030aaa", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "kpoints=np.array(vasp.values['kpoints']['list'])"]}, {"cell_type": "code", "execution_count": 11, "id": "72ffd08a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_852635/781871972.py:1: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\\s+'`` instead\n", "  df = pd.read_csv('/home/<USER>/Downloads/easy/primitive/KPOINTS_band',\n"]}], "source": ["df = pd.read_csv('/home/<USER>/Downloads/easy/primitive/KPOINTS_band', \n", "                 skiprows=3,  # Skip header lines\n", "                 delim_whitespace=True,  # Handle variable whitespace\n", "                 header=None,  # No header row\n", "                 engine='python')  # More flexible engine\n"]}, {"cell_type": "code", "execution_count": 12, "id": "44511a28", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "0", "rawType": "float64", "type": "float"}, {"name": "1", "rawType": "float64", "type": "float"}, {"name": "2", "rawType": "float64", "type": "float"}, {"name": "3", "rawType": "int64", "type": "integer"}, {"name": "4", "rawType": "object", "type": "unknown"}], "conversionMethod": "pd.DataFrame", "ref": "e0fa2f42-6247-4122-8afc-0cd737707df8", "rows": [["0", "0.0", "0.0", "0.0", "1", "\\Gamma"], ["1", "0.009090909090909", "0.009090909090909", "0.009090909090909", "1", null], ["2", "0.0181818181818181", "0.0181818181818181", "0.0181818181818181", "1", null], ["3", "0.0272727272727272", "0.0272727272727272", "0.0272727272727272", "1", null], ["4", "0.0363636363636363", "0.0363636363636363", "0.0363636363636363", "1", null], ["5", "0.0454545454545454", "0.0454545454545454", "0.0454545454545454", "1", null], ["6", "0.0545454545454545", "0.0545454545454545", "0.0545454545454545", "1", null], ["7", "0.0636363636363636", "0.0636363636363636", "0.0636363636363636", "1", null], ["8", "0.0727272727272727", "0.0727272727272727", "0.0727272727272727", "1", null], ["9", "0.0818181818181818", "0.0818181818181818", "0.0818181818181818", "1", null], ["10", "0.0909090909090909", "0.0909090909090909", "0.0909090909090909", "1", null], ["11", "0.1", "0.1", "0.1", "1", null], ["12", "0.1090909090909091", "0.1090909090909091", "0.1090909090909091", "1", null], ["13", "0.1181818181818182", "0.1181818181818182", "0.1181818181818182", "1", null], ["14", "0.1272727272727272", "0.1272727272727272", "0.1272727272727272", "1", null], ["15", "0.1363636363636363", "0.1363636363636363", "0.1363636363636363", "1", null], ["16", "0.1454545454545454", "0.1454545454545454", "0.1454545454545454", "1", null], ["17", "0.1545454545454545", "0.1545454545454545", "0.1545454545454545", "1", null], ["18", "0.1636363636363636", "0.1636363636363636", "0.1636363636363636", "1", null], ["19", "0.1727272727272727", "0.1727272727272727", "0.1727272727272727", "1", null], ["20", "0.1818181818181818", "0.1818181818181818", "0.1818181818181818", "1", null], ["21", "0.1909090909090909", "0.1909090909090909", "0.1909090909090909", "1", null], ["22", "0.2", "0.2", "0.2", "1", null], ["23", "0.2090909090909091", "0.2090909090909091", "0.2090909090909091", "1", null], ["24", "0.2181818181818182", "0.2181818181818182", "0.2181818181818182", "1", null], ["25", "0.2272727272727273", "0.2272727272727273", "0.2272727272727273", "1", null], ["26", "0.2363636363636364", "0.2363636363636364", "0.2363636363636364", "1", null], ["27", "0.2454545454545454", "0.2454545454545454", "0.2454545454545454", "1", null], ["28", "0.2545454545454545", "0.2545454545454545", "0.2545454545454545", "1", null], ["29", "0.2636363636363636", "0.2636363636363636", "0.2636363636363636", "1", null], ["30", "0.2727272727272727", "0.2727272727272727", "0.2727272727272727", "1", null], ["31", "0.2818181818181818", "0.2818181818181818", "0.2818181818181818", "1", null], ["32", "0.2909090909090909", "0.2909090909090909", "0.2909090909090909", "1", null], ["33", "0.3", "0.3", "0.3", "1", null], ["34", "0.3090909090909091", "0.3090909090909091", "0.3090909090909091", "1", null], ["35", "0.3181818181818182", "0.3181818181818182", "0.3181818181818182", "1", null], ["36", "0.3272727272727273", "0.3272727272727273", "0.3272727272727273", "1", null], ["37", "0.3363636363636363", "0.3363636363636363", "0.3363636363636363", "1", null], ["38", "0.3454545454545455", "0.3454545454545455", "0.3454545454545455", "1", null], ["39", "0.3545454545454545", "0.3545454545454545", "0.3545454545454545", "1", null], ["40", "0.3636363636363637", "0.3636363636363637", "0.3636363636363637", "1", null], ["41", "0.3727272727272727", "0.3727272727272727", "0.3727272727272727", "1", null], ["42", "0.3818181818181819", "0.3818181818181819", "0.3818181818181819", "1", null], ["43", "0.3909090909090909", "0.3909090909090909", "0.3909090909090909", "1", null], ["44", "0.4000000000000001", "0.4000000000000001", "0.4000000000000001", "1", null], ["45", "0.4090909090909091", "0.4090909090909091", "0.4090909090909091", "1", null], ["46", "0.4181818181818182", "0.4181818181818182", "0.4181818181818182", "1", null], ["47", "0.4272727272727272", "0.4272727272727272", "0.4272727272727272", "1", null], ["48", "0.4363636363636364", "0.4363636363636364", "0.4363636363636364", "1", null], ["49", "0.4454545454545454", "0.4454545454545454", "0.4454545454545454", "1", null]], "shape": {"columns": 5, "rows": 197}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1</td>\n", "      <td>\\Gamma</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.009091</td>\n", "      <td>0.009091</td>\n", "      <td>0.009091</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.018182</td>\n", "      <td>0.018182</td>\n", "      <td>0.018182</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.027273</td>\n", "      <td>0.027273</td>\n", "      <td>0.027273</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.036364</td>\n", "      <td>0.036364</td>\n", "      <td>0.036364</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>192</th>\n", "      <td>0.031250</td>\n", "      <td>0.000000</td>\n", "      <td>0.031250</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>0.023437</td>\n", "      <td>0.000000</td>\n", "      <td>0.023437</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>0.015625</td>\n", "      <td>0.000000</td>\n", "      <td>0.015625</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>0.007812</td>\n", "      <td>0.000000</td>\n", "      <td>0.007812</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1</td>\n", "      <td>\\Gamma</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>197 rows × 5 columns</p>\n", "</div>"], "text/plain": ["            0         1         2  3       4\n", "0    0.000000  0.000000  0.000000  1  \\Gamma\n", "1    0.009091  0.009091  0.009091  1    None\n", "2    0.018182  0.018182  0.018182  1    None\n", "3    0.027273  0.027273  0.027273  1    None\n", "4    0.036364  0.036364  0.036364  1    None\n", "..        ...       ...       ... ..     ...\n", "192  0.031250  0.000000  0.031250  1    None\n", "193  0.023437  0.000000  0.023437  1    None\n", "194  0.015625  0.000000  0.015625  1    None\n", "195  0.007812  0.000000  0.007812  1    None\n", "196  0.000000  0.000000  0.000000  1  \\Gamma\n", "\n", "[197 rows x 5 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 13, "id": "3b90f604", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'basis': [[0.0, 5.97508523, 5.97508523],\n", "  [5.97508523, 0.0, 5.97508523],\n", "  [5.97508523, 5.97508523, 0.0]],\n", " 'rec_basis': [[-0.08368081, 0.08368081, 0.08368081],\n", "  [0.08368081, -0.08368081, 0.08368081],\n", "  [0.08368081, 0.08368081, -0.08368081]],\n", " 'positions': [[0.37509937, 0.37509937, 0.37509937],\n", "  [0.87470189, 0.37509937, 0.37509937],\n", "  [0.37509937, 0.87470189, 0.37509937],\n", "  [0.87216142, 0.87216142, 0.38351573],\n", "  [0.37509937, 0.37509937, 0.87470189],\n", "  [0.87216142, 0.38351573, 0.87216142],\n", "  [0.38351573, 0.87216142, 0.87216142],\n", "  [0.87216142, 0.87216142, 0.87216142],\n", "  [0.0, 0.0, 0.0],\n", "  [0.50071826, 0.99928174, 0.99928174],\n", "  [0.99928174, 0.50071826, 0.99928174],\n", "  [0.50071826, 0.50071826, 0.99928174],\n", "  [0.99928174, 0.99928174, 0.50071826],\n", "  [0.50071826, 0.99928174, 0.50071826],\n", "  [0.99928174, 0.50071826, 0.50071826],\n", "  [0.5, 0.5, 0.5],\n", "  [0.25, 0.25, 0.25],\n", "  [0.7491269, 0.2508731, 0.2508731],\n", "  [0.2508731, 0.7491269, 0.2508731],\n", "  [0.7491269, 0.7491269, 0.2508731],\n", "  [0.2508731, 0.2508731, 0.7491269],\n", "  [0.7491269, 0.2508731, 0.7491269],\n", "  [0.2508731, 0.7491269, 0.7491269],\n", "  [0.75, 0.75, 0.75]]}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["vasp.values['finalpos']"]}, {"cell_type": "code", "execution_count": 14, "id": "2065e198", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['incar', 'kpoints', 'parameters', 'name_array', 'composition', 'elements', 'formula', 'pseudo_potential', 'potcar_symbols', 'valence', 'mass', 'calculation', 'finalpos', 'bands', 'occupy', 'metal', 'gap', 'cbm', 'vbm', 'band_paths'])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["vasp.values.keys()"]}, {"cell_type": "markdown", "id": "a5391c90", "metadata": {}, "source": ["### I have to add band points manually. Actually, this is added by the parser which is present inside the code. I have turned that off and I would have to manually add the band points now to the code. \n"]}, {"cell_type": "code", "execution_count": 15, "id": "b544514c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_852635/1022061828.py:26: MatplotlibDeprecationWarning: The get_cmap function was deprecated in Matplotlib 3.7 and will be removed in 3.11. Use ``matplotlib.colormaps[name]`` or ``matplotlib.colormaps.get_cmap()`` or ``pyplot.get_cmap()`` instead.\n", "  cm = plt.cm.get_cmap('RdYlBu')\n"]}, {"data": {"image/png": "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**************************+VAIfHR9Fq3YzMLCTkIy69hRkSdgF6496GWtCeINeJZZgszG2BkZm3kiNJKoIEReQUEBKioqHBd4bro5m7He6cUDRsZfBoNBwyLP7jkxWwrkBz/4gdYXEziXjLNkyRJpMgF1gQKIGtNtAJDGpxm5rrlF7JpBtc+cOZs4rJbBUT00A8ZDBYygl2AVzzWAhZ2EZLcUi7QgGXXhAiz4AGMXTyB6Dul82x0rYzbGzmr/1Xj2S4g8EaQemXqfKWtKT0hQtwm1chp5ihdxeKpesXRMa+4B1mqyqfaJjo8fP65Z02hZEQBhzeGN3tzEuLS0FO+++66l0BRZH9rS0lKcPn3addc6mcW/ra1NKoZbW1szttxVosMM6HkMyNe+rCoAPa/NxvDqkajwExZ2EuwQdipxYfUiZMSFC+gLPjv2JZmYLfYM6JedsXohNRN7pCpQrLoB0LFe83a7XPbiotPV1YWBgYGo1Hu6pqjoSPUYskgia+7FKk0gK8cSKzBafFZgpE5hPDXZVPvU3Nysra933nlHs6ZVVVWhvb0dpaWlmDJliuMPFrI6dpElTsRvU82Z2YdbcXyNFHo1MpZZ/On+0b+J3xZZqiITxJws/szJ64CRh2nqsQAQFSOrKoptZT8SWbaIhZ0EO4SdKIdiRXDRsd6N2qzgE2PgI5ejme9LJDLhZCQL2aqAMxskrhKQsrEg0i1FY5FU0Np09LPCpSUwur5UliBZ/BLNzqVrSlbo10iySCq41cTvF5YsK5nEejc0I9YsGhcpagiqyukYsSyo4ubcYi2ia6OkpETLYhUZq9SibuRa9/DDD8d0swniLRQus/jT/aOFl0+cOJH2LQn11j6NQTVbqNupfVXFSRtpWag3ltWsdDr8hIWdBDstdmYFlxHxpRpTUaaXratXTNNpS59Vy5uYE6MlB8yINaula4xkRE9MTGB8fDwqHslIeYb3339fGsskPitcVEbXl6ounhAsKreeiBuLTBxRdZCIVVDYTXF1qhu/ENRGA5qN3NDM/Ga7S7PQh023iDmK1fhCldWvt7c35ro0Iq7tLnFi9zF1I0YylsU8UNHjpjkw0ylHNRa/jYZdGAmvsEPgsrCTYKZXrFmhY6Y8il018mT7Go/w1PseI31RjVi79Cxv8bhlZWNAXzRavaDTuluAcTdrpLuWvl+0m6HJDlR8GbEEAdEZsjTGjroAzRZqlrXDKSwsxLRp0xL6hG6kNAEQfeNXuero+W63mNP7DWZLs8jOETeJObsxE5BuJLg+UclDVsvtuAWrsWNuSqDSw4hrl7YylBUUBsy1rgNix9Oqrr1ebwb0ijWL2V6xelY1u+PqzAYi03Gk2yByX/W6Zxh184p50dsP2p4tVscOsY+qkiRGvsfJAqN6ItOsSFeNqbUtsvNEZKsjvRgjVbyJINIFSLM7Af0LVF9fX5SApBc5q64NI0kDdGwkqUF241cFX9P9dlLMqdBzZ6tcrumabWlVVBgJrk+UuzQVQhRiker7bwTVgwAVV/RhViSe0XuWLIzCSLksOhbX0Mj+3QD3io2JkV6xZqxqiXZvGhF/AivWQJX4E2Pag1H290jRZiVWzahos8MyYYdlUOxzvG4fmbVNEBmDR8tUrFy5UvcGped+EDXyVDF7RrJ543FtyMaqpAE6NltKILKGVawLb6LEnGz/VJYet7tc7cCOgHQjFhir9Qit/p5UsFwJkl1g3EnMtAOj7R9pb1eRWGUkTk/WIzrSAgicu+ZmZWVheHg4qn83vU9wr1gJdneeiMe9aXcyg6yUil3WQDEWOBGr5mRJErNlUsz+Bjv2V3YzorFCW7du1WLwaC0xUcqisLAQTU1Nhm9QqpggIH6rhh0lYFRJA/Fkb5qxiCU72Ujsq8/nw/DwcFpnWybK7S37znSOiTNDMjI8E4WZdmAywaUqWm/kYZbOn17LPSEauaWYSeyuY2dV/AHOuXmd+A0y8eWk+9PqbzDrLrVDtJnNuDVSBkVY4VQxF9SySovOUpFnxHUr0MsgS5dMP5n1JJllG4zsK83y7O3tTfh+UJwqaZPsNmKpaFWzm2QfA7vRO69lD45OWoljPbSYeVhlYSch2Z0n7HDzJtuSkEysWt4AubvUrPC0WqaFjqnlU0VkQdlY9c6OHTsmteSJRAmPx4OPf/zjCAaDqK6uRnZ2tuEbs8qqYaYMSrKJ1bbKSNmGZFktxForKirCiRMnUFtbixUrViR1rp2Kt2JhlTxk4QmpegysWn6dFLV2b5uFnYRkd54QxOPmTVRcX6JxokCx3ckTsu8HzMXY0dpXqjIob7/9dlgdvIKCAixYsCDKohf5tHf33XdLRV4kZt22gL7rtqCgAMPDwwlN/TeCTIyYKduQrMxGGk8nkiOSEciezvFWTOonR1gVc06uaye3zcJOgluEnRHsLlBMx8kQfnZkl+qVSXFiX6mYS/T302BaVacIWYmOyALELS0tGBkZCfuO8vLyqCyvSFGml4xBv4cePyOdFfSsfvG4/fR6vpq1TiTq5qdXwiRRYjOd462Y9BLrZi1iTtXZS9Q5w8JOQioJOxVWCxTTsRGrn2xspI6damxHdqmTMXturN5vpFMErcKvJ8SOHDmC4eFh5Ofnw+PxYGBgQLO0ycqu0H6pQOySI/n5+RgZGYnZWQH4aD2oSqyIbdLuEHQ/9H5jrFg5O+NnnBBUMiudCtkNTZWFZyaZJlmlXhg1dsQ2pqpY1wulAPSFqey3G0lU0IPeM+j1zMlzhoWdhHQQdnrYlc0rGwus1GlLVHapGdwo5oxAj/G2bdukQkx2A6AChfZSnD17tubmPXjwYFRhYyOWN1lCQqxsMVWJFXGBpN0h6H6orItNTU3S99h9wbXbeme10LBqjum80t+ut6/pFjyfTlhdc+kg1sVvp6EeZtY13YZdv13M68GDBzExMRH2gM0txZJAJgg7I5gtkGw1KzaZos0I6VAXbO7cuVIhZkbkqeq3UfGn6mlqpgK9kWKdQphQ67Co9F5WVobe3l6tnl9+fr6WBezz+aLqTLmpUwTF7kLDdJ/uvPPOmC57va4MqXCzzzRka07VxD4dxBxFJqL0QlGcWNt02+IhUrQUS+Q9g4WdBBZ2TLq1YlJZ4ayKPFUCgd2lT8w06aZWqOeee05LEBkaGtJE3vXXX29JeMYDtQTIBCkgj2u1+4GC3nRo67kdO3ZEHTMaWwTI621Z/W43ZkWnG2LNqYrjpoOYo4j1JdoAqtZ1rELDdqxtum3aeUfVX9opWNhJYGGXmWRKKyYjIm/v3r1xb1sm8lQWBKvfoyr+KSsmGnmxdzJuSCai6uvrsWXLlrBM5qysLGRnZ0vXGrWY2yHmVDFTsmNGY4usFntO1Xgtt2BHYpCR8yPVxZxqfRnxNti9tunDZaIeHGWwsJPAwi6zEDdQmlCSqi5Xs8hEXk5ODkKhUNyZ0bILK+1zaFU8Gvk+vQBpuxIcVG4derEX1q/y8nKcOXMmrMRMfn4+Fi5cqMUuvvTSS7rbNrOvVrMB7XBHUatfOgiJRGMk8cUM6VYH0MzatnNdu7mupYCFnQQWdpmFcHkBH2XcprOYU2EmTsXqtkWh5LKyMqxbt05q1VLVt7PbhTdv3jxpIgVwzjW6aNEi9Pb2Gsr2jZX0QWP/xG9WWVLozUDVpF7vJp/o2luqObEjozCTMVv4m47T1d2d6LWt6hUrs/q5KQaVhZ0EFnbpT7rF0NkJdQGaLZmih+pmRRtpq+rbyW5oZl279OK9YcMGTXDR+n9AeHZ3eXk5AGj7RMcyQWg2y9ZI7KJeCzfVDcis1UDW/FzMdSwhS8dsnbMfIxnkdGyHhc/oPjldIxGQrz+rmb90e6ptA4jZK9aO9mJOwsJOAgu79CRTYujsxGrJFED/wq+qv6eqbyeraacKDqcWQOAjwbV9+3apKJP15s3Ozsb4+DjKysoAQNsnOqaZtWKfktGYXnUDMvLdes3PVb9L1uuSxZzz6PUbTZRL0MmC3HrWajOZ9lYeSMRcUss6/T63l/1hYSeBhV16kg5lS5KJkZIpgH1P17Fqrwl3rcqlSS2A4+PjWlbs5MmTtYLLeXl5UiucGAthFwgE4Pf7TRXa1muX5mQSiVUxp9f83E03LiY2iYinc/I7VOLVqpiz44EklTpxsLCTwMIuPbEjyzCTMZJNK3Od2hGwbKSUCn3v97//fbS3t6O6uhrd3d1a6RPaSQOAdJybm4uenh5tH2K5h2liBCB318rGHo8HoVAoynJI4xiphVkV3yhznVrtuuHWmxSTeZhNHEpEwk4q9ctlYSeBhR3DxEYl8qjr1O5ew7HKckTeAO666y7NYrdo0SLNzbp79+4oARfpXhW/oaioCCdOnIj63Gc/+1lpYgTdht5YEBnfR8MCZBbmyHnVq1dGb4Rudx8xjMBI4hDgfMJOKlnpKCzsJLCwYxjjOFmgWO876RM6tZ5VV1drYq61tVUTSMXFxbpdMmRxa7TYqMwNHcvFIxtTVyeNL6SWZCNJLAAMZ9mmW7kLJr1QiSh6/mzYsMEx74BsX+j1xe1WOgoLOwks7BjGGlQ80Iuwyt0YL9Rd+fzzz0vj56jIW7lypa4IFYkXtDsEFX7UQklLtjhZYoKKvOeff15rkXbo0KGo99pdq4/JTGTJT05mwqpcnapMdie9A2JfUrVkjxlhl5WgfWIYJkVZtWoVgsEgVq5cCfEcKHoBj46O4sknn0RFRQUaGxvR3NyMQCCA5ubmsLEZtm/fjtHRUWzfvh25ubkAgNzc3LDv7u3tBQDt/+L11atXw+/3Y82aNZorZ+PGjQgGgxgdHUVnZydGR0fDbjKhUAjr1q2D3+/H+vXrwz7nJHV1daiqqsLSpUu1Ase00DGFHgO6f1bnmMkc6BqRrW0j68nMOqPvpeejaj/ouSfOU7ssabJ9Ed+TSqLOLCzsGIYxDL0I19fXw+v1wuPxYHR0FC0tLWhqakJbWxuamprCxirBR8eNjY2oqKjA6dOnAUQLLjpWCThBpMgT44aGBt3PBQIBeL1erUyKnahubtdffz28Xi9uuOEGbR4aGxul29D77QxDoWuErh2Baj2p1qqe+BPnfeR5pdoP+tAi2z+zqPabfk+6w65YhmFioueqoS5FWq8OgDSGDZBnl546dQqjo6PIysrC7NmzlRXgI8e0vh2taScr2SIr0hurQKysHIuRcif072aTHSK7phjJoE01txKTOMysESNliWSFummyQ0FBAYaHh00X9bbjN6ZzdjjH2ElgYccw1jBTEkBVq4rGsAHy7NLZs2cjGAyiuroa2dnZURdpI2VIhoaGYta0U5UkocWMaX07WvRaVR4FiC6sLF5TZf3FutHI+hwbKbjtZKwUk1rYsRaMiDxZcXCfz4eBgQEWczbDwk4CCzuGsYadzbYjBZ/IHqVP91lZWZiYmIgSX1bLkBgpSQJAKuDE98fqniErrGxHxwBqCRUxjbGsd6lUk4txFrvXgkrk0cxy0bHFyeSETBNzFDPCLvZfGYbJeFatWhW3BYhuQ8TmhEKhsGDpQCCAjo4OZGVlYXh4OOrvIlvVbBkSIyVJhHASAk7sX11dHYLBIJYuXaq9Z9myZWH7VVdXBwBYunRpWGkT8Xvpfphh8+bNmogT7tmWlhYAkIo8enNj613mQY85XQt2QM9fI+ebU+IqMk6Pfh+v849gix3DMFEkqgSCLHaHPvEno/WVkebsqpgjmXXEjrlU1b+jhZpfeukl7f2qOba7hATjHhJlsaXrmca30hhUp0RWJseUsitWAgs7hjGOkzcJveLHbrpoq1zIspijRYsWobe3N2bChKqWF2A8GeOxxx7TxBzd3o033iiNGXznnXe0vrrUHc2kPvF0UZC1rhPbiDVuamqKSniiIQwFBQWoqKgw3DqMMQYLOwks7BjGOIloBp7Ilj5Wb2J6fSxVXS1kxZEjrRpr167VihLn5uZGJWaokjHouKSkBMFgELW1tXj33XelMYM5OTlaX93Dhw87NsdM4onnAUzWug74aG3RZKWzZ89qazUUCkW14qMxqDRBiSYZcdxnfHCMHcMwcWFHXB1FFvQsCgfH+z16li/6nR0dHRgdHdVqbNF6W6qxatvAR3XvOjo6NIsdFXtiGyIeT7htN27cGFaU2OfzRW1PdMkQ9fQ6OjqixnV1dejt7cWKFStQU1MjjRlcunSprfFWjHswEkunCgUQn42MOxVra9u2bQDOrUm6VktKSjAwMIDc3NywOFDxPceOHcPg4KBWS5LXXuLhAsUMwziCqlComSKkRgob022rxrICxbICxpFjI9sWiRS9vb3SQqu04DF9nRYlllXfp10yaLIGHdP9oJ0s6FgUZhWikbtUpDZ07dOiu0bOD4r47NKlSwGcE3C068uyZcvg9XqxbNkyLFmyBACwZMmSsNdlxYoXLlyo/V0QCoVs6UrDGINdsQzDALC39lU85QhU2wDk7ki6bbuz9FQxdma+x4i7TGZ1lGXwRsbPAdDGe/bs0dxl06ZNi5qfVG1+zoQjK3Id61xRnXtizdF1QcuX0KLisrqNqrGREAI65rVoDI6xk8DCjmFiY0fChJHOCipkNxonRVuiMBKvKJt7kQlbX1+PF154QRNtg4ODWjIEAOl4yZIlmuDr7e1N6ebnTDhGko9k54oqPEGVhU6LipspNaSq55iq569bYGEngYUdw8TGasKEHZl5qhtNMi76qpg9O6yYRpIxxE3v4MGDmJiYgNfrxfj4uCba8vLytGQIANp44cKFmphrbW2NKolCO3rY8buYxBIrAUhljTNryUtGYhNjDBZ2EljYMYy9yCxsRix9yagebyYrVnUjtMOKaWQb4v35+fkYGRmJmQyhKsEismWpyBNEZuHu3bvX0u9iEsu8efPQ3d2tdWaJVT5HdV7ZETbAJAcWdhJY2DGMvYibgBELm5NizkxWrJH4HzM3QjP7Z9aKKVxaRq1qKhedEHmCrKwseL1eDA8Pw+fz4Xvf+x5b71wKXdsbNmxAT08P8vPzMX369Cg3qx0xrQBb6dwKCzsJLOwYxl70BEuixJyZBAsj8T9GxGkiBFA81hOZyMvJycHIyEhU0WIWee6CrjNaDHjt2rVRVmc7ziu20qUGLOwksLBjmMQSTyKFDLOuJqvWNpUFkH5nIm6Asrg7K4JL5jKnrlr6WwoLC7Ukjfnz50vblTH2o1rbNEtVJDCokovMnleR4QlspXM3ZoQd17FjGMYRaM02Wm/LDLTeVVNTE9ra2tDU1BRWG05An1Ejx2I7jY2Nluvimam/Zwd0zuh+WN3O2rVrtf3v7e0FAPT29mpzWFtbG1aIVgi+YDCI5cuXo6SkBMuXL7fp1zFG6jzS+obiPaFQKO7zCoC2PVEbkUVd+sAWO4ZhXIUR60VxcbHhulpma2zZHWNnB3a3eFNZA2kfWgBau7IdO3Zon/V6vaivr9c6DjD6yJJ39KzOqphRJ9YAizr3w65YCSzsGCY1ULlwjdTEsqPGlttvcnbH+tH5pv1Babbs8uXLw1y2QtyJOnsAtDELvnPIHlBk/YQj15zIfnWi12qi40QZ+2BhJ4GFHcOkBmxJiI3dwe50vmlR2n379kW9VxRNbmhowNNPP63VygOgjTNB8JnJxFYl76jW9ty5c6WFge04FzhRInVhYSeBhR3DMOmAXUkV8W6bijxaZ8+M4KusrERnZ6frRKBe3UMzmdjJLjnC5UzSAxZ2EljYMQyTbjhpgaEuQTNFjM0IPoFRq5/emArFuro6XauaaqxX99DuTGyrc20EttKlByzsJLCwYxgm3XDSekddgjK3rFlkgk8IMaNWP72xwOv1oqqqylKCjZG6h3Zbu+yea7bSpR8s7CSwsGMYJp1xMvbu9ddfT6ibVGX10xtToahqveaWxvTiN1LrIsfSMSpY2ElgYccwTDrjZNJJRUWFZh3r6uqydduZipNzyglI6QcXKGYYhskwaLFaWvzWDurr6+H1etHQ0GDL9hj755Qe83gKFzOpDws7hmGYNCOeThUyNm/ejK6uLoRCIVRUVKCxsdGW7WYaVHyJOd20aZMt27b7mDOpCws7hmGYNIO2pbLTetfS0oLR0VG0tLTYsJeZh93iix7bRLe8Y9xLSgu73/3ud7jmmmtw3nnnwePx4De/+U2yd4lhGCbp2NVnNhJ2ycaH3eKLHlt2vzKClBZ2g4ODuOiii2wzZTMMw6QbdooJ6j5sbGxkt6xBhGUNgK3ii610jIy0yYr1eDx45pln8PnPf176d86KZRgmk7G7TyhnyhrHzvIj3O81M+Gs2Bj09/fj9OnT2n/Dw8PJ3iWGYRjHsTu+i92yxrHTssZJEoweGWexu/baa3HmzBnt9dtuuw133HEHZsyYgba2NgDnnkRDoRCOHz8OALjgggtw5MgRDA0NITc3F+effz4++OADAMC0adOQlZWFY8eOAQD8fj+OHTuGwcFB+Hw+VFdXay1iysrKkJOTgw8//BAAMHv2bHR3d2NgYABerxdz5szB7t27AQAlJSXIy8tDZ2cnAKC6uhq9vb04ffo0srOzMW/ePOzevRuhUAhTpkxBYWEhDh8+DACoqqrC6dOncerUKXg8HixYsAB79+7F+Pg4ioqKMHXqVBw6dAgAMHPmTJw5cwa9vb0AgIULF2Lfvn0YGxtDYWEhysrKcPDgQQDAeeedh+HhYZw4cQIAMH/+fBw4cAAjIyPIz89HRUUFDhw4AACYMWMGxsbG0N3dDeBcZfWOjg6cPXsWeXl5qKysxP79+7X5BqA99c+ZMwednZ3afFdVVWnV2MvLyzFp0iQcPXoUAFBTU4Ouri4MDg4iJycHNTU12LNnDwCgtLQUPp8vbL57enrQ39+PSZMmYe7cudi1a5c235MnT8aRI0cAALNmzcLJkyeV811UVISOjg4AwPnnn4/+/n7lfJeUlKC9vR0AUFlZiaGhIW2+FyxYgP3792N0dBQFBQUoLy8Pm++RkRH09PQAONd2qL29HcPDw8jPz8f06dO1NTt9+nRMTEyErdnDhw9r8z1z5sywNevxeLT59vv9OHr0KM6cOQOfz4dZs2bFnO/jx49jYGBAOt+5ubnSNRs531OnTkVBQUHYmu3r60NfXx+ysrIwf/587NmzBxMTEyguLkZxcXHYfA8MDODkyZNRa1Y232fPnpWu2YKCAkybNi3mmj106BCGh4cxefLklL9G/P73v8fJkydRU1ODkydP4q233sLy5ctRVVUV9zXid7/7HQ4ePIjS0lJceumlfI1w4Bpx4MABvPrqqxgbG0NNTQ1qampQW1vL14gMuUbs2bMHw8PDmVWg2Kiwq6mpQVbWR4ZKn88Hn8+XoL1kGIZJPnZ3JnCy12mqYrfLlLtJZDbsio1BYWEhioqKtP9Y1DEMk2nYXQ5F2AfSxE5gC3a4TLmcCWOFjBN2DMMwmY7d5VDWrVsHv9+P9evX2971IlWxQ4hxORPGCikt7AYGBtDa2orW1lYAwMGDB9Ha2qr52RmGYZjY2CFAqOhoampCW1sbmpqabNzL1MDutl5spWOskNLC7u2338bFF1+Miy++GADw9a9/HRdffDHWr1+f5D1jGIZJDezuMZvJblm73a9spWOskNLC7vLLL0coFIr671e/+lWyd41hGCblYLdsfNjtfmUYK6S0sGMYhmHsw46kikxzy9phYeMkCcZO0qbciR7ceYJhGMY4dpTXmDt3Lnp6elBWVqbVPUs37JgnLmXC6MHlThiGYZi4sMNylAluWTvmia10jJ2wxY5hGIaJiR3FdtOpiLEd88E9XxkzsMWOYRiGsQ07AvpptmyqW+/smA9OkmCcgoUdwzAMExPhKqytrbUsyKhbNtVFjVXXKSdJMImAXbEMwzCMIewK8m9sbERLSwvq6+uxefNmG/fQOexwnXKSBGMVdsUyDMMwtmNXj9lgMIjR0VFs27YtZVyy8VgZxVwFAgG20jGOwxY7hmEYxjTxWJ+E9ev06dPo7u5OCQuW2Oc1a9aYrlPHljomXthixzAMwzhKPDFiopDvVVddBa/Xi0Ag4MAexo9d7b04no5JJCzsGIZhGNPY0WNWuGSDwaArM2XtcL9yz1cm0bCwYxiGYeLCqgCiliw3tR+zIyYu1TN/mdSFhR3DMAwTF1aTKqgly0117oQoCwaDpixtXM6EcQOcPMEwDMPYhtVEAZqcsGHDhoR3qaDlTABYSpTgJAnGKTh5gmEYhkkKqWq9o65TMzFxbKVj3AZb7BiGYRhHcLv1jq10TKrAFjuGYRgm6dhtvWtsbERFRQUaGxst7xPdD7bSMekIW+wYhmEYx7HDenfnnXdidHQUXq8X9fX1htuSUcucEHN+v1/7N1vpGLfDFjuGYRjGVVi1bFFLWn19PbxeLxoaGtDS0oLR0VG0tLRolrzFixdrFj2VZY7uh9X6cmylY9yMZYvdrl27sGvXLvT09MDj8aCsrAwLFizAwoUL7d5HW2CLHcMwjDugFrRVq1ZZ2kZjYyNaWlrQ0NCAp59+GqOjo9rfvF4vqqqq4rbM2b3PDGMVMxY7U8LulVdewa9+9Ss899xzOHXqFCI/6vF4UFxcjGuuuQa33XYbLr/8cks/wAlY2DEMw7gDu12ZQuRVVlais7MTDQ0NWLp0adxizsl9Zhgz2C7sXnzxRaxbtw7vvPMOLrzwQnz605/GkiVLUFNTg6lTpyIUCuHkyZM4ePAg3nnnHbz88sv405/+hMWLF+M73/kOli9fbtuPswoLO4ZhGHcgrF+1tbXYsWOHa61gdmTNMowd2C7sCgoK8Ld/+7f4yle+gvnz5xvaiT179uAnP/kJ/u3f/g2nT582tucOwsKOYRjGXbjdCub2/WMyB9uTJzo6OvCv//qvhkUdAMyfPx//+q//ivb2dsOfYRiGYTIHq+VQnMaOXrEMkywMx9idPHkSU6dOdXp/HIMtdgzDMO7FTdYxN+0LwwAOlTuZPn06rrvuOjz99NMYHh6OeycZhmEYRpBs6x0XHWbSBcMWuxUrVmDr1q04c+YMCgsL8YUvfAErVqzAlVdeCY/H4/R+xg1b7BiGYVKDZFjM2ErHuBlHLHaPP/44jh8/jsceewyf+tSn8Pjjj+Mzn/kMKisrceedd+Kdd96Je8cZhmEYJlHWO7bSMemI5QLFJ0+exJYtW/DEE0/gtddeAwBccMEFWLlyJW6++WbU1NTYuqPxwhY7hmGY1INa0kSh4XjKo6jai7GVjnEzCWkpNnXqVPzd3/0dXn31VXR0dOCf//mfMXnyZKxfvx4XXHABPvGJT1jdNMMwDMMACLfe0dZgZix5RtqLMUy6YNliJ+OPf/wj1q9fj2effRYejwfj4+N2bTpu2GLHMAyT2ghhtmbNGjzyyCNRlrxAIIBgMBhWUDjSMmdHezGGSTSOtRST0dHRgSeeeAL/8R//gT/96U8IhUL4xCc+gRUrVuD222+PZ9O2wsKOYRgmfZCJPK/Xi9HRUfj9fgBgMcekDWaEXey/Kujp6dHi69544w2EQiHMnz8f999/P1asWIHq6morm2UYhmEYQ6xatUqLswuFQmEtytasWaO9JsScG1uWMYwTGLbYDQ4O4plnnsETTzyB7du3Y3R0FDNmzMCNN96IFStWYPHixU7va1ywxY5hGIZhmFTEEYvdtGnTcPbsWRQUFODmm2/WathlZVnOv2AYhmEYhmFsxLCwW7ZsGVasWIFrr70Wubm5Tu4TwzAMwzAMYwHDwu7ZZ59V/q2vrw8FBQXs4mQYhmEYhkkilv2ob7/9Nv7yL/8SkydPRmlpKV599VUA5xIrPve5z+GVV16xax8ZhmEYhmEYA1gSdq+//jo++clP4oMPPsDKlSsxMTGh/a2srAx9fX346U9/attOMgzDMAzDMPpYEnb33HMPFixYgF27duGBBx6I+vsVV1yBt956K+6dYxiGYRiGYYxjSdgFg0Hcdttt8Pl88Hg8UX+vrKzEsWPH4t45hmEYhmEYxjiWhJ3X6w1zv0bS2dmJgoICyzvFMAzDMAzDmMeSsFu6dCmefvpp6d8GBwfxy1/+EpdddllcO8YwDMMwDMOYw5Kwu++++/D222/j6quvxm9/+1sAwHvvvYef//znWLJkCbq7u7Fu3Tpbd5RhGIZhGIaJjaVesZdccgn+67/+C7fffjtuueUWAMCdd94JAPD7/fiv//ovfOxjH7NvLxmGYRiGYRhdLAk7ALjyyiuxd+9etLa24oMPPsDExAT8fj+WLFkiTahgGIZhGIZhnMWysBMsWrQIixYtsmFXGIZhGIZhmHgwFGP3xhtvWP6CeD7LMAzDMAzDGMeQsLvyyitxxRVXYMuWLThz5ozu+wcGBvDEE0/g0ksvxVVXXRX3TjIMwzAMwzD6GHLF7tu3D/fffz9WrVoFr9eLSy65BIsXL8bs2bMxdepUhEIhnDx5EgcPHsTbb7+NHTt2YGxsDLfccgsef/xxp38DwzAMwzAMA8ATCoVCRt/c09OD5uZmPPvsswgGgxgaGgr7e15eHj7+8Y/jc5/7HFatWoXy8nLbd9gqY2NjePXVV3HRRRchOzs72bvDMAzDMAxjiPHxcbz33nu47LLLMGlSbJucKWFHGRsbQ0dHB06cOAEAKC0tRVVVle4XJgsWdgzDMAzDpCJmhJ1lFTZp0iTU1NSgpqbG6iYYhmEYhmEYG7HUeYJhGIZhGIZxHyzsGIZhGIZh0gQWdgzDMAzDMGkCCzuGYRiGYZg0gYUdwzAMwzBMmmBJ2H33u99FZ2en3fvCMAzDMAzDxIElYfetb30Ls2bNwpVXXolf/vKX6O/vt3u/GIZhGIZhGJNYEnaHDh3Cgw8+iN7eXnzpS1/C9OnTceONN+KFF17A+Pi43fvIMAzDMAzDGMCSsKusrMQ3vvENtLa24g9/+ANWr16NN998E9dccw1mzJiBO+64A2+99Zbd+8owDMMwDMPEIO7kiQsvvBAPPvgg2tvb8eqrr+JTn/oUNm/ejE984hOYO3cumpqacPz4cTv2VcqmTZtQXV2N3NxcXHLJJdixY4dj38UwDMMwDONmbMmKPXv2LJ588kl873vfw3PPPYfs7Gz81V/9FS688EJs2LABfr8fzzzzjB1fFcZTTz2Fr3/96/j2t7+NnTt34qKLLsLy5csdFZIMwzAMwzBuxbKwC4VC+O///m/ceuutqKiowM0334wPP/wQ3/ve93DkyBE8//zz+PWvf4329nYsWbIEd955p537DQD4/ve/jy9/+cu47bbbsHDhQvzkJz/B5MmT8W//9m+2fxfDMAzDMIzbmWTlQ1/72tfw1FNPoaurCzNmzMBXvvIV3HLLLfizP/uzqPfOmDEDf/u3f4tbbrkl7p2ljIyM4J133sHdd9+tvZaVlYVly5bhjTfeUH6uv78fWVkf6Vmfzwefz2frvjGZQWNjI1paWlBfXw8AuuO2tjYEg0EEAgEAkI5XrlyJjRs3YvXq1Vi1alUSfhXDMAyTylgSdj/72c9w3XXX4ZZbbsGyZcvg8Xhivv+Tn/wkfvnLX1raQRU9PT0YHx9HRUVF2OsVFRXYs2eP8nMXXnghzpw5o/37tttuwx133IEZM2agra1N20YoFNJcuhdccAGOHDmCoaEh5Obm4vzzz8cHH3wAAJg2bRqysrJw7NgxAIDf78exY8cwODgIn8+H6upq7N27FwBQVlaGnJwcfPjhhwCA2bNno7u7GwMDA/B6vZgzZw52794NACgpKUFeXp5WL7C6uhq9vb04ffo0srOzMW/ePOzevRuhUAhTpkxBYWEhDh8+DACoqqrC6dOncerUKXg8HixYsAB79+7F+Pg4ioqKMHXqVBw6dAgAMHPmTJw5cwa9vb0AgIULF2Lfvn0YGxtDYWEhysrKcPDgQQDAeeedh+HhYZw4cQIAMH/+fBw4cAAjIyPIz89HRUUFDhw4AOCcoB8bG0N3dzcAYO7cuejo6MDZs2eRl5eHyspK7N+/X5tvAOjq6gIAzJkzB52dndp8V1VVYd++fQCA8vJyTJo0CUePHgUA1NTUoKurC4ODg8jJyUFNTY12/EtLS+Hz+cLmu6enB/39/Zg0aRLmzp2LXbt2afM9efJkHDlyBAAwa9YsnDx5UpvvRx55BNXV1aiqqsKxY8fw5JNP4vrrr8c//uM/oqWlBRdccAH+8R//EaFQCA899BDuuOMO5OfnY/fu3Zg+fTouuOAC+P1+DAwMYMqUKaitrcVnPvMZPPjgg2hsbMRnPvMZfPDBB9i4cSNuvPFGdHd3Y9myZfjMZz6Dz372swCAefPmob29HcPDw8jPz8f06dO1NTt9+nRMTEyErdnDhw9r8z1z5sywNevxeLT59vv9OHr0KM6cOQOfz4dZs2bFnO/jx49jYGBAOt+5ubnSNRs531OnTkVBQUHYmu3r60NfXx+ysrIwf/587NmzBxMTEyguLkZxcTE6OjoAAOeffz4GBgZw8uTJqDVbVFSEkpIStLe3AziX6HX27Fnpmi0oKMC0adNirtlDhw5heHgYkydP5msEXyNiXiMi57uoqChszfb39yvnO3LNDg0NafO9YMEC7N+/H6OjoygoKEB5eXnYfI+MjKCnpwcAXyPS+RoRS9dE4gmFQiHD7/7/Mzg4iPz8fLMfs5UPP/wQlZWVeP3111FXV6e9/o//+I949dVXo7Jyx8bG8Oqrr6KmpoYtdowSlRWupaUFo6Oj8Hq9AIDR0VEAgNfrRUNDA0KhEFpaWmKO9+/fj2AwiNraWoRCIen47bffxsTEhLY/WVlZWLJkCVv0GIZhMpjx8XG89957uOyyyzBpUmybnCWLXbJFHXDuyTY7O1t7ohB0dXVh+vTpys8VFhYiOzvb6d1jUggq5oSAa2lpAQBtLP5GxVplZSU6OztBn41ijf1+P1pbW1FTUwMA2riurg69vb1YsWIFampq0NLSgomJCYyPjyMvLw/BYBDAOXftnj170N/fj7Vr1wIAizyGYRgmDEsWuyuvvDL2Rj0e5ObmYubMmbjiiivQ0NCgqzCtcMkll6C2thY//OEPAQATExOoqqrCP/zDP+Cb3/xm2HuFxe6iiy5iYcdIxZzX65UKuIaGBmzatCnqs2NjYwiFQmFWPCvjKVOmoLu7G+Xl5bjqqqvCRCONzaMWvaysLJSWlmqfW7t2LYs8hmGYNMWMxc5SVuzExAQOHz6MV155Be+9957m937vvffwyiuv4PDhwzh+/DhaWlpw88034+Mf/7gWA2AnX//61/Gzn/0Mjz76KHbv3o3bb78dg4ODuO2222z/Lib1aWxsREVFhSbMqDVOuFQ3b96Mrq4ubNq0KWws+6wQdQ0NDWHbMDsWz1ZCSI6OjqK9vR2jo6PYvn07Vq5cCb/fjxUrVuD666+H1+vFDTfcgKGhIQDA0NAQmpqa0NbWhqamJjQ3NyMQCKC5uTmZ080wDMMkAUsWu9deew2f//zn8YMf/AA333yzZgEbHx/HY489hrvuugvPPfccLrnkEjz66KP48pe/jC9+8Yv42c9+ZvsP+NGPfoSHHnoIx44dw6JFi7Bx40ZccsklUe9ji11mYsQyR61xdn3WDM3Nzdi4cSPWrFmD119/HS0tLcjKysLw8DDKysrg8XiklrkNGzagp6cHZWVlOHv2LAYGBlBQUIC8vDy25DEMw6QRZix2loTd0qVL8alPfQoPPfSQ9O/f+MY38Nprr2llR/7u7/4Ozz33nJZ5lAxY2GUWMnepm8ScHlTs3X///ZqAoyJPuG3r6+uxbds27T0ApO8XmZcMwzBMauG4K/YPf/gDqqurlX+vrq7Ge++9p/17yZIlWuo2wziFnruUulb1PhfLRav33arx8uXLUVJSguXLl4eN6XsEq1atQjAYxMqVK7Fu3Tr4/X6sX78+zG0bDAYxOjqKYDCIZcuWwev1YtmyZWFj8f6+vr6o72AYhmHSD0sWO7/fj6qqKmzfvj2sdAhwLv7uiiuuwOHDh7XaLw8++CB++MMfssWOsR2rFjY7XLQ0wUFWDiVyLEqkRJKVlaUlRIjSJtXV1cjOzo5yocrctvX19QgGg2hra4Pf78fp06ejrHrUcvnwww+ze5ZhGCaFcNwVu2nTJtxxxx245JJL8OUvfxl+vx8AsH//fvzsZz9DMBjExo0b8fd///cAgMWLF6Oqqgq/+c1vzP8am2Bhl55UVFRYcpea/ZxMCApiZdMaqWP3/vvva7UhBwcHw763sLAQV199tfbdmzdv1v4WCAQ0MRcIBKRu2eLiYrS1tSE/Px8jIyNoaGjAtm3bOAaPYRgmhXBc2AHAj3/8Y6xfvx4nTpzQOk+EQiGUlpbi3nvv1UTd8PAw3nzzTVRXV2PWrFlWvsoWWNilD7IiwkbEnNnP6Vn1hMUu3rg7aoVrbm7W6tYB0CziwqJ3/fXXa/tUV1enfe6RRx7RRN7q1aujrHqLFi1Cb29vVNIFx+AxDMO4n4QIO+Cci+ntt9/WWs/MmjULH//4xzUXlJtgYZfaqERWZIHqWFArnepzbkmeEPvR0NCArVu3aha9kZGRqH2KFHmhUEizwm3cuBFtbW2au5da6SITM5YtWya1DDIMwzDJxdHkiTNnzqC0tBQPPfQQvF4v6urqcOONN+LGG29EXV2dK0Udk5oYSWowsw0jn7OaPGE39PseeOAB+P1+PPjgg1i0aBEAYNGiRVFdMgBooq6trU0Td36/H5MnT9b+Tt9LEzNk22MYhmFSC0sWu4qKCnz7299OqQw7ttilHlbj5wB5uRMjVjrAuGs3GdC4upKSEq2HbHt7u7QMirDkBQIBBINBrFmzBhs2bJC6X6mVUMQGsvWOYRgm+Tjuim1sbMSePXuwfft2Lb7O7bCwSw3sEllCFALQrG6ytmDxuHaTAY3Ho3F1fX190rg5AFEibu7cubruVyqqOYuWYRgmuTgu7H73u9+hsbERZWVl+PKXv4zq6mrk5eVFvW/x4sVmN+0YLOzci10iy4wojMca6BZUpU9kxYpzcnIQCoWi4vHuvPNO6XxT6x3NouUEC4ZhmMTjuLCjtetkFrtQKASPx4Px8XGzm3YMFnbuw2p3CBV6yRGp5HI1C3XRyrJiVS5pI+5XIxY+hmEYxjnMCLvYf1Xwy1/+0tKOMQyF1oOj7lKrgoGKwljf19LSgq6urrQSJlTMybpT5OTkYGRkBIsWLdIsfatXr8bmzZu1eRDCuKWlJWxu1q1bF2Xha2lp0Sx/7KJlGIZxD3GVO0kl2GLnDuy2mtHtyYRaOlvpVMgKF+fm5qK/vz+qMwUtUPzGG2/oWu/YRcswDJN4ElbHDgCOHj2K48ePY86cOcjPz49nU47Cwi55OJmooOd+NVK7Lt2QJViILhRr1qzBPffcg4GBARQUFKCioiLKhbt69WrcdddduvMmXLT5+fmYPn06W+4YhmEcwtE6doJnn30W8+fPx8yZM7F48WK89dZbAM4Fal988cV45plnrG6aSTOs1qBTYaQ2nXhPZWVl3N+XaqxatQrBYBArV67U6th9+tOfBnDOPZubmwsAyM3NRSAQgNfrRSAQCKt/R+e1ubkZgUAAzc3NYd8jauBlZWWhra0NTU1NyvcyDMMwicGSxe65557D5z//edTV1eEzn/kM7r33Xmzbtg1XXnklAOCzn/0ssrOz8eyzz9q+w1Zhi11icdIFasQKl4mWuliokitoTTvakYJ2r2hqaorpcqXJFaI3rd/vD2uNxjAMw1jHcYvd/fffj0svvRSvvfaa1hOWUldXh3fffdfKppkURtUpwo6uDWasdEa7TGQSwnK3Zs2aMIseTbSgr1PrHX2PzCJHu1dQCyBb7xiGYRKPJYtdXl4evv/97+P222/HiRMnUF5eHmax+/nPf45/+Id/wNmzZ23fYauwxc4ZEtVbla109kGzYgFIrXT0dVojb/v27TGtd9QyCEAav8dxeAzDMOZw3GI3efJkDA4OKv9+4MABlJaWWtk0k2I42VuVrXTOQK1xKisdfV2UTAkGg7rWO2oZVMXvsSWPYRjGOSxZ7BoaGrB37168++676OvrC7PYHTt2DH/+53+Oz372s66qd8cWO2sIi1xlZSU6OzvDYuacLiHCVjpnoFmzKiud3dY7GtdHW6FxHF7i0CsNxDCMe3G83MnevXuxdOlSVFdX42/+5m+wbt063HXXXfB6vfjpT3+KUCiEt99+G9XV1VZ/g+2wsLMG7bkKnCskDCAhYorWTFOJRiPvYdRQ8UVFlpHXu7q6MDAwAJ/Ph5kzZ0a5Wc0KSHbROgs/BDFM6pKQOnbvv/8+1qxZg//93/8F3cTll1+OTZs2YcGCBVY26xgs7KwRabGjxWudEFNGrApsebCPeKx3999/P3p6epCVlYWJiYmYFjgjQpHj8JxBdg7zQxDDpBYJLVB88uRJ7N+/HxMTE6ipqUF5eXk8m3MMFnapAbtfk4dZ650QhePj42hvb0cgENBi9axY7yKLKaeKwNMLV5B17lCFNDjxsMLnC8OkPgkVdqkCCzv3YqTmXSa2Bks0VHytXLlS93UBFQ5VVVW68XN6QlG0PHODFU+WQUzbr9FscEGscAU6V/Q9dCy2SYWiWbHH5wvDpBcJEXbj4+N46aWXcODAAZw8eRKRm/F4PFi3bp2VTTsCCzv3wlY690EFjczyRl+ncY60x2xdXZ0p6514j6wlWqTIA2Cr4FMJOJHNG9lj99SpU0ohFitcQTZXkeOnn346SiiK7zBqDbS7dR/DMMnFcWH39ttvo76+HkeOHIkSdNqGPR6Mj4+b3bRjsLBzH2ZifzhJIrGYdcsK5s2bp4mfoqIiU9Y7mXVOJfIAeY08ADHHgUAAwWAwpoCj2w4EAppY2rZtm9ZhY9myZY7HmdLzQog9lXXP6TqSDMMkF8eFXW1tLdrb2/GLX/wCn/rUpzBlyhSr+5owWNi5Dz0rHCdJJA+rGa20vZgQP0atd3plUGT7FEvwycZerxejo6MxBRzwkUUsGAxKS7bIXNJOorL0UcGnJ+b4fGKY1MVxYZebm4vvfOc7uPPOOy3vZKJhYec8Zm8celY4dr+6AzPWOyq+aB9aI9Y78dna2lrs2LHDsJtVJfhkY7HtyJp8VMAB0dbAZIg5I5ixZPP5xDCpi+OdJ2bOnKl0wTKZC+1CoYJ2itDrTsGdJNwB7Sah97qqD61qGxTx2R07dki7VKg6VtDv1BsvXbpU2yfaUYPun6qvrhsx0uFFnHOVlZV8PjFMBmDJYvezn/0MDz/8MILBIIqKipzYL9thi53zqILoARgO6mZ3kbuRJU/oJVpEWsdUblnZ5+yIq6NjWcat09Y41fzYjaqUCidSMEzq47gr9vvf/z4ef/xxHD58GDfeeCPOP//8KLHk8Xjwta99zeymHYOFXWJRlXXQiwNid5G7kblf9RIqgPDjOmXKlJityCh2xNXRsaiRl0jXqpH5sYoqExYwfs4xDON+HBd2WVn6HlzOis1sVMHeekHdANfccjOymnZ65UuA8PXw8ssvawkW69ats2TNMhNXFzlOtFtVrw6gWVRijgo4J7vDMAyTeBwXdocOHTL0vlmzZpndtGOwsEseKheRcLWylS41kbkYjVin6HrYvn27Zr1bu3ZtzJIkqdCFIhHQ84WtcQyTGXDnCQks7JKHnluWe1imJnpZsSrrFP1cX1+fZr3zeDzo7u4O6z0LwDE3ZqKwI8bOrFVb9v54OlkwDJNcHBF2O3bswJw5c1BSUqL73oMHD+L3v/89brnlFmN7nABY2CUW1Y1IVYOLLXWph155EiNJFdRFev/996Onpwf5+fmYPn160l2odmE1xi6eThKyhylBZGFjFnkM434cEXbZ2dlobm7GzTffDADo7e3FzJkz8dvf/haXXXZZ2Hsff/xx3HLLLRxjl8EYca8uXrwY7e3tqK6uxs6dOxO8h4xdqIQL7UKhSpKQtfJKZREnw2qMnVmXq97DlKqTBYs8hnE/jtSxi9R/oVAIZ8+edZV4Y8xBa8rZvT0jNeg6OzvD/s+kJqoadbSOnaoGnWjltXHjRq1mXCgUkr43VbFaC4+eQ0bq1dE6kvT9Yrxz507tNbpt+jm7rwkMwyQewxa7rKwsPPbYY5rF7sSJEygvL8e2bdtw5ZVXhr2XLXapgd1JC2a3x/1f0w+VBY52oRBJEqoWZbTWXKrG1SUKOzLK6XnIljyGcSeOd55g0gP61G71Sd2slc5M5wkm9ZBZ4CK7UND3COjroVBIswCqLH2phJO/QWWlMwP9HFvyGCb1YYsdA8C69c7s57i0SXqjqmkHfGSZU/VoDQQCURYiGr9Hu02kUukTuwsUJ6ruo8ySB3yUfCG+my16DOM8jlns2tvbsXPnTuzcuRN/+MMfAAAffPCB9pr47+DBg9b3nkkKZqx3Zq10qu9h0g9qpVNZ71Q9WunrAvp3maUvFTDSJ9cMdljpjCCz5Hk8Hu27VRY9tu4xTHIxZbHzeDxhr4VCoajX6OtssUtNZNl4tAaW2dIL3P81M1Flg6qsem+88UbMemt2d3BIJdzSnUXVUYbG5gHRtSrZwscw8eFIuZNHH33U9I7ceuutpj/jFCzsjKNywQCwVO2e3a+MqqYdLYlSVFSEtrY27W+8Xj7C7eeQVcFHRTwAqQikYxaETKbCnScksLCzhrhgx9MdgrNfGVWc2dy5c6P6xpaWluLdd98NEwmZeFN3i5UuHvQEnyBS+KnGKgugGBcVFeHEiRMIBAJaOAC3pnMf7MUxDws7CSzszsEnFJMMjCRV0NfFzVdlqbKjTZfbcbuVLh5kD4xU+KnGKgugGFP8fr/2MAEg5ZNw0ol0XttOwcJOAgu7c/AJxSQblfVO9jq1+CxdulS7ITc1Nel2tUhV7LCSpysqC6AYC4tdbW0tVqxYIW1d98gjj7DISwLpYIFOJizsJLCwOwe7RZlko5dUoeo9S+PxQqFQlAs3XW7O/PDlLHT9schLHLyu44MLFDNKaAkDLkvAJANa+oQW7xWv79ixQ1rWhBY5XrZsGbxeL5YtWxZWBiUdChpzSSBnoetPVU4nHdaR2+B1nThY2GUwtA6VCiH+Fi9ezCKQsR1ZbTp6s6U3WCrmVLXwzNS6c9PNmzuyJAc9kbdhwwbXrJFUhNd1cmBXbAajcsvSWAgh/gTcQ5KxE1lSRUlJCVpbW1FfX4/t27dr7lcAUf1mjSZjiNcDgYAmBsXN2w09adlN5S7EuuS+xfHB69o+2BXLGELllqWWPGE+r66ulvaQZJh4kHWqENa4LVu2YGhoCAAwNDQU5ooVGOk9u3HjRm28ZcsWtLW1oampyfaOEGaJp4ML4yxiXa5duzat+hZbxUzYDq/r5MPCjgEAqZhraGjQxN/OnTuljcIZxi6E0PL5fACAvLw85ObmAgByc3Oxbt06+P1+rF+/Pky0BQIBeL1eBAIB5etiLEpjRIrDRN20VQ9Q7KZyJ6oWeZkm8lTt4yji9aeeeorXdZJhYccAACorK7X/652MnIDBOIG4iX7ve9+D3+/Hgw8+GCbmBKFQKMzatn37doyOjmL79u1hYu6FF17A6OgoXnjhBe09AKRJF6qbtmos641K41Dp3+nnVA9QjPtJh77FVqFrVSXyxOuhUIjXdZLhGLsMRhZLZzYWQtZXlmPvGCeg5U5o7TravcLj8WjvOXHiBCYmJpCVlYXJkydjYGBA+0x5eTmqq6sRDAYRCATg9/u1tauK66PjU6dOKYvjRhbPraqq0mL5AoEAlxtKA1QFtzOhTIqs5aSVVpOMOTjGLgbXXXed9Gk7UyxOerF0DQ0NunOiiqEwYq5nGKvQGDu6vmi2LH3P9ddfD6/XixtuuEFz6dJttba2AgBaW1vDsmzpNlRjuu5lcaj079TSw64pZ1i+fDlKSkpw/vnno6SkBMuXL3f0GqRy0WYCdA3LwnZ4bduHWNfLly/HddddZ/hzGWexu/baa3H27FntSR6ANr7++uvTsjG1yjKnesLSy2RS/V31JMfZUIxVaOsw4KOM1zvvvDOmRSzyvIwsfrxmzRq8/vrrUV0tIrsUqMa0sDJjP7IuBfX19VpyTSAQAABtLMtY9Xq9hnvMxnNNz2TrHWMe2dqmPY4B+bqePHkytm7dyp0nKELY3Xfffdi5cycGBweRn58PANp4aGgIExMT2mfibUydTMFnVszJPqt6j5HuFdzhgrED2maMdgZ47LHHpG7UYDDomhImgkzoa2sVlYCj16zx8XHt4ZtenyniJpifn4/BwUHU1taipqbGcI9Z1TWdPuQbuZ6r2uUxmYeZta1CrOva2lrk5OTg3nvvZWFHoTF2TzzxhPQp/O6778bg4CA8Hg9CoVCUehY3EKONqWWCz+yFwgzxiDmGcSOq9k/AR03dRa2xyPp2brGq0djAq666yhUPfslAdqMbGxvTgu2Bj66dixYt0q67u3bt0h6+Fy5cqN3oQqGQNn7xxRcNfbeqx6zqmi4w+gCfCdY7ehwzbQ3LMCLggNhrW2iOWOuae8VKMJI8IStKCUC3l6DqoiETfAIr7gG9Mb1IsphjUgEz1ix6Aa2rq9NuoPfff7+WPLFs2bKo9yT7xlpVVYWBgQEUFBRgeHg4LZONVDc3Opbd6ABoMVr79+/Xbni9vb1R191EinXxe8SDuNkH+Pr6erzwwgvo7+9HYWEhDh06lJD9dgoziXbpJvxka5saaFQCjt6D7VjbLOwkmMmKlT11xWoYDcCw4DNyobA6Bj66SLpJzLndFSWL43LrvqYDdL7NdH9QuWUBeezdlClTpFm0iYZm7QrhqcoodNPNUO+GJl6LdXOjY9mNrrq6GtnZ2VFrIRliTg8zD/CRLuQlS5YgGAyitLQUp0+fTomYbaseILdXSjDyEELHqgcSADEFnEissWtts7CTYEe5EyNuISOCT2DWPaAa08X00ksvWZ8kh9C7IdM2T06KKZWAoycdgIyNkVHNDx2bOT5G5tvMRU51/tE19cYbb2jnxXPPPadZyj772c+ipaUFixYtQm9vb0JFPN1v+hv1ykbYdTOMtD7ZdUMTr0Xe3FTXKZGgQteASHBwq5gzgpHr+H/8x3+EfcZKzLYYq46jnWslnnCeRK/reDxbgPF4S5WBRrauVbognrXNwk6C3XXsjFj1gOgDS0UMIL95mh27qeelDD1BLLu42zU/qt6g4rsjT7pMznykAhyA4QcX1Zp28iJnRORt2LBBs5T19vaGBSkXFhYiNzc3KjYvWZZa2c0Q+MgiAFgP1RA3aEE8NzSavUdjjunN3oygp1nK6Xy+LV++PCy5w0rMthgLzApCI0LR7thsIyLPzP7RvxuxEqvmT3i2jBhOZGtbdV9x8l7Cwk5CogoU6wk+KmIA+c3T7DiVnnRl80Mv7iphbHWssghksoBT3XiFtSvWxVZWvFcE/kYW8aWJAnQbNPaNfr8Ym7XgqkQeLX3yn//5nxgfHw/7XEFBgWbRq6io0LW6643temgTGb+CyLJMdJydnW06w9PKDU1AE0GoGKb776SgT3WMZM3qWQBVx9FIaA91DwMIe9ihLuNIVyIQ/3lAM9nfeecd6XqmY7G2BVasxGbWthGPRTItzRkh7L7zne/ghRdeQGtrK3JycnDq1KmY7xfC7t5778Xw8LC0FpJ4gnIic1VWR8tIvaxMq6mlEsZWx5liETCDEcucatzX16dZwQCgp6dH2y59raysDMXFxXGLcbMWaLp+NmzYoImQ7u7usPdlZ2dj6tSp2r6uW7dO1+puZr/p32nWblFRkeHtCSLLMtGxyBKNvJbRsbhBxyM8hejPysrC8PBwzONr9wOUmZp2MouYE7XrrGIka1ZPYBgRTmI+Isc0wxg4t44E+fn5mD59uq0P06r3Hjt2TLqe6VisbXpM6W+xW3iqHkjo2G5LsyxMQlWr0efzpX+5k29/+9uYMmUKjhw5gl/84heGhd21116LM2fOSN8TWSfJqMnYLUGhDGMEJ8WzHdsrKSlBa2trTOueEUseTVoYGhoKu4lFWieoCKAZt3bMA83apQLSjnk1I0Lj8RZ0dHRgdHQUWVlZmD17dtwPmmbi/rZs2SK16MSq/wWEFygGrNUjdTKeTWW903vwiuc4ykS33eevXdtzysul2p7qgcTM2jYb9yfWtiDWus6oAsW/+tWv8NWvftWwsFu/fj3Onj2LnTt3YsmSJQCAd955B7W1tXj//fej6ti1tramZIFihklVjFgUjVjyZBfZnJwcDA4Oorq6Gh0dHVKX5sMPP2xZTMpQJU/YgRmRbvaGKzpz0PkzEzwfT9wfdRnm5eXFtOjQ2l+qAsVmatfFE89mR8cKAI4Jp1TyWjjl5VJtz44HEiNxf7K1LYhVqzGjChSbFXaRFrvbbrsNd9xxB2bMmIFXXnkFBw8exP/8z/9gcHAQ11xzDXJycnDffffh7//+71FVVYV3330XIyMjuOSSS3D06FEMDw/j1VdfxbJly5CVlYX/9//+H5YtW4aamhoUFxfj29/+Nh566CEA5w7+hRdeiL/5m78BAMyePRvd3d0YGBiA1+vFnDlzsHv3bgBASUkJ8vLy0NnZCQCorq5Gb28vTp8+jezsbMybNw+7d+9GKBTClClTUFhYiMOHDwM4Vzfr9OnTOHXqFDweDxYsWIC9e/difHwcRUVFmDp1qlZXaebMmThz5gx6e3sBAAsXLsS+ffswNjaGwsJClJWV4eDBgwCA8847D8PDwzhx4gQAYP78+Thw4ABGRkaQn5+PiooKHDhwAAAwY8YMjI2NaS6wuXPnoqOjA2fPnkVeXh4qKyuxf/9+AOfS4wFoNZHmzJmDzs5ODA0NITc3F1VVVdi3bx+Ac83bJ02ahKNHjwIAampq0NXVhcHBQeTk5KCmpgZ79uwBAJSWlsLn8+HDDz/U5runpwf9/f2YNGkS5s6di127dmnzPXnyZBw5cgQAMGvWLJw8eVI530VFRejo6AAAnH/++ejv71fOd0lJCdrb2wEAlZWVGBoa0uZ7wYIF2L9/P0ZHR1FQUIDy8vKw+R4ZGdHcnfPmzUN7ezuGh4fD3CYAMH36dExMTOD48eMAgAsuuACHDx/W5nvmzJn44IMPAADTpk2Dx+PR5tvv9+Po0aM4c+YMfD4fZs2aFXO+jx8/joGBAel85+bmStds5HxPnToVBQUFYWt2+/btOHr0KAoLC/Gv//qvaGpqAgD893//Ny655BIUFxfj4MGDKCkpQU5ODo4dO4bZs2fj+PHj6OnpwcKFCzE8PIyf/vSnWL16NQ4ePIjHH38cH/vYx/Dnf/7nGBwcxD//8z/j7/7u71BaWor29na89NJLuP3221FYWIjHHnsMixYtQk1NDYaHh/Hv//7v+MIXvoCCggL09PRgZGQEM2fOxIwZM9DW1oa33noLN998MwBg7dq1WLVqFYqKilBYWIg//elPmDVrFmbMmAGv14utW7fi5ptvxsmTJ3HfffdhzZo1mDJlCv74xz/i9OnTuP322wGcc2fn5OSErdlkXSOam5sxODiIN998Ez/84Q+jrhE7d+7EK6+8guLiYlx88cU4evQo+vr6sGvXLlxyySXIysrCv/zLv+DWW2/VriMvvvgibr/9dsyYMQO//OUvMXPmTFx44YWYMWMGnnjiCcyfPx/z58/Hnj178PTTT2PNmjWYP38+nnzySVxzzTXIz8/HwYMHUVRUhLq6OluvEX/4wx/wz//8z/jyl7+M0tJS7Nu3D7/5zW+watUqzJgxA52dnXj22WfR2NiIo0eP4t///d+xZMkS/Nmf/RnOnDmDjRs34pVXXrF0jfjKV76CK664Avn5+bjpppv4GqG4RvT19aGvrw9ZWVnaOpmYmEBxcTGKi4vD5ntgYAAnT54MW7NjY2PSa/LZs2fD7muPPvooTp06BZ/Ph4MHD+K8887DjBkz8G//9m+YPHkyPvWpTwE4J8BXrFiB0tJSHDlyBFlZWdJrxJkzZzQd4fP5cPjwYTz//PN45JFHcPDgQZw6dQq7d+/GihUrMHPmzDB3tc/nQ3V1Nfbs2YPh4WEWdhQh7GpqajSTPgD4fD74fL6w9xrJuIunQLF44mOLHsN8hF5guar+HSC36pWUlCjjZ+m5Wl8f3opMFkdYVlaGvr4+S9mKtJ7eqVOnXG31V/XmFdYMVTkM2W8xEryuOqZuT7RQXfOtZo+ma8cKlfXWjfc+M2tblcCiyg63Y22nbPLEN7/5TXz3u9+N+Z7du3dj/vz52r/NCjuzWbEqkWfGFUQPvqw7RCoseoZxGpnL0sjFUeU+pBdnIa58Ph8mJiaixBzNoFXF2Iltm81WfPnll6UFit34EEjFNZ0TIHZ3G6viRlbj0q1izkmoqEh0v2MnO0XQYsWA/EEm0fc7Iy3A3Li2U1bYdXd3a+ZQFTU1NcjJydH+7bSwo9jxVKWq6wO47+mdYZKBGTEXGQfX1NSkWceqq6s1i93u3bsxMDCgfYfH48ENN9wgLeNid5cKIzF2Zh4C7b4O0PmmmZWy+GLAnu42kfXAVMWbY8Xp2Z0VS8eJvtZSAUR7idLMT7utd2KO6TqTtQiz4ztiPcgkSuTJfi8gtzTHa3lVWbzjIWWFnRUSKewoRuoR6WG2RY1VwZduvfuY9ETWq1n1pEvPP1pOZGhoSKtNl5eXF/U6RZRBifyc6FIRb1au0d+rKsswPj6O9vb2qLpfNJvXaqcZek2gPU3Pnj1rq9VCxaxZs7RYV4/HE9N6Iqu95kRWLB1HutycFn6qB/7c3Fzt2Fx99dVxf78olEzXlMBoCRE71r6T3Sn0LHKAvECxHevaDl2gIiOEXUdHB3p7e7F161Y89NBD+P3vfw/gXOB9QUFB1PsT0XnCiUVvRvAZydSx+4mMYeJBZp0TNdIixRx9Ly2m/Pzzz2uiLDc3VxofJ8Y5OTkIhUJRLcfo52gsXVVVVdwdOFQ1yvTiBGlZBlr3i2bRie80YqGi1wdVCZHrr7/e1hsdvclSC1ukqFD1VlXVXnMiK1Z2raX7ByTGq0Kv/0899ZS0oLCZjiR03qnYiFxLZkumRJ6H8QoxlcizoxWeXRa5yH2OnF8hjJ0IJ8gIYffFL34Rjz76aNTr//u//4vLL7886nUnO084qdIpeoJP4OSCZph4MRNUTC+gsq4X5eXlCIVCpurEiW3TOnci9i2ytAdNsKCxd3qt8cRrkdcE2rmBduaQxfWpyjI0NzdrgmbHjh3atoUVy0gFf1H2JT8/H9dcc03c1weVlYQKyEjrUKTrlAo4Wig3GR1jIstaOO1V0duPhoYGbN26NUyIGekqoZr32tparFixwlI5Ebr2RX1DWiYont7fqtZ6st+rGtttaZatbbFfkfMrMpmdICOEnVmcFHZO1qkygt5FiMUck2ysijn69O3z+aQWNirmzJx/qi4VNMZOFgCtqjVmpD4WFZO0c4Mq414P4VqjdTiNVPC3w7JgJItQdKrw+XyYMWMG2tvbUV1djezs7JRu+RdPGI1VkSfWK3XRA7G7SkRaNl988cW4fzs9b2hC0Y4dO5THNB6RZ0crPLPorW2BE/OrgoWdhET3ik2HVHWGiQez6f6y4GYaSH7w4EFdMaeKW9M7F6ngEvW56PaMlj4yOiexrCB2b1uIVtrT12yCiKwwK73h0eMEQHrMZEI23Vr+GfGqyNyoZsQetfoCiOrdmyxhLCsobMe6dhKrGbKLFi1Cb29vQufXjLDLivlXxjTiBrZx48Zk7wrDJBV6LqxevRp+vx9r1qzBqlWrEAwGsXLlSjQ3NyMQCKC5uVm7mApR19DQoLk2ent7sW7dOvj9fqxfv177jlAoFLYN+p10LN7T2NiovZdCt023R/eV/gbZtiO3KYNuT2/b99xzDyoqKtDY2Bi2Db3vo8/qoVBI+3fk//VobGzUvl8cm/b2doyOjmLLli1YtGgRAGg3OeDccRKFX9vb28PmVbYGNm3apM1BOrB582Z0dXVh06ZNYWMxVwC0+RNz2tLSEjbXetDjKDumqrHTyI6pHeeM3cjWdUtLS9i4vr5euwbR41hXV4eqqiqsXLnS1euWLXY2k2y3LMMkEyPp/vQ9tDyJiDkTQkHl8oyVeKBy58n6TqosCEZiZmWWPGGVspJFKEsMUZU7obGGwkpjZE5U1jEj8XEimUFY/WLFwdHeuNT6mcmINSWgLrzIrGeRwBJ5PEQcJi0Ro7L6ArETfZzIdNXDbuu3EWRrO55ajImKp5fBrlgJiRJ2FHbLMpmAyuWquvDRiyPt8iDcq7Tciao5utl4LDNuIprpR8udqM5hWZkWQJ48oXIVy0RZTk4ORkZGomrK0VjDioqKuJMMysrKpEH3tEerEHE0Y1kWXO9E/a50QG/90aznoaEh7RgIq5zH40FBQYFW+kS0hJR9h1HBR8eJFilOijwj8XFmajE6WZvODCzsJCRD2CU6W9ZsWriZsRPb5pp6qYHqAcVM3TlVqRJZpqlMPDhxUVXdXICPbni0Rp5eXJrqxqqqv0e/RybK6LzS9mg0bo1m81ppQL969Wrcc8890qD7Bx54wLBQTKYlI1VRrZe7775bWtqGYiZOT/U9bklQsSryktFBIplrm4WdhGRa7Jzu/yeqlgusFOTUGzuxba6plxqo2kyJ1kdG6s5Rl2tRUZHhenBGLIB2oLr5UbeiVRFl5HtkN1a9G57ZG6GRZBbakk3vN7rFkpFuyASLrNwJLWUTq9QKHbv9YZqu+W9961vo7+/X3P+RtRrN1qsz40Fz49pmYSchGcKO4qTqt5oWbmbsxLa5DEtqQC+2d955Z9QFVBYTFynmVJ0dZPFiRiyAZvY7nocp1W8XNbuoBZLOg90Pb6rYXSO9rK129JAJRbNudyYcPet3rHkV7zly5AiGh4ej4vRkLeCM1Nmj3phECT8jLeNEjCeFdhOxGh9HY2HNHoNkwsJOQrKFHSdVMOkALeewbdu2qDIaAKRijtad83g8MVuAidixeM8VmUgx0h3CzG8vLy/HqVOnwqzaquLDkW5owF6riqy+mBHLqmobMmugXaI7U1EJZ5UF1Ywll7rrhWXLSJ09gVGrn97YSNiOytpGx+J9RUVFOHHiRFiHESvt9GI94Jg9BsmAhZ2EZAs7ASdUMKkMXb8bNmxAT0+P9jdVG6/IunPUvXn27Fmp+NPLptTrsxopooRlkIpQPZdwrLH47WVlZZg9ezaCwSCys7MxPj4esy0ZrdQPOBeiQBMiaJallTg8VfyjW254qYQR97rdRbYpsjp71BujKrBsZiyw0h1Cz/KmZ1E2O2d2HINEwcJOgluEnZlSCiz+GDegF5tFM/2opUjWJitScG3bts1SBwlVEoJqLLJvBZHfZyRzkI5prKEQjT6fDxMTE1GxUUuXLo2ypDkRoqBKiIjMaLVyXWGPg/3YPae0yLZY22aPtarAst1hO/GucSeKert9XXOBYhdDCzbSIo2qIqsMkyiMrEdZkdmlS5cCgOYmGR0dRTAYDCvA29TUhLa2NjQ1NYW9hxawpe/Xg+6HkbH4nptuukn6fWa3t337doyOjmL79u0YGhoCAAwPD2N0dBTPPPOMtp+hUEj6u2IVlBUFVBcvXmy4aC2AsDl+4IEH4Pf78eCDD2r77fF4pAVijYwFr7/+etIKy6YDsjmNLLKtdwxU0HOJrgVakFc2lq0zIwWP9QohG1njqn2KHAvMFgyXjWVFyiOPQarDFrskYqT0gdufIpjURmWNA4yvR736cpGuSz3LnBE3q2ocb5KEFVcsgDBrIADN5evxeHDixAnt9dLSUu11ANp40qRJMV1aRtyzqrZokb/RioVSjO3qA5pp6NU6NDLWC+KnCQnPPPOM1qN3YmIibtepKOobuW5lY4FqXZsdi+3EikU1s7bpGlbNtRvXNrtiJSRb2ImFR4O3VfW8WMxZQ3ZzpvPtlhM0GaiEC81cNdtrMnJNR76X9rSk29br7RrPzU8vVk6VPKEStXrfQ+dKNGcXrmmBaLcFACUlJdK5rK6uRnt7O6qrqwGca8eVn5+PkZGRmK4rq2UZjNQ2k41TqQ9ospE9NOkVeDZba05VjHd8fDyqa4gqqYK6Tt94442odSjWphVuvPHGuF27tKgwIM/wVYk8vTWsmmtVhnkyYWEnIdnCTlg1VE8Lblk8qYzMAqqyMADmLEGysUok2DXWq0VmZnsqsUQ7Pxhp/6RXEoDeaGTxc0YEVTw3P3pBpsWFARhKnojnJkuhsUpUlOk1iZdVyI+V9JDM4qnJaBGlt/7NnpNO7p8sHtWOB3eVmFMlJOzYsSPmGtHr1GA02UEmFO0oaWXknFGJPKu4MfaOhZ2EZAs7WUsZVUsefuI1jspioWdhAMxZgmRjIyZ9q2NV31GzlqXI7RkRLrFufjK3a0lJCVpbW5X9RQOBANrb26NEVmRZEMBayQ9VVwsqLAFEJU/QgsNGWoc5iezmRXtaUlesG4unGhF5Yl/jEWJ669/sOenk/tkdUiPWiKrXqZ5VV3WfEQXu7erUkCjoOeOkyHMLLOwkJFvY6WEkjVv1tJquQjCeYp6xtmfFEmTVpG91rLKwmbUsmbEyqWI+aQYoDRkQGZ7CuuTxeJCTkyON7SkuLpaWQSkuLjYsalVj1c1eNldGhL7qZp/o80wm9mhdOrda+u2Od6JjvfVv9px0cv/sFHPUmgaY63VKkXWRsdvClgwyQeSxsJPgdmFn5IlXdQNz48XdDuwo5pmqmGk95cR30rIlMnePqjJ8WVlZVAuuyJIfVkWtaqy62VuNMzMr+MTYbtc83d5Xv/pVLUj94x//uOUirYnGjngnJ9e/G/fPiKvVqgBbvny5tnbeeecdzboemfxjF2ZDR+x4eEpXkcfCToLbhR3FSAHFRN3sk4mZCuvxVEE3O25ra0MwGNSadAcCAQDQ9smOMd02/Y3UZQhYuziqLraygr7l5eWorq6W3gy8Xi+Gh4e1rLVIAWd1XaoEpup42O1GNSL49OL3xGvxjOn2uru7tf3LysqSurtXrlxpWCiq5slMDJudc22XGBYJLEauDUbHdooA1e997LHHpOeYKC4dj5hTCcWcnByt1uHhw4dt+40Us/UmZQ9P8cRNUpH31FNPhT2Ixjpn7F7bdmyPhZ2EVBJ2FDuCOI305dMbqwRSop58VD07gfiroKvGNLMMgDaOtFI5Db2R5+fno7+/H4WFhcjNzTUct0aPHxVt9HOiNVak63R4eFj7zomJCe1mkJeXh56eHq0FWLw3ZyEsjeyraqyK2RNjVV9bM/tnJH5PvBbPmG5vaGhIm/exsTHN3T08PKy9TwhAI0JRZCnHE8OmZ8GMJ6nH6phC+4oC1rsoCGuPeKCL54FNnMuFhYUAoJ1X/f392n6Lh7r8/Hw88MADcYsDVQydeAhzsnKA2fAXmbWcrjnZA1XkWLW2RdFuipj7yOPR1NQUt3Wenkt2eNVY2EmwW9i5Pd7NSKaTmbEg3vZHdL+sikIz1dGNVEHfv3+/9GK8a9cu7QILQBuLxtviAkx7GNo1ptt+//33te8eGhrSRN7kyZO1dlwAtPGZM2e092RnZ0cdP1W8m2iNFWntUCVEiPf4fD4MDAwYvgmrLs6RPVdj7atqTFt5Aep1HJkVa2S/6Vgvfs/JGE7aku306dMYGRlBTk4OPB6PJvgKCwtjCkXar5ceA1XJG6eSkeLJgJaNacmZmpqauEtt0EQFun7iJfJhUZxXtbW1WLFihVTomBEHsod5I226kh3WIxOCdM3RtQ/IrwF6a5uukbffflv68D579uy4Yy/tDhMyI+xi/5VRItT4xo3nukPQsZ1xOGaak1NLhxBzVNxZucCJukaiD2ZlZSWWLl2q/d0sdL/Ev+l+mxF7oVAIdXV1Yd0PIsdivlV/X7p0qVZzjNYb6+3t1Z6Wk+36lrkmGxoa8PLLL2NgYAC5ubkAoI1DoRAGBweRl5cXJUIXLVqkCbJly5ahra0NPT09mD17tlarqr29HXv27MHo6CheeOEFVFVVob29HVVVVWhtbQUAtLa2ore3F6OjoyguLkZFRYW2fx0dHZpIlo23bdsGILoavVinVIybic2LnB/Zmp6YmMD4+DiGhoZw1VVX6e4rHUfWmqTnpuw8Fa+JSvah0LkuFKrPGd2e6rd/9atfBXBOwNIi0KobpOwYGB2HQiEEAgHdeTIbl2Z2bvTmjF5PrIzpQ6R4ADT6kHb8+PGomnA5OTkYGRnBkiVLAJy7Bi1ZsiQqVlJ2rM1Ar7NdXV0xr6tUgDjZztLItmOdH4B67Uc++Ij30s8Jli5diuzsbKxYsUI7XpHHw+/3x1zPRh7iIq8RiYQtdhYxG3gtxvGk48tiByKzpQBEmdxVsRkqF+2bb76pXZAOHToUdlJ4PB7MmjVL+7sQeUZdtLLAVtl+x9oGdS3k5uZq5vNp06bF/WSVSrGLRtwcYj2q3Bkq9yu18FCX1qJFi6IsdtS9qefKc3q+9WK2urq6MDAwoMzIjTW2as1IlEVElUUrO4+cvH4l2+qTTPQ6uSSqjqmqjqIeTq7VRJ0Hemvb7D3YLeuZXbESEhVjZ3eWlSppoLW1NSpuwkja+uLFi6NEW2RrGDNLgrZOotug4m/nzp3Sz0ZaGiOzmKiIoE+0soBYVSC50SerdEWsR1prjsaH0cxVKgaee+65KNdzIBBAb2+vobgrp+dbJeD0hKXdlf/N7Gsi1xptXXbDDTcYDn+w4/qVzueTHnrt9VTz55Y506t5Z3V79DxN1u81WkvWrceGhZ2EVE2eoOnp9MlB/FtV8oBa46gFzkhrGNreSFjkVG1nrG5PdZNR/V7Rmibyc+ma2m4Xsh6VshvO6tXh7cVoEgKtm6Z3szJ7EdRrA0cTFoCPLMKqpAozyRNuiYd1Atq6TNW3lrGHZIoXO2KWVdhhYXNT/F6qw8JOQioJO1Xig7Bg1dbW4sSJE1GWvNLSUpw+fRr19fV46qmnpBY4mciKp8q4KpFBJf7oTUZYEoqKinDixIkoi5D4XbW1tXj33Xd1RZvKzavXlimdERdWmaUqUswNDQ1pCRh5eXlSsSQrLRJPyQr6/UB09iZ1FQOQuo2B2AWPjWR1ppvIo+fCf/zHf2iv0zhSxjpWi6TbDQ1LMZvIpodV652brHTpBAs7Cakk7FTp6VRwqSxbMqiYU7lFnYS6f/UsfCpLpFnLXKz2O+ku8vSsYCoxl5ubqyuWaHyeEHxZWVkYHh62VLMtFAoZavsV+eBgNqlCL6tTdQ65PfvdCLLkqnRe/4lA5XJNtHixGktnFjOdkah3gK109sHCToIbhZ0qS8jIyUrFn7B4UahAokUYk3FDor+TFuKMPOljWeaAaLdarJg88ZtFbFk6umpVPVKpm1Kk+B85cgTDw8MoLCxEKBSSlklZsGCBbj2u3bt3R5VVEVip2aaXvelUp4HIOVMVflbF7OkVUnWj8JM9MKby+k80mWyJMtMZiXoHMmFuEgULOwluFHaqpyBA3zJArWCdnZ1h2aWRrlogMX0wzQa0U1friy++CEBtmQMgFXAApEJRFJ70+XyYOXNmlKiUJZ8kq/iyGegcU8vbiRMnojpC+Hw+5OTkhBU/NVv/LpYLVBTNzc7ORlZWlrIjRCIDk/Xcwna1K9MrpEr768rmxIir2q4WZeLcNmL1pvvqxvWfaNzicrULWSUEs915aKHmPXv2aFUJ5s+fHxUSxGvIPljYSXCjsDPSLFslxO66666Y7tpk9MHUs26YvalTdzMQW8AVFhYiJycHJ06cQGlpKU6ePBlmTYqsLC4uQpEtfKigSeYNT2VZeuGFF7TfMDIyogk4ANJuBJGdMqqrq9HT0yMVdqL+XX5+Pq655pqY9eDonIRCIYyNjUXN8dVXX617k5AJnXgEjV5WbEFBAYaHhw3fxFRCTJbUIasjGdnBpLS0VOqSlhVLNlKW4fjx49LK+XQsqujTeVU95NB9lWWZ212H08m6afGgl3SUqpYoarUF7O3OIyuGbnfcn1n0wlLEa/E8MCUKFnYS3CjsKCoh9q1vfUt6sabCxEwjcCfrV9lRn4yeiGvXrpX+dirgqAua1lsTfRBVCAHk8/kwMjKiJXTQButU8AHRVeKd7BVLWw8NDAxIE2FoqzFqpRsbG8P4+Diys7MxZcqUsDnyeDzIycnR3uv1ejVh99nPftaUa1Lc9Oj+0HlSWf3oeMqUKTGTJ8x2h6BrkPaZFb+nr68PPT09cbew0usVK+IPGxoasHXr1qg2bJEuaRFLaNaS8uSTTyrXuCBS7Pt8PhQVFUl7Af/hD3+QPiD4/f6463DSdSTGQpjGymhWrUUnC+gKa7jP58PExIQjPYmTgSzZzUh3HjNjI2W37EZlqddLzrLafUa4mxO5FljYSXC7sFNRVlYm7VFKLVW0r1289YYSWb9KXGRoViw17Q8ODkqFFRUM9AmRxhVadZVQsRQKhTRBlZWVpYml8fFxS9u2G1HFXvzfKPQ3lpSUSFvxqC54VDiJ9j45OTkIhUKWbgYvv/yyoeQJKxY2mevMbO9KKg7p9zz//PNRSSd0v0XsYKySLXQsK98ijlOsG82xY8ekbe/oWPaAI4pQRybHCOEb2a7sC1/4QpTYNOJq13tgFL8xsh0ctQSp+iPv3bvX8JpXoQptEEk9qmOQqq7YdIWGNQGQrmfA+PVFb20nI0GEW4qlEbSdE80oFRfrwcFB7Qa2YcMGywLPaKsjo383guhBKixKwlIFnPtd119/vVQMiNY+ixYt0j4nSqUA50o6WBVgQjxTER0KhbRtybaZnZ2N7Oxs7SYIQDoeHx/XxKHYVnZ2NiYmJiwVhxZiLpaoi5wH2jEiMruUtuJRtYui60Ql6s20btLrDfnII49gdHRUu3jqjUXNPSruaDsmuv+01ZcgchwMBqXfk5ubq7Vwmz17Nnp6ejQRNXv2bO2c3Lhxo6H9Ft9Lv3/SpEnasaDHgI6NiKvm5mZpb2PRSk64WVWi9gtf+II2DyKeNxgMoq6uTnf+Ii2okb9BCEXR3k6c18Jy6PV6tRjR/v5+bftDQ0Nh8WJiHoy42agbur29Hd3d3Whqago7BkKYqx5oVbjVtZzuGCmMLsYioW7ZsmXK83vz5s0x74f0uuVG2GLnclSlT2SlH2TxIG67wNAL3z333IPBwUFN0EQ27qZmfL1WPSpXsKopOK3tRcvB9Pf3S928bkHcnCOtMmfPnpVaFIVlhloe9NxjRmMxE7GuZH1yIwO4gY/EvRCtRuL39OLxYoUX0H0R8XSCyPhMKr5k7mEjVgHV/lm1Fqjqn1GxRK2Iwl1bXV2N7OxsrFmzBhs2bNAtZ0Pdq2asktTSIpJ0Il3Zvb29UbGL1M2m2icaPkAtlzRDW+Z9MFL6JhMK8hqpW+mWe44sVpmWZ6JhCPQBx40xlGYsdlkJ2ifGIvX19fB6vWhoaMDmzZvR1dWFTZs2oa6uDlVVVVi6dClWrVqFYDCItWvXwu/3Y82aNWEWg+bmZgQCAc1CkWjo99P9euCBB+D3+/HII4+gt7cXL774YtjvUn1u9erV2u+kYzEPovlyMBjEzp07tW3T+bvxxhvh9Xpx0003obOzEwDQ2dmJT3/60/B6vfj0pz8d9h4hIGpra7WxeEAoLS3VxsJ6JxuLtmsejydsLERafn4+SktLw7ZNvy8QCGD69OkAgOnTp4eNp06dCgCYOnVq2H6vW7cOfr8fN9xwg3Rt0DGdP9Ucy9ZVY2OjreuLHvc33ngDHR0deP3117F9+3aMjo5i+/btaG1tBQC0traGjel7mpqa0NbWhqamJm28ZcsW3XWkWlOCSEueOEerq6u1c5X+nW4j0kIgticbi+0FAgEEAgHpmM6V3pgeJ2HtXrRoERYvXoySkhIsXrwYW7ZswejoKLZs2YKhoSEA56xjYo47Ojq0/aQWLtVYiF6RZBP5nkmTJkl/l1i369ev164TDz74YNjreXl5AIC8vLwoi2esfaLnMt0ePU6yOVOdNxS6dtIV1TxQz5FsHSYDeg0Qa1HEjoZCobBrhyAdbF3sinUhkW1iZFmYkRcWcaMSNwxZnEvke+k4EeVOIt1jQoCJkz/yvQBifk5gZd/pvFLrpygDQeMuQqEQ/H4/WltbUVNTY6qpupFxLOvAihUrtLkQrbCMbI/uY+T8qD5HUbnmZeuqo6MDo6OjMdeXaiyLPxNP1E1NTVrniZaWFhQXF2v7oLJev/zyy9p76O8VRLo3Y703FApJLVhNTU246qqrwtyhNLZz6dKlCIVCYW5HAXVxq9a6GIt5Nep6lm1Dtj1hDQSguSLFWFiE8/LywtzNosahx+PRbpTCBT179mzNUrps2TLtOEa6V2UuX2GVjPxd1M1rZC3GEwss66Aijg2dM9W5F7ktt1irnEIVpiHGw8PD0nWYqHmhx5EKenHNuPjii3HixIkwC3pDQ4PynpqKx5NdsS7ESJsYWakUmqkDWHex0c/KFrXZenWyC6KqtpgdmbXxYKSOXiJS+N1S2T4WsqbaesHykWNx45SVX4jsPEHjAVXzoCewjbg3acYrbWlGa/jJWpepMkYjezyL12mtO5lb1myzcr0x3Z5IfCkrK0NBQYEWilBeXi51TYnjqkp2qKqqspQhK+KdYiWTiCLbdj2MGrl+iX87WSw7lTASCiPG+fn5GBkZiVrXiZo/q9dOVfkxt7jUOStWgtuFnaxwpNF0cVnNJUB+QZVV2zci+IzEJumVmlB91q2VylV9cBORwk8vMm6aEz2sZp3SOaZP1E4VMI4VM7dt27aoMiRG2piphBj9DUYq+Cc6dpHun+qhUibi6bEz0uJNr6izeC0yxo5mawPy+D0gukSM1W4iqXbOOYVKzOl5DZId623HtdNqn1wnYWEnwe3Czo5mzvEUJVaVZBBPzrIaXUZLTci+M1kWOYaRkQwLqRstBE72HtXLgFYJQmpdBD6q/UcthkB0sV2VFZGvQcZIJcuXk9nIbkmIYWEnwY3CLh4rnRmsFiWmT84ClRuKL55MKpNsC2myv19GZKxvstCzsqqK46qsiG6ZXzdidz/cRFm+nBRfbrHesbCT4EZhZ4eVLh6MuKWA2FXwVSUb+OLJMKlNsq9PTGJIVD9cVW90O2Mlnb73JNN6x+VOUgRayiQZGCnlICtMqirZINsewzCpSbKvT0xioNmgTpZrUZVNsgrdRqLuPalSzoaFXYJpbGxERUUFGhsbw+qqJQMzdeIaGhoM1zVLVs0ihmHsg16f6HWLSS/06jbahapOptX7RjJElvgNomSSW+917IpNMMl2bxjJdDJzUrsxAJxhGHtJ9nWLSV/MuDfd0rItGS5ZdsW6EPHEW1lZmXD3hhHLnNUnNdVTGMMw6QO7ZRmnMGO9s8OFawd2WBydhC12CSKZT7ypUOyWYRiGyWz0EizcmD2eKOsdW+xcSDKfeBMVQ8EwTHrDsXaMk8jit2nvWTfev9zoqWKLnYO4pQ4UwzCMHXCsHZMohHXu+PHj6O/vR3l5Ofbu3Zvs3YqJkzGAbLFzCS0tLVoDc4ZhmFSHY+2YRCGscz6fD8C5klpujGejuCUGkIWdzVBXBV8EGYZJJ7gECpMohIhbtmwZ/H4/1q9f7xrhpMItbll2xdoMuyoYhskE+FrHOIksKcEt7b2MYLdbll2xSYStdAzDZAJ8rWOcRGb9oskTbi+On0zrIgs7G3BTNwmGYZhEwNc6xm6oQNPLgHV7B6RkumXZFWsD7JJgGCaT4QoAjB1YrQnn9g5Idrhl2RWbADhJgmEY5hxcAYCxA6tWLrv70NpNot2ybLGzCFvpGIZhziEsdg0NDeyaZUzhZO03vU4WicKOjhlssUsAbKVjGIY5B8fbMVZx0pql18kiUVCLYiKsiCzsTMBJEgzDMLHh+naMGZxMMpC5aD0eT1ITLRLhlmVXrAnY/cowDBMbvk4ybkaVaJEoV63VWnzsirURTpJgGIYxDl8nGT2SmdSgSrRIVMkUVS0+O2GLnQ789MkwDMMw9mG1rImTJMOSZyapgi12ccJWOoZhmPjheDtGhlt6qlKSYckT3xkKhWzdbkpa7Nrb27Fhwwb8z//8D44dO4bzzjsPK1euxLe+9S3k5ORIP2PGYsdWOoZhmPjhaykjcLKsiZPILHllZWUoLi7G6tWrASDu32XEgpn2Frs9e/ZgYmICP/3pT/H+++/jBz/4AX7yk5/gnnvuiWu74umysrKSrXQMwzBxwh4PRpDM3qnxoJdZq7LombHu2V1UOSUtdjIeeugh/PjHP8aBAwekf1dZ7GgrHFE9nZ8uGYZh7IXbjmU2dhTpdQuyzNbI2DwA0jg9ILaFT2W9M2OxSxtht3btWrz44ot4++23pX8Xwu7nP/85RkdH8eyzz+Jzn/scnn32WYyNjcHr9WrijqunMwzD2Au7ZTOPVHW/WsWq4AsEAggGg1i9ejXeeOMN7QEIgDb2er249dZbM0fY7d+/H0uWLMHDDz+ML3/5y9L3CGF37bXX4syZM9rrf/3Xf43y8nJkZ2fj//v//j8A5y5AoVAIx48fBwBccMEFOHLkCIaGhpCbm4vzzz8fH3zwAQBg2rRpyMrKwrFjxwAAfr8fx44dw+DgIHw+H6qrq7F3714AQFlZGXJycvDhhx8CAGbPno3u7m4MDAzA6/Vizpw52L17NwCgpKQEeXl56OzsBABUV1ejt7cXp0+fRnZ2NubNm4fdu3cjFAphypQpKCwsxOHDhwEAVVVVOH36NE6dOgWPx4MFCxZg7969GB8fR1FREaZOnYpDhw4BAGbOnIkzZ86gt7cXALBw4ULs27cPY2NjKCwsRFlZGQ4ePAgAOO+88zA8PIwTJ04AAObPn48DBw5gZGQE+fn5qKio0CymM2bMwNjYGLq7uwEAc+fORUdHB86ePYu8vDxUVlZi//792nwD0C72c+bMQWdnpzbfVVVV2LdvHwCgvLwckyZNwtGjRwEANTU16OrqwuDgIHJyclBTU4M9e/YAAEpLS+Hz+cLmu6enB/39/Zg0aRLmzp2LXbt2afM9efJkHDlyBAAwa9YsnDx5UjnfRUVF6OjoAACcf/756O/vV853SUkJ2tvbAQCVlZUYGhrS5nvBggXYv38/RkdHUVBQgPLy8rD5HhkZQU9PDwBg3rx5aG9vx/DwMPLz8zF9+nS0tbUBAKZPn46JiYmwNXv48GFtvmfOnBm2Zj0ejzbffr8fR48exZkzZ+Dz+TBr1qyY8338+HEMDAxI5zs3N1e6ZiPne+rUqSgoKAhbs319fejr60NWVhbmz5+vhVwUFxejuLg4bL4HBgZw8uTJqDUrm++zZ89K12xBQQGmTZsWc80eOnQIw8PDmDx5MmbMmKHNN18jzF8jXn31VfzoRz/CunXr8LGPfYyvERlwjXj++efx0EMPYcOGDbj00ksz9hpx+PBhPPjgg/jGN76BSZMmIRgMoqamBqdPn8bg4CB+//vf48yZM/jCF74AAPiXf/kX3HTTTZg6dSqOHz+Oxx57DN/4xjfwyU9+MvWE3Te/+U1897vfjfme3bt3Y/78+dq/Ozs7cdlll+Hyyy/Hz3/+c+XnhLD7xS9+gZGRETz77LP4/Oc/jx/+8Ifw+Xy2/QaGYRiGYdLL/eoEYn5qa2uxY8eOKEvf66+/rnkRJ02alJoWu+7ubk01q6ipqdEyXz/88ENcfvnlWLp0KX71q18hK0udC2JH5wmGYRgmfjjeLn3JNPdrosiIGLvOzk5cccUVWLJkCR577DFdscbCjmEYxh1wvF364sbiw+lA2pc76ezsxOWXX46qqio8/PDD6O7uxrFjx7QYFoZhGMa9cBmU1EVVjkO8HggEXFd8ONNISYvdr371K9x2223Sv6l+DlvsGIZhGCY+VBY5ttQ5S9pb7L74xS8iFApJ/2MYhmFSA245lnqoium6sU1YppKSFjsrsMWOYRjGXXCsXWrDVrrEkfYWO4ZhGCb14Vi71IatdO4ktuxjGIZhGIfYvHkzlztJAzLE8ZcysMWOYRiGSTpuirdz0764mY0bN6KtrQ0bN25M9q4wBBZ2DMMwTNJpaWnB6OgoWlpakr0rrtoXt6FKmFCVQWESDws7hmEYJum4Kd7OTfviNqiVbtWqVQgGg1i5ciVb71wECzuGYRgm6WzevBldXV3YtGlTsnfFVfviNlQJE5xI4R5Y2DEMwzCuIhkxbhxXp4a6WamVjqJ6nUk8XMeOYRiGcRXJqG/HNfXUcL265MN17BiGYZiUJRkxbhxXFw53lUhd2GLHMAzDuJbGxka0tLSgvr7e9pp3Tm471WErnbtgix3DMAyTFjhZeoTLmqhhK13qwsKOYRiGcS1OukjZ/RqOkSQJxv2wK5ZhGIZJCexwnbL7VQ27X90Lu2IZhmGYtMMO1ym7X9Ww+zU9yHhhx21QGIZhUgPqOjVTd46+l92v4bD71V3YUU8xI12xTzzxBDZu3IjVq1drbVDY9MwwDJM60Lpz9fX1Ue5V6nIVVjquURcNu1/dhWpd//CHP2RXrIrrrrsOa9euRVtbG9auXYuSkhIA0P7PMAzDuB9qeduyZQtGR0fx5JNPoqSkBMuXLw9zubKVLhphqQsEAux+dRGydb1lyxZcd911hreRccJu586dGBwcBAAMDg6itbUVALT/MwzDMO6nrq4OVVVVWLp0KfLy8sL+FgwGw26Q3Ps1GuGtCgaD7H51EbJ1nZeXh507dxreRmx7XhqyZMkSVFZWoqWlBQ0NDQiFQtqYYRiGSQ2EMNm4cSMeeOABbNy4EePj42hvb0dtbS02b96ckKxX6vIFEHNcWVmJzs5OQ+91chv19fUoKSlBW1sbTp06hYqKiri3Z/f+uXl7dBt2rzHZul6zZg22bNlieBsZGWPH5U4YhmFSAyqc6urqtPhoANpNz6q1SU+UGREBNH4PQMyxwMh7ndjG+Pg4JiYmkJWVhezsbNv2ye7f6Pbt0W3QODjAmlBctGgRent7Y65rM+VOWNhJ4DpHDMMw7oAGk+fm5qK/vx+FhYU4dOiQpe2pkioAayJAbCfSAyQbC6Fo5L1ObGPr1q0YHBxEfn4+rrnmGtv2ye7f6Pbt0W08/fTTtonNWOuahZ0EM8JOL9uKYRiGSQzLly9HMBhEIBDAO++8o1mcrr/+eqkVRJYVS/9OxZyeKDMiAtwet9fc3Cy1cr7++uuuc3Wm4j1WrLN4hKIQd1lZWejp6ZF+Dws7CVYsdpFqnEUewzBMYqHlOAKBgPTaLFyM9DOtra26Fja3izI9jMT3vfDCC5qV8+qrr44SuAI3uDplbk0jQjHV78dUc6jWJAs7CVZj7FjkMQzDJBaVlYnGHFFL3q5du7RqB4Ibb7wxJS1sZuL+VK5kGksHICqujt7D3OTqlLk1BWYFYboJQRZ2EuxInlCJPC54yTAMYx9GiubS94hi8zQr9sUXX0zwXicmGUMQy5Usi6VLBYErc2saEYpm4tyAj+YOsO56TrQ4ZGEnQQi7e++9F3/zN3+jPQ2uWrXK0vZkCzBVnwQYhmHchLDYRVrp9Cx5ZkqPOBEvZkaUyf5uNu5PJc5U85eumIlzGxsbQygUitvdbIc4FGMj6+zIkSO49957WdhRhLC79tprkZ2drcUczJ8/XzPnr1y50pLgo8kWbL1jGIYxDxVtquuvypInPnvw4EFMTEwkLV4smckYRuaPsSfZwQ5xaGSdUZd6bm4utm7dysKOIoTdfffdh//7v//TJosG3Pr9frS1taGsrAzFxcWGTxAjgY8MwzCMGj3RFiveTnw2Pz8fIyMjSYsXS+b1n3u+Jg47xKGRdUZd6hdffDFb7CKhMXZ33HGHNnH79+9HMBhEbW0tQqEQgsEgfD4fhoeHw2I3+CmIYRjGOVTuw3gEX7rDc5B+qI7pTTfdxDF2kRhJnhAuVUEgEEBvby8/BTEMwyQQM1a6TL428xykH6pjaiZ5IsvpnUwl6uvPNY32eDwAgNbWVqxevRp+vx9r1qxBc3MzAoEAmpubk7ynDMMwqY/qmkr7Za5atUrapJ5emzMJOmeZOgfpjB3HlC12ElQZr8FgMCq9nl20DMMw1tBzs7JbMRq20qUfRhJfuNyJBKt17GjG68MPP6xdbB555BE+uRiGYeKACrhQKMQPyzEQcxUIBBAMBln0phFGxDq7Ym1EuGcbGhrCXALsomUYhokPek2l7lcmGjE/wWBQ6ppmUhe7Xeos7HTYvHkzurq6sGnTJjQ2NqKiogKNjY18QWIYhrGI7GGY48Wi4Xi69EYcXwC2inV2xZpAVYiY3QkMwzDG4TgxY/A8pTdmji+7Yh2CumXZescwDGMNYX0KBAIcxhIBW+kyB6eOL1vsLGLEescxEAzDMGrYInUOmhUpjAOZPidMOGyxSwBGrHdmkiroNhiGYdINjqtTQz09PCdMvLDFzgZU1jszT6OqbTAMw6QD9HroxjqgRmqJOfV9ALcDyxSsrjO22CUYar2jmHnyUlkAGYZh0gF6PXRjLHIi9olaLY1012DSj0SsM7bY2YzoWlFfX4/Nmzdrr5tR6Wy9YxgmnXFjLHIi9klmtXTTHDDOY3WdcecJCYkSdna4ZWlLs02bNjm2rwzDME6SaPdmMjDyG7lrBBMv7IpNIiqXqhm3rKooMsMwTCpB3U6p1KHHzL6qXGsytyt3jchMEr322WLnIHa4VNktyzBMKqFKCkil/tpmPCyqAvW0bAm7XTMbO8r6sMXOJciSKswqd1ViBsMwjBtRJQWkUhkPM/uqKlBPt8HJEZlNotc+W+wShIiby83NRX9/vyXlrkrMYBiGcQuZ1mKRy5YwiYAtdi6kpaUFo6OjGBwctKzcxTZaWloc2EOGYRj7CIVCaGpqQltbG5qampK9O5agHhbVWBZjlyH2EiYC1RpJNGyxSxCyTFezGWN0G6FQiK13DMO4DhpP1NfXh56eHpSVlWHfvn3J3jXT0N8CQDqm8XOpFEfI2I9qvdixFthi50JEpmsoFNKyXM0WKqTZsmy9YxjGjdB4onXr1sHv92P9+vXJ3i1L0N+iGqdqHCFjP6o1kmjYYpdgaJbrww8/bDkeg2vdMQzDxIcsPo6ORd25dI4RZJxBtbasriMuUCzBLcLOCXcqJ1UwDJNM3FKIePny5QgGgwgEAlqWqkq0RZYkAaJdrV6vF6Ojo+xaZUxjt1uWhZ0Etwg7isx6Z+XCyLXuGIZJJrJWWYkUeeLhdnR0VHvN7/ebio8TGbx0XFtbix07dnCWK2MaWXZ4POuIhZ0ENwo7ar3bsWOHZUXPblmGYZIJvYklI4FAPNwKamtrsWLFipiijcUak0qwsJPgRmFHoS6El156ydI22CXLMEyysdrk3Cz0egfA8Ydbt7ibGffi5BphYSfB7cKO248xDMMYJ9HXOzvaQjHpjZNrhMudpCB2tA7j9mMMwySKZBRjbWxs1MpFJfp6x6VMGD3cskbYYudC2C3LMIzbcbIYqwr2SjCZClvsUhxxYYznAskFjBmGcZJkFGNlrwTD6MMWOxciLHa1tbWoqamxZHnj9mMMwzAM4yyJSqrh5AkJqSTsKJxUwTCMW0hGZiiHlTBuJlFJNRnhir322mtRVVWF3NxczJgxA6tWrcKHH36Y7N2yHU6qYBjGLdD+1olKmOCwEsbNuCVhgpKywu6KK67Ali1bsHfvXrS0tKCtrS0thUtdXR2qqqqwdOnSsIwwM2zevBldXV3YtGmT5W0wDMPQmxgVeU7CD6aMm1m1ahWCwaCril2njSt269at+PznP4/h4WF4vd6ov6eqK5aaeTs6OtgtyzCMK3CyEDG7XxkmnIyLsevt7cXtt9+Ozs5OvPbaa9L3jI6O4ne/+x2qq6uRlfWRoTInJwc+ny9Ru2qaJ598Ej/5yU9w++2346233sLWrVvxuc99DqFQCFu3bsW1116L73//+4a39/Wvf13bxr/8y784uOcMwzDWmDNnjvYAun///mTvDsMknfHxcfzpT3/CpZdeKjVeUVJa2P3TP/0TfvSjH+HMmTNYunQpnn/+eZSWlkrfe/bsWfzf//1fgveQYRiGYRjGHv7iL/4Cubm5Md/jKmH3zW9+E9/97ndjvmf37t2YP38+AKCnpwe9vb04dOgQ7rvvPhQXF+P555+Hx+OJ+tzExAT6+/sxPj4e9ne3W+wYhmEYhslsQqEQxsfHkZOTE+Z1lOEqYdfd3Y0TJ07EfE9NTQ1ycnKiXj9y5AjOP/98vP7666irq3NqFxmGYRiGYVxL7Ai8BFNeXo7y8nJLn52YmAAADA8P27lLDMMwDMMwKYOrLHZGeeuttxAMBvHJT34SU6dORVtbG9atW4euri68//777FplGIZhGCYjSck6dpMnT8avf/1rXHXVVZg3bx6+9KUv4WMf+xheffVVFnUMwzAMw2QsKWmxYxiGYRiGYaJJSYsdwzAMwzAMEw0LO4ZhGIZhmDSBhR3DMAzDMEyawMKOYRiGYRgmTWBhxzAMwzAMkyawsGMYhmEYhkkTWNgxDMMwDMOkCSzsGIZhGIZh0gQWdgzDMAzDMGkCCzuGYRiGYZg0gYUdwzAMwzBMmvD/A0nYiNHhCGvfAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "plot_band(vasp,use_mask=True,ylim=(-3,3))"]}, {"cell_type": "code", "execution_count": 16, "id": "8ab1d7f8", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'vasprun' object has no attribute 'to_xml'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[16], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mvasp_corrected.xml\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mw\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[0;32m----> 2\u001b[0m     f\u001b[38;5;241m.\u001b[39mwrite(vasp\u001b[38;5;241m.\u001b[39mto_xml())\n", "\u001b[0;31mAttributeError\u001b[0m: 'vasprun' object has no attribute 'to_xml'"]}], "source": ["with open('vasp_corrected.xml', 'w') as f:\n", "    f.write(vasp.to_xml())  # Note: Check if your vasprun object has a to_xml() method\n"]}, {"cell_type": "code", "execution_count": null, "id": "62269c47", "metadata": {}, "outputs": [], "source": ["for elem in root.findall('elements'):\n", "    print(elem.text)  # Access text"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}