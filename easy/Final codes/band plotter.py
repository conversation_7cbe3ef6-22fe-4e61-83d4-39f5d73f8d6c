import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Load the data
data = pd.read_csv(r'/home/<USER>/Downloads/easy/easy/without_mask.csv')
data = data.iloc[:, 1:3]  # Select only kdist and energy columns
data.columns = ['kdist', 'energy']

# Get unique k-points in order of appearance
unique_kdist = sorted(data['kdist'].unique())

# Identify bands by reorganizing data
# First, count how many energy values exist at each k-point
energies_per_kpoint = data.groupby('kdist').size().max()

# Create a 2D array to store band energies
# Each row represents a band, each column a k-point
bands = np.zeros((energies_per_kpoint, len(unique_kdist)))
bands[:] = np.nan  # Initialize with NaN

# Fill the bands array
for i, kdist in enumerate(unique_kdist):
    # Get energies at this k-point and sort them
    energies = data[data['kdist'] == kdist]['energy'].values
    energies.sort()
    
    # Fill the bands array for this k-point
    for j, energy in enumerate(energies):
        if j < energies_per_kpoint:
            bands[j, i] = energy

# Create the plot with a professional style
plt.figure(figsize=(10, 6))
plt.rcParams.update({'font.size': 12, 'font.family': 'Roboto'})

# Plot each band as a continuous line
for i in range(bands.shape[0]):
    plt.plot(unique_kdist, bands[i, :], '-', linewidth=1.0, color='#1f77b4', alpha=0.8)

# Identify approximate high symmetry points based on the k-distance values
# This is an educated guess based on typical band structure calculations
# In a real scenario, these would be known from the calculation setup
min_k = min(unique_kdist)
max_k = max(unique_kdist)
k_range = max_k - min_k

# Estimate high symmetry points (common pattern in band structures)
high_symmetry_points = [0.0, 0.914, 1.660, 2.188, 3.243]
high_symmetry_labels = ['Γ', 'X', 'M', 'K', 'Γ']

# Add vertical lines at high symmetry points
for k in high_symmetry_points:
    plt.axvline(x=k, color='k', linestyle='--', alpha=0.3)

# Set plot properties
plt.xlabel('k-path', fontsize=14)
plt.ylabel('Energy (eV)', fontsize=14)
plt.grid(True, alpha=0.3, linestyle=':')

# Add Fermi level line at E=0
plt.axhline(y=0, color='r', linestyle='--', alpha=0.7, label='Fermi Level')

# Set the high symmetry point labels
plt.xticks(high_symmetry_points, high_symmetry_labels, fontsize=14)

# Add legend
plt.legend(loc='upper right')

plt.tight_layout()
plt.ylim(-3,2)
plt.savefig('band_structure.png', dpi=300)  # Save high-resolution image
plt.show()

