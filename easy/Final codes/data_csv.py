from easyunfold.unfold import UnfoldKSet
from easyunfold.plotting import Unfold<PERSON>lotter
from easyunfold.effective_mass import EffectiveMass, fitted_band
from monty.serialization import loadfn

unfold = loadfn("easyunfold.json")

def clean_latex_string(label: str):
    """
    Clean up latex labels and convert if necessary

    :returns: Cleaned tag string
    """
    if label == 'G':
        return r'$\mathrm{\mathsf{\Gamma}}$'
    if label.startswith('\\'):  ## This is a latex formatted label already
        return f'$\\mathrm{{\\mathsf{{{label}}}}}$'

    return r'$\mathrm{\mathsf{' + label + r'}}$'
def plot_spectral_weights_simple(unfold, figsize=(10, 8), ylim=(-3, 3), factor=3.0,alpha=0.1,lv=0.1,uv=1,return_data=False,add_mask=False,file_name='masked_data.csv',
                            eref=None, color='C2', save=None, x_is_kidx=False):
    import matplotlib.pyplot as plt
    import numpy as np
    import pandas as pd
    global size_array,nb,kdist,sws
    # Get the necessary data
    kdist = unfold.get_kpoint_distances()
    sws = unfold.get_spectral_weights()
    nb = sws[0].shape[2]

    # Set reference energy
    if eref is None:
        eref = unfold.calculated_quantities.get('vbm', 0.0)
    
    # Create figure
    fig, ax = plt.subplots(figsize=figsize)
    
    # Get weights for each k-point
    kweights = unfold.expansion_results['weights']
    labels = unfold.get_combined_kpoint_labels()
    kdist = unfold.get_kpoint_distances()
    
    # Store all data points
    all_x = []
    all_y = []
    all_sizes = []
    
    # Loop through all data points
    for ik, sw in enumerate(sws):
            x = np.repeat(kdist[ik], nb)
            y = sw[0, 0, :, 0] - eref

            # Point sizes (spectral weights)
            sizes = sw[0, 0, :, 1] * factor * kweights[ik][0]
            # Save to our lists
            all_x.append(x)
            all_y.append(y)
            all_sizes.append(sizes)

    # Convert lists to arrays
    x_array = np.concatenate(all_x)
    y_array = np.concatenate(all_y)
    size_array = np.concatenate(all_sizes)
    lower_size=lv
    upper_size=uv
    mask=(upper_size>size_array)&(size_array>lower_size)
    if add_mask:
        x_masked=x_array[mask]
        y_masked=y_array[mask]
        size_masked=size_array[mask]
    else:
        x_masked=x_array
        y_masked=y_array
        size_masked=size_array  
    # Make the scatter plot
    ax.scatter(x_masked, y_masked, s=size_masked, color=color, alpha=alpha)
    # Set axis limits and labels
    ax.set_xlim(0, kdist.max())
    ax.set_ylim(ylim)
    tick_locs = []
    tick_labels = []
    for index, label in labels:
        xloc = index if x_is_kidx else kdist[index]
        ax.axvline(x=xloc, lw=0.5, color='k', ls=':', alpha=0.8)
        tick_locs.append(xloc)
        tick_labels.append(clean_latex_string(label))
    ax.set_xticks(tick_locs)
    ax.set_xticklabels(tick_labels)
    plt.show()

    ax.set_ylabel('Energy [eV]')
    if return_data:
        pd.DataFrame({'kdist': x_masked, 'energy': y_masked, 'size': size_masked}).to_csv(file_name)
    
plot_spectral_weights_simple(unfold, factor=2,lv=0.05,uv=1.99,alpha=0.5,return_data=False,add_mask=False,file_name='without_mask.csv')