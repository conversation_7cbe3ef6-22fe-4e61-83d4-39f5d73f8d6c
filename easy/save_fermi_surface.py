import numpy as np
import sys
from pymatgen.electronic_structure.core import Spin
from ifermi.surface import FermiSurface

def write_bxsf(filename, eigenvalues, kgrid, fermi_energy, reciprocal_vectors):
    """
    Write eigenvalues to a .bxsf file for visualization in XCrySDen.
    
    Args:
        filename: Output filename
        eigenvalues: List of 3D numpy arrays with shape (kx, ky, kz) containing eigenvalues for each band
        kgrid: Tuple of (nx, ny, nz) k-point grid dimensions
        fermi_energy: Fermi energy in eV
        reciprocal_vectors: 3x3 array of reciprocal lattice vectors
    """
    with open(filename, 'w') as f:
        f.write(" BEGIN_INFO\n")
        f.write("   #\n")
        f.write("   # this is a Band-XCRYSDEN-Structure-File\n")
        f.write("   # aimed at Visualization of Fermi Surface\n")
        f.write("   #\n")
        f.write(f"   # Case:   {filename}\n")
        f.write("   #\n")
        f.write(f" Fermi Energy:         {fermi_energy:.4f}\n")
        f.write(" END_INFO\n")
        
        f.write(" BEGIN_BLOCK_BANDGRID_3D\n")
        f.write(" band_energies\n")
        f.write(" BANDGRID_3D_BANDS\n")
        f.write(f"   {len(eigenvalues)}\n")  # Number of bands
        f.write(f"   {kgrid[0]}   {kgrid[1]}   {kgrid[2]}\n")  # k-point grid
        f.write("  0.000000  0.000000  0.000000\n")  # Origin (Gamma point)
        
        # Write reciprocal lattice vectors
        for vector in reciprocal_vectors:
            f.write(f"  {vector[0]:.6f}  {vector[1]:.6f}  {vector[2]:.6f}\n")
        
        # Write eigenvalues for each band
        for i, band in enumerate(eigenvalues):
            f.write(f"BAND: {i+1}\n")
            for kz in range(kgrid[2]):
                for ky in range(kgrid[1]):
                    line = ""
                    for kx in range(kgrid[0]):
                        line += f"   {band[kx, ky, kz]:.6f}"
                        # Add a newline every 6 values for readability
                        if (kx + 1) % 6 == 0 and kx < kgrid[0] - 1:
                            line += "\n"
                    f.write(line + "\n")
        
        f.write(" END_BLOCK_BANDGRID_3D\n")
        f.write("END_BLOCK_BANDGRID_3D\n")

def extract_and_save_fermi_surface(fs_object, output_filename):
    """
    Extract data from a FermiSurface object and save it as a .bxsf file
    
    Args:
        fs_object: An ifermi.surface.FermiSurface object
        output_filename: Name of the output .bxsf file
    """
    # Get the reciprocal lattice vectors
    reciprocal_vectors = fs_object.reciprocal_space.reciprocal_lattice
    
    # Get the Fermi energy
    fermi_energy = fs_object.band_structure.efermi
    
    # Get the eigenvalues and kpoints
    band_structure = fs_object.band_structure
    
    # Get the spin polarization
    is_spin_polarized = band_structure.is_spin_polarized
    
    # Get the bands that cross the Fermi level
    isosurfaces = fs_object.isosurfaces
    
    # Get unique band indices that cross the Fermi level
    band_indices = set()
    for spin in isosurfaces:
        for isosurface in isosurfaces[spin]:
            band_indices.add(isosurface.band_idx)
    band_indices = sorted(list(band_indices))
    
    # Get the k-point grid dimensions
    kpoints = band_structure.kpoints
    kpoint_coords = np.array([k.frac_coords for k in kpoints])
    
    # Determine the grid dimensions
    unique_kx = np.unique(kpoint_coords[:, 0])
    unique_ky = np.unique(kpoint_coords[:, 1])
    unique_kz = np.unique(kpoint_coords[:, 2])
    kgrid = (len(unique_kx), len(unique_ky), len(unique_kz))
    
    # Extract eigenvalues for each band
    eigenvalues = []
    spin = Spin.up  # Use up spin if spin-polarized, otherwise use the only spin channel
    
    for band_idx in band_indices:
        # Create a 3D grid for this band's eigenvalues
        band_eigenvalues = np.zeros(kgrid)
        
        # Fill the grid with eigenvalues
        for i, k in enumerate(kpoints):
            # Find the indices in the grid
            kx_idx = np.where(unique_kx == kpoint_coords[i, 0])[0][0]
            ky_idx = np.where(unique_ky == kpoint_coords[i, 1])[0][0]
            kz_idx = np.where(unique_kz == kpoint_coords[i, 2])[0][0]
            
            # Get the eigenvalue for this k-point and band
            eigenvalue = band_structure.bands[spin][band_idx, i]
            
            # Store in the grid
            band_eigenvalues[kx_idx, ky_idx, kz_idx] = eigenvalue
        
        eigenvalues.append(band_eigenvalues)
    
    # Write to .bxsf file
    write_bxsf(output_filename, eigenvalues, kgrid, fermi_energy, reciprocal_vectors)
    print(f"Fermi surface saved to {output_filename}")

if __name__ == "__main__":
    # This script should be run from a notebook after creating a FermiSurface object
    # Example usage:
    # %run save_fermi_surface.py fs "output.bxsf"
    
    if len(sys.argv) < 3:
        print("Usage: %run save_fermi_surface.py <fermi_surface_variable_name> <output_filename>")
        sys.exit(1)
    
    # Get the FermiSurface object from the caller's namespace
    import inspect
    frame = inspect.currentframe().f_back
    fs_var_name = sys.argv[1]
    
    if fs_var_name not in frame.f_locals:
        print(f"Error: {fs_var_name} not found in the current namespace")
        sys.exit(1)
    
    fs_object = frame.f_locals[fs_var_name]
    output_filename = sys.argv[2]
    
    extract_and_save_fermi_surface(fs_object, output_filename)
