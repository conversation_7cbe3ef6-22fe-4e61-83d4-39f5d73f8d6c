import numpy as np

def verify_lu_solution():
    # LU factorization matrices from Problem 6
    L = np.array([
        [1, 0, 0],
        [-0.3, 1, 0],
        [0.1, -0.148148, 1]
    ])
    
    U = np.array([
        [10, 2, -1],
        [0, -5.4, 1.7],
        [0, 0, 5.351852]
    ])
    
    # Original A matrix
    A = np.array([
        [10, 2, -1],
        [-3, -6, 2],
        [1, 1, 5]
    ])
    
    # Part (a) - original right-hand side
    b = np.array([27, -61.5, -21.5])
    
    # Part (b) - alternative right-hand side
    B = np.array([12, 18, -6])
    
    print("Verification of LU Factorization Solution")
    print("-" * 40)
    
    # Verify A = LU
    LU = np.dot(L, U)
    print("\nVerifying A = LU:")
    print("Maximum difference between A and LU:", np.max(np.abs(A - LU)))
    
    # Part (a) verification
    print("\nPart (a) - Original system:")
    print("-" * 40)
    
    # Given solution from our calculation
    x_a = np.array([0.5, 8.0, -6.0])
    
    # Verify using nump<PERSON>'s solver
    x_a_numpy = np.linalg.solve(A, b)
    print("\nSolution comparison:")
    print("Our solution:     ", x_a)
    print("Numpy's solution: ", x_a_numpy)
    print("Maximum difference:", np.max(np.abs(x_a - x_a_numpy)))
    
    # Verify Ax = b
    Ax = np.dot(A, x_a)
    print("\nVerifying Ax = b:")
    print("Ax =", Ax)
    print("b  =", b)
    print("Maximum residual:", np.max(np.abs(Ax - b)))
    
    # Part (b) verification
    print("\nPart (b) - Alternative system:")
    print("-" * 40)
    
    # Given solution from our calculation
    x_b = np.array([1.972318, -4.235294, -0.747405])
    
    # Verify using numpy's solver
    x_b_numpy = np.linalg.solve(A, B)
    print("\nSolution comparison:")
    print("Our solution:     ", x_b)
    print("Numpy's solution: ", x_b_numpy)
    print("Maximum difference:", np.max(np.abs(x_b - x_b_numpy)))
    
    # Verify Ax = B
    Ax = np.dot(A, x_b)
    print("\nVerifying Ax = B:")
    print("Ax =", Ax)
    print("B  =", B)
    print("Maximum residual:", np.max(np.abs(Ax - B)))

if __name__ == "__main__":
    verify_lu_solution()
