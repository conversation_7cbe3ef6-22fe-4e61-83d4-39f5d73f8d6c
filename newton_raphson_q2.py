import numpy as np

def f(x):
    """Calculate the function value for f(x)"""
    return (x**5 - 16.05*x**4 + 88.75*x**3 - 192.0375*x**2 + 116.35*x + 31.6875)

def f_prime(x):
    """Calculate the derivative f'(x)"""
    return (5*x**4 - 64.2*x**3 + 266.25*x**2 - 384.075*x + 116.35)

def newton_raphson(x0, tol=0.01, max_iter=100):
    """
    Perform Newton-Raphson iteration
    x0: initial guess
    tol: tolerance for convergence (in percentage)
    max_iter: maximum number of iterations
    """
    iterations = []
    x_old = x0
    
    for i in range(max_iter):
        fx = f(x_old)
        fpx = f_prime(x_old)
        
        x_new = x_old - fx/fpx
        
        # Calculate relative error in percentage
        if x_new != 0:
            error = abs((x_new - x_old)/x_new) * 100
        else:
            error = 0
            
        # Store iteration data
        iterations.append({
            'n': i,
            'x': x_old,
            'f(x)': fx,
            'f_prime(x)': fpx,
            'error': error if i > 0 else None
        })
        
        # Check for convergence
        if error < tol:
            iterations.append({
                'n': i+1,
                'x': x_new,
                'f(x)': f(x_new),
                'f_prime(x)': f_prime(x_new),
                'error': error
            })
            break
            
        x_old = x_new
    
    return iterations

def main():
    # Initial conditions
    x0 = 0.5825
    tol = 0.01  # 0.01%
    
    # Perform Newton-Raphson iteration
    iterations = newton_raphson(x0, tol)
    
    # Print results
    print("\nNewton-Raphson Method Results:")
    print("-" * 80)
    print(f"{'n':>3} {'x_n':>15} {'f(x_n)':>20} {'f\'(x_n)':>20} {'ε_a (%)':>15}")
    print("-" * 80)
    
    for it in iterations:
        if it['error'] is None:
            error_str = "---"
        else:
            error_str = f"{it['error']:.4f}"
        
        print(f"{it['n']:3d} {it['x']:15.7f} {it['f(x)']:20.7f} {it['f_prime(x)']:20.7f} {error_str:>15}")
    
    print("-" * 80)
    print(f"\nFinal Solution: x = {iterations[-1]['x']:.4f}")
    print(f"Final f(x): {iterations[-1]['f(x)']:.10f}")

if __name__ == "__main__":
    main()
