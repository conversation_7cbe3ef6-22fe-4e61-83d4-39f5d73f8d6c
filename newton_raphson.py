import numpy as np

def f(x):
    """Function: f(x) = x⁵ - 16.05x⁴ + 88.75x³ - 192.0375x² + 116.35x + 31.6875"""
    return (x**5 - 16.05*x**4 + 88.75*x**3 - 192.0375*x**2 + 116.35*x + 31.6875)

def df(x):
    """Derivative: f'(x) = 5x⁴ - 64.2x³ + 266.25x² - 384.075x + 116.35"""
    return (5*x**4 - 64.2*x**3 + 266.25*x**2 - 384.075*x + 116.35)

def newton_raphson(x0, tolerance=0.01, max_iter=100):
    """
    Newton-Raphson method implementation
    x0: initial guess
    tolerance: convergence tolerance in percentage
    max_iter: maximum number of iterations
    """
    iterations = []
    x_old = x0
    
    for i in range(max_iter):
        f_value = f(x_old)
        f_prime = df(x_old)
        
        # Compute new value
        x_new = x_old - f_value/f_prime
        
        # Calculate error
        if x_new != 0:
            error = abs((x_new - x_old)/x_new) * 100
        else:
            error = abs(x_new - x_old) * 100
            
        # Store iteration data
        iterations.append({
            'iteration': i,
            'x': x_old,
            'f(x)': f_value,
            "f'(x)": f_prime,
            'error': error if i > 0 else float('nan')
        })
        
        # Check for convergence
        if error < tolerance:
            iterations.append({
                'iteration': i+1,
                'x': x_new,
                'f(x)': f(x_new),
                "f'(x)": df(x_new),
                'error': error
            })
            break
            
        x_old = x_new
    
    return iterations

# Initial conditions from the problem
x0 = 0.5825
tolerance = 0.01  # 0.01%

# Solve using Newton-Raphson method
results = newton_raphson(x0, tolerance)

# Print results in a formatted table
print("\nNewton-Raphson Method Results:")
print("-" * 90)
print(f"{'Iter':^6} {'x_n':^15} {'f(x_n)':^20} {'f\'(x_n)':^20} {'Error (%)':^15}")
print("-" * 90)

for res in results:
    if np.isnan(res['error']):
        error_str = "---"
    else:
        error_str = f"{res['error']:.4f}"
        
    print(f"{res['iteration']:^6d} {res['x']:^15.6f} {res['f(x)']:^20.6f} {res['f\'(x)']:^20.6f} {error_str:^15s}")

print("-" * 90)
