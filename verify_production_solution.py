import numpy as np

def verify_production_plan():
    # Given solution
    x1 = 11
    x2 = 153.55
    x3 = 53.45

    # Material constraints (coefficient matrix)
    A = np.array([
        [15, 17, 19],       # Metal (g)
        [0.30, 0.40, 0.55], # Plastic (g)
        [1.0, 1.2, 1.5]     # Rubber (g)
    ])

    # Available materials (right-hand side)
    b = np.array([3890, 95, 282])

    # Solution vector
    x = np.array([x1, x2, x3])

    # Calculate actual usage
    actual_usage = np.dot(A, x)

    print("Solution Verification:")
    print(f"Components to produce: x1 = {x1:.2f}, x2 = {x2:.2f}, x3 = {x3:.2f}\n")

    # Check material usage
    materials = ['Metal', 'Plastic', 'Rubber']
    available = [3890, 95, 282]

    print("Material Usage Check:")
    print(f"{'Material':<10} {'Used':>10} {'Available':>12} {'Difference':>12} {'Valid':>8}")
    print("-" * 52)

    all_valid = True
    for i, (mat, used, avail) in enumerate(zip(materials, actual_usage, available)):
        diff = avail - used
        valid = diff >= -0.01  # Allow for small numerical errors
        all_valid &= valid
        print(f"{mat:<10} {used:>10.2f} {avail:>12.2f} {diff:>12.2f} {str(valid):>8}")

    print("\nIntermediate Steps Verification:")
    # Verify the Gaussian elimination steps
    # Original matrix
    A_aug = np.array([
        [15, 17, 19, 3890],
        [0.30, 0.40, 0.55, 95],
        [1.0, 1.2, 1.5, 282]
    ])

    # After first pivot (division by 15)
    print("\nAfter first pivot (R1 / 15):")
    print(f"Expected: 1.0000, 1.1333, 1.2667, 259.33")
    print(f"Got:      {A_aug[0,0]/15:.4f}, {A_aug[0,1]/15:.4f}, {A_aug[0,2]/15:.4f}, {A_aug[0,3]/15:.2f}")

    # Final non-negativity check
    print("\nNon-negativity Check:")
    for i, val in enumerate([x1, x2, x3], 1):
        print(f"x{i} = {val:.2f} {'✓' if val >= 0 else '✗'}")

    return all_valid

if __name__ == "__main__":
    verify_production_plan()
