\documentclass[12pt]{article}
\usepackage{amsmath, amssymb}
\usepackage{geometry}
\geometry{margin=1in}
\usepackage{listings}
\usepackage{xcolor}

% Page setup with simple headers
\usepackage{fancyhdr}
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{\textbf{Numerical Methods - Root Finding}}
\fancyhead[R]{\textbf{Page \thepage}}
\renewcommand{\headrulewidth}{0.4pt}

% Spacing and formatting
\setlength{\parindent}{0pt}
\setlength{\parskip}{0.8em}
\linespread{1.2}

% Enhanced section formatting
\makeatletter
\renewcommand\section{\@startsection{section}{1}{\z@}%
                                   {-4ex \@plus -1ex \@minus -.2ex}%
                                   {2.5ex \@plus.2ex}%
                                   {\Large\bfseries\centering}}
\renewcommand\subsection{\@startsection{subsection}{2}{\z@}%
                                     {-3ex\@plus -1ex \@minus -.2ex}%
                                     {1.8ex \@plus .2ex}%
                                     {\large\bfseries}}
\renewcommand\subsubsection{\@startsection{subsubsection}{3}{\z@}%
                                     {-2.5ex\@plus -1ex \@minus -.2ex}%
                                     {1.2ex \@plus .2ex}%
                                     {\normalsize\bfseries}}
\makeatother

% Enhanced code formatting
\definecolor{codegray}{gray}{0.95}
\definecolor{codeframe}{gray}{0.7}
\lstset{
  backgroundcolor=\color{codegray},
  basicstyle=\ttfamily\footnotesize,
  frame=single,
  frameround=tttt,
  rulecolor=\color{codeframe},
  breaklines=true,
  breakatwhitespace=true,
  numbers=left,
  numberstyle=\tiny\color{gray},
  stepnumber=1,
  numbersep=8pt,
  showstringspaces=false,
  tabsize=2,
  captionpos=b
}

% Custom list spacing
\renewcommand{\labelitemi}{$\bullet$}
\renewcommand{\labelitemii}{$\circ$}
\setlength{\itemsep}{0.3em}
\setlength{\parsep}{0.2em}

\title{\textbf{\Large Key Concepts in Numerical Methods for Root Finding}}
\author{\textbf{Student Name}}
\date{\today}

\begin{document}
\maketitle
\thispagestyle{fancy}

\section{Initial Guess Selection Strategies}

\subsection{1.1 Open Methods}

\noindent\textbf{Definition:} Open methods approach the root from one side without bracketing, making convergence highly dependent on the initial guess selection.

\noindent\textbf{Key Characteristics:}
\begin{itemize}
  \item Convergence in open methods (e.g., Newton-Raphson) is highly sensitive to the initial guess
  \item A good initial guess ensures quadratic convergence; poor guesses may cause divergence or convergence to incorrect roots
  \item Generally faster than bracketing methods when they converge
\end{itemize}

\noindent\textbf{Selection Strategies:}
\begin{enumerate}
  \item \textbf{Graphical Analysis:} Plot the function to observe sign changes or x-axis crossings
  \item \textbf{Incremental Search:} Systematically evaluate the function at regular intervals
  \item \textbf{Mathematical Rules:} Apply Descartes' Rule of Signs for polynomial roots
  \item \textbf{Domain Knowledge:} Consider physical constraints and problem context
\end{enumerate}

\subsection{1.2 Bracketing Methods}

\noindent\textbf{Definition:} Bracketing methods require two initial guesses that bound the root, ensuring convergence through the Intermediate Value Theorem.

\noindent\textbf{Mathematical Foundation:}
\begin{itemize}
  \item Require two initial guesses $(a, b)$ such that $f(a) \cdot f(b) < 0$
  \item Based on the Intermediate Value Theorem to guarantee existence of a root in $[a, b]$
  \item Always converge but typically slower than open methods
\end{itemize}

\noindent\textbf{Implementation Guidelines:}
\begin{enumerate}
  \item Use incremental search to locate valid bracketing intervals
  \item Smaller initial intervals reduce total iterations
  \item Convergence is guaranteed regardless of interval size
\end{enumerate}

\section{Truncation Error in Newton-Raphson Method}

\subsection{2.1 Theoretical Foundation and Derivation}

\noindent\textbf{Taylor Series Basis:} The Newton-Raphson method is derived from the Taylor series expansion of a function around a point $x_n$:

\[
f(x) = f(x_n) + f'(x_n)(x - x_n) + \frac{f''(x_n)}{2!}(x - x_n)^2 + \frac{f'''(x_n)}{3!}(x - x_n)^3 + \cdots
\]

\noindent\textbf{Linear Approximation:} The method truncates after the linear term:
\[
f(x) \approx f(x_n) + f'(x_n)(x - x_n)
\]

\noindent\textbf{Iterative Formula Derivation:} Setting $f(x) = 0$ and solving for $x$:
\begin{align}
0 &= f(x_n) + f'(x_n)(x - x_n) \\
f'(x_n)(x - x_n) &= -f(x_n) \\
x &= x_n - \frac{f(x_n)}{f'(x_n)}
\end{align}

Therefore, the Newton-Raphson iterative formula is:
\[
\boxed{x_{n+1} = x_n - \frac{f(x_n)}{f'(x_n)}}
\]

\subsection{2.2 Truncation Error Analysis}

\noindent\textbf{Error Definition:} Truncation error arises from neglecting higher-order terms in the Taylor series.

\noindent\textbf{Mathematical Expression:} The truncation error can be estimated as:
\[
\boxed{E_{\text{truncation}} \approx \frac{f''(\xi)}{2f'(x_n)}(x_n - r)^2}
\]

where:
\begin{itemize}
  \item $\xi$ is some point between $x_n$ and the true root $r$
  \item $r$ is the exact root of the equation $f(x) = 0$
  \item This explains the quadratic convergence behavior
\end{itemize}

\noindent\textbf{Convergence Implications:}
\begin{enumerate}
  \item \textbf{Quadratic Convergence:} Error reduces with the square of the previous error
  \item \textbf{Rapid Improvement:} Near the root, convergence is extremely fast
  \item \textbf{Practical Estimation:} Use successive approximations to estimate relative error
\end{enumerate}

\section{Convergence Comparison: Bisection vs. Newton-Raphson}

\subsection{3.1 Bisection Method Analysis}

\noindent\textbf{Convergence Characteristics:}
\begin{itemize}
  \item \textbf{Guaranteed Convergence:} Always converges when initial interval brackets the root
  \item \textbf{Linear Convergence:} Error reduces by a constant factor each iteration
  \item \textbf{Predictable Behavior:} Number of iterations can be calculated in advance
\end{itemize}

\noindent\textbf{Mathematical Analysis:} The number of iterations required for a given tolerance is:
\[
\boxed{n = \left\lceil \log_2\left(\frac{b-a}{\varepsilon}\right) \right\rceil}
\]

where $[a,b]$ is the initial interval and $\varepsilon$ is the desired tolerance.

\noindent\textbf{Advantages and Limitations:}
\begin{itemize}
  \item[$+$] Robust and reliable
  \item[$+$] Simple to implement
  \item[$-$] Slower convergence rate
  \item[$-$] Requires function evaluation only
\end{itemize}

\subsection{3.2 Newton-Raphson Method Analysis}

\noindent\textbf{Convergence Characteristics:}
\begin{itemize}
  \item \textbf{Quadratic Convergence:} Near the root, error squares with each iteration
  \item \textbf{Fast Convergence:} Typically requires fewer iterations than bisection
  \item \textbf{Conditional Convergence:} Success depends on initial guess quality
\end{itemize}

\noindent\textbf{Requirements and Limitations:}
\begin{itemize}
  \item Requires computation of both $f(x)$ and $f'(x)$
  \item May diverge with poor initial guesses
  \item Fails when $f'(x_n) = 0$ or is very small
  \item Sensitive to function behavior near the root
\end{itemize}

\subsection{3.3 Comparative Summary and Selection Guidelines}

\begin{center}
\begin{tabular}{|l|c|c|}
\hline
\textbf{Criterion} & \textbf{Bisection} & \textbf{Newton-Raphson} \\
\hline
Convergence Rate & Linear & Quadratic \\
Reliability & High & Moderate \\
Implementation & Simple & Moderate \\
Derivative Required & No & Yes \\
Initial Guess Sensitivity & Low & High \\
\hline
\end{tabular}
\end{center}

\noindent\textbf{Selection Guidelines:}
\begin{enumerate}
  \item \textbf{Use Bisection when:} Reliability is critical, derivative is unavailable, or robust convergence is required
  \item \textbf{Use Newton-Raphson when:} Speed is essential, good initial guess is available, and derivative can be computed
  \item \textbf{Consider Hybrid Methods:} Brent's method combines reliability of bisection with speed of Newton-Raphson
\end{enumerate}

\section{Implementation: Bisection Method Pseudocode}

\subsection{4.1 Algorithm Structure}

\noindent\textbf{Input Parameters:}
\begin{itemize}
  \item $f(x)$: The function whose root is to be found
  \item $[a, b]$: Initial bracketing interval where $f(a) \cdot f(b) < 0$
  \item $\varepsilon$: Tolerance for convergence
  \item $N_{\max}$: Maximum number of iterations (safety limit)
\end{itemize}

\noindent\textbf{Output:} Approximate root of $f(x) = 0$

\subsection{4.2 Detailed Pseudocode}

\begin{lstlisting}[language=Python, caption={Enhanced Bisection Method Algorithm}]
ALGORITHM: Bisection Method
INPUT: Function f(x), bounds a and b, tolerance epsilon, max_iterations
OUTPUT: Approximate root or error message

BEGIN
  // Step 1: Validate initial conditions
  IF f(a) * f(b) >= 0 THEN
    PRINT "Error: f(a) and f(b) must have opposite signs"
    PRINT "Current values: f(" + a + ") = " + f(a)
    PRINT "                f(" + b + ") = " + f(b)
    RETURN "Invalid initial interval"
  END IF

  // Step 2: Initialize variables
  iteration = 0
  error = |b - a|

  // Step 3: Main iteration loop
  WHILE error/2 > epsilon AND iteration < max_iterations DO
    // Calculate midpoint
    c = (a + b) / 2
    fc = f(c)

    // Check for exact root
    IF |fc| < epsilon THEN
      PRINT "Exact root found at x = " + c
      RETURN c
    END IF

    // Determine which subinterval contains the root
    IF f(a) * fc < 0 THEN
      b = c    // Root is in left subinterval [a, c]
    ELSE
      a = c    // Root is in right subinterval [c, b]
    END IF

    // Update error and iteration counter
    error = |b - a|
    iteration = iteration + 1

    // Optional: Print iteration details
    PRINT "Iteration " + iteration + ": x = " + c + ", f(x) = " + fc
  END WHILE

  // Step 4: Return final result
  root = (a + b) / 2
  PRINT "Converged after " + iteration + " iterations"
  PRINT "Final root approximation: " + root
  RETURN root
END
\end{lstlisting}

\subsection{4.3 Algorithm Complexity and Performance}

\noindent\textbf{Time Complexity:} $O(\log_2(n))$ where $n = \frac{b-a}{\varepsilon}$

\noindent\textbf{Space Complexity:} $O(1)$ - constant space requirement

\noindent\textbf{Key Features:}
\begin{enumerate}
  \item \textbf{Input Validation:} Ensures proper bracketing before proceeding
  \item \textbf{Error Monitoring:} Tracks convergence progress
  \item \textbf{Safety Limits:} Prevents infinite loops with maximum iteration limit
  \item \textbf{Detailed Output:} Provides iteration-by-iteration progress information
\end{enumerate}

\section{Summary and Conclusions}

\subsection{5.1 Key Insights and Takeaways}

\noindent\textbf{Method Classification and Characteristics:}
\begin{enumerate}
  \item \textbf{Bracketing Methods:} Guarantee convergence but exhibit slower linear convergence rates
  \item \textbf{Open Methods:} Provide rapid quadratic convergence with proper initial guesses but risk divergence
  \item \textbf{Hybrid Approaches:} Combine reliability and efficiency for practical applications
\end{enumerate}

\noindent\textbf{Critical Success Factors:}
\begin{itemize}
  \item \textbf{Initial Guess Selection:} Fundamental to success in open methods
  \item \textbf{Error Analysis:} Essential for understanding convergence behavior
  \item \textbf{Method Selection:} Must align with problem requirements and constraints
\end{itemize}

\subsection{5.2 Practical Implementation Guidelines}

\noindent\textbf{For Engineering Applications:}
\begin{enumerate}
  \item \textbf{Reliability First:} Use bracketing methods when solution robustness is critical
  \item \textbf{Efficiency Focus:} Apply Newton-Raphson when derivatives are available and good initial estimates exist
  \item \textbf{Hybrid Strategy:} Consider combined approaches for optimal performance
\end{enumerate}

\noindent\textbf{Error Management:}
\begin{itemize}
  \item Understand truncation error sources and implications
  \item Implement appropriate convergence criteria
  \item Monitor iteration progress and set safety limits
\end{itemize}

\subsection{5.3 Final Recommendations}

\noindent A comprehensive understanding of error analysis, convergence behavior, and implementation details enables practitioners to:

\begin{itemize}
  \item \textbf{Select Appropriate Methods:} Choose optimal algorithms based on problem characteristics
  \item \textbf{Implement Robust Solutions:} Develop reliable numerical implementations
  \item \textbf{Optimize Performance:} Balance accuracy, speed, and reliability requirements
  \item \textbf{Handle Edge Cases:} Manage numerical challenges and convergence failures
\end{itemize}

\noindent\textbf{Success in numerical root finding requires mastering both theoretical foundations and practical implementation considerations.}

\end{document}
