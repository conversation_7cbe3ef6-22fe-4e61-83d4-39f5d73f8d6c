\documentclass[12pt]{article}
\usepackage{amsmath, amssymb}
\usepackage{geometry}
\geometry{margin=1in}
\usepackage{listings}
\usepackage{xcolor}

% Clean formatting
\setlength{\parindent}{0pt}
\setlength{\parskip}{0.6em}

% Code formatting
\definecolor{codegray}{gray}{0.9}
\lstset{
  backgroundcolor=\color{codegray},
  basicstyle=\ttfamily\footnotesize,
  frame=single,
  breaklines=true
}

\begin{document}

\textbf{Q1. Root Finding Methods - Key Concepts} \hfill \textbf{[6 marks]}

\subsection*{(a) Initial Guess Selection Strategies \hfill [1.5 marks]}

\textbf{Open Methods (Newton-Raphson, Secant):}
\begin{itemize}
  \item Convergence is highly sensitive to initial guess selection
  \item Good initial guess ensures quadratic convergence; poor guess may cause divergence
  \item Selection strategies:
  \begin{itemize}
    \item Graphical analysis to identify sign changes or x-axis crossings
    \item Incremental search to systematically evaluate function values
    \item Apply mathematical rules (e.g., <PERSON><PERSON><PERSON>' Rule of Signs)
    \item Consider physical constraints and domain knowledge
  \end{itemize}
\end{itemize}

\textbf{Bracketing Methods (Bisection, False Position):}
\begin{itemize}
  \item Require two initial guesses $(a, b)$ where $f(a) \cdot f(b) < 0$
  \item Based on Intermediate Value Theorem to guarantee root existence in $[a, b]$
  \item Incremental search helps locate valid bracketing intervals
  \item Smaller intervals reduce iterations but convergence is always guaranteed
\end{itemize}

\subsection*{(b) Truncation Error in Newton-Raphson Method \hfill [1.5 marks]}

\textbf{Derivation from Taylor Series:}
The Newton-Raphson method truncates the Taylor series after the linear term:
\[
f(x) \approx f(x_n) + f'(x_n)(x - x_n)
\]

Setting $f(x) = 0$ and solving for $x$ gives the iterative formula:
\[
x_{n+1} = x_n - \frac{f(x_n)}{f'(x_n)}
\]

\textbf{Truncation Error Estimate:}
\[
E_{\text{truncation}} \approx \frac{f''(\xi)}{2f'(x_n)}(x_n - r)^2
\]
where $\xi$ lies between $x_n$ and the true root $r$. This explains quadratic convergence: error reduces with the square of the previous error.

\subsection*{(c) Convergence Comparison: Bisection vs. Newton-Raphson \hfill [1.5 marks]}

\textbf{Bisection Method:}
\begin{itemize}
  \item Guarantees convergence with linear convergence rate
  \item Number of iterations: $n = \log_2\left(\frac{b-a}{\epsilon}\right)$
  \item Robust but slower convergence
  \item No derivative computation required
\end{itemize}

\textbf{Newton-Raphson Method:}
\begin{itemize}
  \item Quadratic convergence near the root
  \item Requires computation of $f'(x)$
  \item May diverge with poor initial guesses or when $f'(x_n) \approx 0$
  \item Faster convergence when conditions are favorable
\end{itemize}

\textbf{Selection Guidelines:}
\begin{itemize}
  \item Use Bisection when reliability is critical and derivative is unavailable
  \item Use Newton-Raphson when speed is needed and good initial guess is available
  \item Hybrid methods (e.g., Brent's method) combine reliability and speed
\end{itemize}

\subsection*{(d) Bisection Method Pseudocode \hfill [1.5 marks]}

\begin{lstlisting}[caption={Bisection Method Algorithm}]
INPUT: Function f(x), bounds a and b, tolerance epsilon, max_iterations

BEGIN
  IF f(a) * f(b) > 0 THEN
    PRINT "Error: f(a) and f(b) must have opposite signs"
    STOP
  END IF

  iteration = 0
  WHILE (b - a)/2 > epsilon AND iteration < max_iterations DO
    c = (a + b) / 2
    
    IF f(c) == 0 THEN
      RETURN c
    ELSE IF f(a) * f(c) < 0 THEN
      b = c
    ELSE
      a = c
    END IF
    
    iteration = iteration + 1
  END WHILE

  RETURN (a + b) / 2
END
\end{lstlisting}

\textbf{Key Steps:}
\begin{enumerate}
  \item Validate that $f(a) \cdot f(b) < 0$ (root bracketing condition)
  \item Calculate midpoint $c = \frac{a + b}{2}$
  \item Determine which subinterval contains the root based on sign change
  \item Update interval bounds and repeat until convergence
\end{enumerate}

\end{document}
