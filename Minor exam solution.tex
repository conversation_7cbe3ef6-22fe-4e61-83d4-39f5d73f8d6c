\documentclass[12pt]{article}
\usepackage{amsmath, amssymb}
\usepackage{geometry}
\geometry{margin=1in}
\usepackage{titlesec}
\titleformat{\section}{\large\bfseries}{\thesection}{1em}{}
\titleformat{\subsection}{\normalsize\bfseries}{\thesubsection}{1em}{}
\usepackage{listings}
\usepackage{xcolor}

\definecolor{codegray}{gray}{0.9}
\lstset{
  backgroundcolor=\color{codegray},
  basicstyle=\ttfamily\small,
  frame=single,
  breaklines=true
}

\title{\textbf{Key Concepts in Numerical Methods for Root Finding}}
\date{}

\begin{document}
\maketitle

\section{Initial Guess Selection Strategies}

\subsection{Open Methods}
\begin{itemize}
  \item Convergence in open methods (e.g., Newton-Rap<PERSON>on) is highly sensitive to the initial guess.
  \item A good initial guess ensures quadratic convergence; poor guesses may cause divergence or convergence to incorrect roots.
  \item Strategies include:
  \begin{itemize}
    \item Graphical analysis to observe sign changes or x-axis crossings.
    \item Incremental search to systematically evaluate the function.
    \item Use of mathematical rules (e.g., <PERSON><PERSON><PERSON>' Rule of Signs).
    \item Consideration of physical constraints or domain knowledge.
  \end{itemize}
\end{itemize}

\subsection{Bracketing Methods}
\begin{itemize}
  \item Require two initial guesses $(a, b)$ such that $f(a)f(b) < 0$.
  \item Based on the Intermediate Value Theorem to ensure existence of a root in $[a, b]$.
  \item Incremental search helps locate valid bracketing intervals.
  \item Smaller intervals reduce iterations but do not affect guaranteed convergence.
\end{itemize}

\section{Truncation Error in Newton-Raphson Method}

\subsection{Definition and Derivation}
\begin{itemize}
  \item The method is derived from truncating the Taylor series after the linear term:
  \[
  f(x) \approx f(x_n) + f'(x_n)(x - x_n)
  \]
  \item Setting $f(x) = 0$, the iterative formula becomes:
  \[
  x_{n+1} = x_n - \frac{f(x_n)}{f'(x_n)}
  \]
\end{itemize}

\subsection{Truncation Error Estimate}
\[
E_{\text{truncation}} \approx \frac{f''(\xi)}{2f'(x_n)}(x_n - r)^2
\]
\begin{itemize}
  \item $\xi$ is between $x_n$ and the true root $r$.
  \item Explains quadratic convergence: error reduces with the square of the previous error.
  \item Practical estimation uses successive approximations and relative error.
\end{itemize}

\section{Convergence Comparison: Bisection vs. Newton-Raphson}

\subsection{Bisection Method}
\begin{itemize}
  \item Guarantees convergence; exhibits linear convergence.
  \item Halves the interval each iteration:
  \[
  n = \log_2\left(\frac{b-a}{\varepsilon}\right)
  \]
  \item Robust but slower.
\end{itemize}

\subsection{Newton-Raphson Method}
\begin{itemize}
  \item Quadratic convergence near the root.
  \item Requires computation of $f'(x)$.
  \item May diverge with poor guesses or near-zero derivatives.
\end{itemize}

\subsection{Summary}
\begin{itemize}
  \item Use Bisection when reliability is critical.
  \item Use Newton-Raphson when speed is needed and a good initial guess is known.
  \item Hybrid methods (e.g., Brent's method) combine reliability and speed.
\end{itemize}

\section{Bisection Method: Pseudocode}

\begin{lstlisting}[language=Python,caption={Bisection Method Pseudocode}]
INPUT:
  Function f(x)
  Bounds a, b
  Tolerance ε
  Max iterations N_max

BEGIN
  IF f(a) * f(b) > 0:
    PRINT "Error: f(a) and f(b) must have opposite signs"
    STOP

  iteration = 0
  WHILE (b - a)/2 > ε AND iteration < N_max:
    c = (a + b)/2
    IF f(c) == 0:
      RETURN c
    ELSE IF f(a)*f(c) < 0:
      b = c
    ELSE:
      a = c
    iteration += 1

  RETURN (a + b)/2
END
\end{lstlisting}

\section{Conclusion}

\begin{itemize}
  \item Bracketing methods guarantee convergence but are slower.
  \item Open methods converge rapidly with good initial guesses but may fail otherwise.
  \item Truncation error analysis explains Newton-Raphson’s convergence rate.
  \item A solid understanding of error analysis, convergence behavior, and implementation details allows practitioners to select and implement appropriate root-finding strategies.
\end{itemize}

\end{document}
