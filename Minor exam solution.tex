\documentclass{article}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{booktabs}
\usepackage{geometry}
\usepackage{array}
\usepackage{mathtools}
\usepackage{float}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}

% Set margins and spacing
\geometry{margin=1in}
\setlength{\parindent}{0pt}
\setlength{\parskip}{1em}

\begin{document}

\section*{P1. Root Finding Methods and Analysis}

\subsection*{(a) Selection of Initial Guess Values}

\textbf{(i) Open-end Methods (e.g., Newton-Raphson, Secant):} \\
In open-end methods, the root is approached from one side only, without a guarantee of bracketing. Therefore, the initial guess should be:
\begin{itemize}
    \item \textbf{Close to the actual root} for faster convergence.
    \item Chosen such that the \textbf{derivative is non-zero} in the case of Newton-Raphson.
    \item Based on \textbf{graphical analysis} or tabulation of function values to identify where the function changes rapidly.
\end{itemize}

\textbf{(ii) Bracketing Methods (e.g., Bisection, False Position):} \\
In these methods, the initial values are chosen to bracket the root. That is:
\begin{itemize}
    \item We choose two initial guesses, \( a \) and \( b \), such that \( f(a) \cdot f(b) < 0 \).
    \item This ensures that the function has at least one root in the interval due to the \textbf{Intermediate Value Theorem}.
    \item Graphical or tabulated values of the function can help determine suitable brackets.
\end{itemize}

\subsection*{(b) Truncation Error and Estimation in Newton-Raphson Method}

\textbf{Truncation error} occurs when an \textbf{infinite process is approximated by a finite number of steps}. In the Newton-Raphson method, it arises from neglecting higher-order terms of the Taylor series.

The Newton-Raphson formula is:
\[
x_{n+1} = x_n - \frac{f(x_n)}{f'(x_n)}
\]

Using Taylor series expansion:
\[
f(x) = f(x_n) + (x - x_n)f'(x_n) + \frac{(x - x_n)^2}{2!}f''(x_n) + \cdots
\]

When solving for \( f(x) = 0 \), the Newton-Raphson method uses only the linear term and discards the rest. The \textbf{truncation error} is:
\[
E_t = \frac{f''(\xi)}{2f'(x_n)}(x - x_n)^2, \quad \text{where } \xi \in (x_n, x)
\]

\subsection*{(c) Comparison of Convergence: Bisection vs Newton-Raphson}

\textbf{Bisection Method:}
\begin{itemize}
    \item \textbf{Order of convergence:} Linear (\(O(2^{-n})\)).
    \item Always converges if the initial interval brackets a root.
    \item Slower convergence.
    \item Independent of derivatives.
\end{itemize}

\textbf{Newton-Raphson Method:}
\begin{itemize}
    \item \textbf{Order of convergence:} Quadratic (\(O((x_n - r)^2)\)).
    \item Requires the function to be differentiable.
    \item Faster convergence near the root.
    \item May diverge if the initial guess is not close to the root or if derivative is zero.
\end{itemize}

\textbf{Conclusion:} Newton-Raphson converges faster but is less robust. Bisection is slower but guarantees convergence.

\subsection*{(d) Pseudo-code for Bisection Method}

\begin{verbatim}
Function BisectionMethod(f, a, b, tol):
    if f(a) * f(b) >= 0:
        return "Invalid interval"

    while (b - a)/2 > tol:
        c = (a + b)/2
        if f(c) == 0:
            return c
        elif f(a) * f(c) < 0:
            b = c
        else:
            a = c

    return (a + b)/2
\end{verbatim}

\textbf{Explanation:}
\begin{itemize}
    \item The function takes the function \( f \), interval \([a, b]\), and tolerance.
    \item It checks if \( f(a) \cdot f(b) < 0 \); otherwise, the root is not bracketed.
    \item It repeatedly bisects the interval and replaces the subinterval where the sign change occurs.
    \item The loop continues until the desired tolerance is achieved.
\end{itemize}
