import numpy as np

def verify_lu_factorization(A, L, U):
    """Verify LU factorization by checking if A = LU."""
    LU = np.dot(L, U)
    return np.allclose(A, LU, rtol=1e-6)

def verify_inverse(A):
    """Verify matrix inverse by checking if AA^(-1) = I."""
    A_inv = np.linalg.inv(A)
    I = np.eye(A.shape[0])
    return np.allclose(np.dot(A, A_inv), I, rtol=1e-6), A_inv

def verify_q6_lu():
    """Verify Q6 LU factorization."""
    A = np.array([
        [10, 2, -1],
        [-3, -6, 2],
        [1, 1, 5]
    ])
    L = np.array([
        [1, 0, 0],
        [-0.3, 1, 0],
        [0.1, -0.148148, 1]
    ])
    U = np.array([
        [10, 2, -1],
        [0, -5.4, 1.7],
        [0, 0, 5.351852]
    ])
    b = np.array([27, -61.5, -21.5])
    
    # Verify LU factorization
    lu_correct = verify_lu_factorization(A, L, U)
    
    # Verify solution
    x = np.linalg.solve(A, b)
    
    print("Q6 Verification:")
    print(f"LU factorization correct: {lu_correct}")
    print(f"Solution: x = {x}")
    print(f"Residual: {np.linalg.norm(np.dot(A, x) - b)}")

def verify_q8_pivoting():
    """Verify Q8 LU factorization with partial pivoting."""
    A = np.array([
        [2, -6, -1],
        [-3, -1, 7],
        [-8, 1, -2]
    ])
    b = np.array([-38, -34, -20])
    
    # Compute LU factorization with pivoting using numpy
    P, L, U = lu_decomposition_with_pivoting(A)
    
    # Verify solution
    x = np.linalg.solve(A, b)
    
    print("\nQ8 Verification:")
    print(f"Solution: x = {x}")
    print(f"Residual: {np.linalg.norm(np.dot(A, x) - b)}")

def verify_q9_inverse():
    """Verify Q9 matrix inverse calculations."""
    A = np.array([
        [10, 2, -1],
        [-3, -6, 2],
        [1, 1, 5]
    ])
    
    # Verify inverse
    is_correct, A_inv = verify_inverse(A)
    
    print("\nQ9 Verification:")
    print(f"Matrix inverse correct: {is_correct}")
    print("A^(-1) =")
    print(A_inv)

def lu_decomposition_with_pivoting(A):
    """Compute LU decomposition with partial pivoting."""
    n = len(A)
    P = np.eye(n)
    L = np.eye(n)
    U = A.copy()

    for k in range(n-1):
        # Find pivot
        pivot = np.argmax(np.abs(U[k:, k])) + k
        if pivot != k:
            # Swap rows
            U[[k, pivot]] = U[[pivot, k]]
            P[[k, pivot]] = P[[pivot, k]]
            if k > 0:
                L[[k, pivot], :k] = L[[pivot, k], :k]

        for i in range(k+1, n):
            L[i, k] = U[i, k] / U[k, k]
            U[i] = U[i] - L[i, k] * U[k]

    return P, L, U

if __name__ == "__main__":
    np.set_printoptions(precision=6, suppress=True)
    verify_q6_lu()
    verify_q8_pivoting()
    verify_q9_inverse()
