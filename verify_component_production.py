import numpy as np

def verify_component_solution():
    # Coefficient matrix
    A = np.array([
        [15, 17, 19],       # Metal
        [0.30, 0.40, 0.55], # Plastic
        [1.0, 1.2, 1.5]     # Rubber
    ])
    
    # Constants vector
    b = np.array([3890, 95, 282])
    
    # Solve using <PERSON><PERSON><PERSON>'s linear algebra solver
    solution = np.linalg.solve(A, b)
    print("Numpy Solution:", solution)
    
    # Verify our Gaussian elimination solution
    gauss_solution = np.array([11, 153.55, 53.45])
    
    # Check residuals
    residuals = np.dot(A, gauss_solution) - b
    print("\nVerification for Gaussian Elimination solution:")
    print(f"x1 = {gauss_solution[0]:.2f}, x2 = {gauss_solution[1]:.2f}, x3 = {gauss_solution[2]:.2f}")
    
    # Check constraints
    metal_used = 15*gauss_solution[0] + 17*gauss_solution[1] + 19*gauss_solution[2]
    plastic_used = 0.30*gauss_solution[0] + 0.40*gauss_solution[1] + 0.55*gauss_solution[2]
    rubber_used = 1.0*gauss_solution[0] + 1.2*gauss_solution[1] + 1.5*gauss_solution[2]
    
    print("\nConstraint Verification:")
    print(f"Metal: {metal_used:.2f}/3890g")
    print(f"Plastic: {plastic_used:.2f}/95g")
    print(f"Rubber: {rubber_used:.2f}/282g")
    
    # Verify non-negative solutions
    print("\nNon-negativity check:", all(gauss_solution >= 0))
    
    return gauss_solution

if __name__ == "__main__":
    verify_component_solution()
