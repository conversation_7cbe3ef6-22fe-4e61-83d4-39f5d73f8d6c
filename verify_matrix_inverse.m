%% Matrix Inverse Verification (Problem 9)
% This script verifies the matrix inverse computation and solution from Question 9

% Clear workspace and command window
clear;
clc;

fprintf('Matrix Inverse Verification (Problem 9)\n');
fprintf('---------------------------------------\n\n');

% Original matrix A from Problem 6
A = [10, 2, -1;
     -3, -6, 2;
     1, 1, 5];

% Our computed inverse from Problem 9
A_inv_computed = [0.110727, 0.038062, 0.006920;
                 -0.058824, -0.176471, 0.058824;
                 -0.010381, 0.027682, 0.186851];

% Right-hand side vector b
b = [27; -61.5; -21.5];

% Our solution computed using the inverse
x_computed = [0.5; 8.0; -6.0];

% 1. Check if AA⁻¹ = I
fprintf('Verification steps:\n');
fprintf('1. Check if AA⁻¹ = I\n');
fprintf('%s\n', repmat('-', 1, 40));

% Compute AA⁻¹
AA_inv = A * A_inv_computed;
I = eye(3);

fprintf('AA⁻¹ = \n');
disp(AA_inv);
fprintf('\nDifference from identity matrix:\n');
disp(abs(AA_inv - I));

% Check if AA⁻¹ is close to identity matrix
is_inverse_correct = all(all(abs(AA_inv - I) < 1e-10));
fprintf('\nInverse is correct: %d\n', is_inverse_correct);

% 2. Verify solution x = A⁻¹b
fprintf('\n2. Verify solution x = A⁻¹b\n');
fprintf('%s\n', repmat('-', 1, 40));

% Compute solution using inverse
x_using_inverse = A_inv_computed * b;
fprintf('Solution using inverse:\n');
for i = 1:3
    fprintf('x%d = %10.6f\n', i, x_using_inverse(i));
end

fprintf('\nDifference from our computed solution:\n');
diff = abs(x_using_inverse - x_computed);
for i = 1:3
    fprintf('x%d: %10.2e\n', i, diff(i));
end

% 3. Compare with MATLAB's direct solution
fprintf('\n3. Compare with MATLAB''s solution\n');
fprintf('%s\n', repmat('-', 1, 40));
x_matlab = A\b;
fprintf('%-10s %12s %12s %12s\n', 'Variable', 'Our Value', 'MATLAB', 'Difference');
fprintf('%s\n', repmat('-', 1, 46));
for i = 1:3
    fprintf('x%d:%-9s %12.6f %12.6f %12.2e\n', ...
        i, '', x_computed(i), x_matlab(i), abs(x_computed(i) - x_matlab(i)));
end

% 4. Verify original equations
fprintf('\n4. Verify original equations\n');
fprintf('%s\n', repmat('-', 1, 40));
residuals = A*x_computed - b;
fprintf('%-10s %12s %12s %12s\n', 'Equation', 'Left Side', 'Right Side', 'Residual');
fprintf('%s\n', repmat('-', 1, 46));
for i = 1:3
    left_side = A(i,:)*x_computed;
    fprintf('%d:%-9s %12.6f %12.6f %12.2e\n', ...
        i, '', left_side, b(i), residuals(i));
end
